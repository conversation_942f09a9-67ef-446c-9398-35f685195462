import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
  BelongsTo,
  HasMany,
  UpdatedAt,
  CreatedAt,
  BelongsToMany,
} from 'sequelize-typescript';
import { User } from 'src/modules/admin/authentication/schemas/user.schema';
import { Job } from 'src/modules/job/schemas/job.schema';
import { Role } from 'src/modules/roles/schemas/role.schema';
import { RequestQuotation } from 'src/modules/service/schemas/quotation-request.schema';
import { Service } from 'src/modules/service/schemas/service.schema';
import { UserSubCategories } from 'src/modules/user-management/schemas/user-categories.schema';
import { UserCategories } from 'src/modules/user-management/schemas/user-subcategories.schema';

@Table({ tableName: 'Categories' })
export class Category extends Model<Category> {
  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  name: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  roleId: number;

  @BelongsTo(() => Role, { foreignKey: 'roleId', as: 'role' })
  role: Role;

  @ForeignKey(() => Category)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  parentId: number;

  @BelongsTo(() => Category, { foreignKey: 'parentId', as: 'parent' })
  parent: Category;

  @HasMany(() => Category, { foreignKey: 'parentId', as: 'subCategories' })
  subCategories: Category[];

  @BelongsToMany(() => User, () => UserCategories)
  userCategories: User[];

  @BelongsToMany(() => User, () => UserSubCategories)
  userSubCategories: User[];

  @CreatedAt
  @Column
  createdAt: Date;

  @UpdatedAt
  @Column
  updatedAt: Date;

  @HasMany(() => Service, 'categoryId')
  servicesCategory: Service[];

  @HasMany(() => Service, 'subCategoryId')
  servicesSubcategory: Service[];

  @HasMany(() => RequestQuotation, 'categoryId')
  requestedQuotationsCategory: RequestQuotation[];

  @HasMany(() => RequestQuotation, 'subCategoryId')
  requestedQuotationSubcategory: RequestQuotation[];

  @HasMany(() => Job, 'roleSubcategoryId')
  job: Job[];
}
