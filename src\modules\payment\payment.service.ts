/* eslint-disable prettier/prettier */
import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import axios from 'axios';
import * as crypto from 'crypto';
import { ConfigService } from '@nestjs/config';
import { SubscriptionStatus } from 'src/common/utils/enum';
import { InjectModel } from '@nestjs/sequelize';

import {
  Transaction,
  TransactionStatus,
} from '../user-subscription/schemas/transaction.schema';
import {
  Order,
  OrderStatus,
  PaymentStatus,
} from '../user-subscription/schemas/order.schema';
import {
  SubscriptionType,
  UserSubscription,
} from '../user-subscription/schemas/user-subscription.schema';
import { Package } from '../subscription/schemas/package.schema';
import { PackageDetails } from '../subscription/schemas/packagePermission.schema';
import { I18nService } from 'nestjs-i18n';
import { User } from '../admin/authentication/schemas/user.schema';
import { SendgridService } from '../sendgrid/sendgrid.service';
import { Email } from 'src/common/email-template';
import { notificationTitle, notificationType } from 'src/common/messages';
import { NotificationService } from '../notification/notification.service';
import { Constants, TEMPLATE_IDS } from 'src/common/constants';
import { AccountSettings } from '../user-management/schemas/account-settings.schema';
import { NotificationPreference } from '../user-management/schemas/notification-preferences.schema';
import { SmsService } from '../sms/sms.service';

@Injectable()
export class PaymentService {
  private readonly merchantId: string;
  private readonly merchantKey: string;
  private readonly baseUrl: string;
  private readonly payUrl: string;
  private readonly statusUrl: string;
  private readonly frontendBaseUrl: string;
  private readonly redirectPage: string;

  constructor(
    @InjectModel(Transaction)
    private transactionModel: typeof Transaction,
    @InjectModel(Order)
    private orderModel: typeof Order,
    @InjectModel(UserSubscription)
    private userSubscriptionModel: typeof UserSubscription,
    private readonly configService: ConfigService,
    private readonly i18n: I18nService,
    @InjectModel(User)
    private userModel: typeof User,
    private readonly sendgridService: SendgridService,
    private readonly smsService: SmsService,
    private readonly notificationService: NotificationService,
  ) {
    this.merchantId = this.configService.get<string>('PAYMENT_MERCHANT_ID');
    this.merchantKey = this.configService.get<string>('PAYMENT_MERCHANT_KEY');
    this.payUrl = this.configService.get<string>('PAYMENT_BASE_URL');
    this.statusUrl = this.configService.get<string>('PAYMENT_STATUS_URL');
    this.frontendBaseUrl = this.configService.get<string>('FRONTEND_BASE_URL');
    this.redirectPage = this.configService.get<string>('REDIRECT_PAGE');
    this.baseUrl = this.configService.get('PAYMENT_BASE_URL') + '/pg/v1/pay';
  }

  async initiatePayment(dto: any) {
    try {
      // console.log('dto payment:::', dto);
      const { amount, name, uniqueId, userLanguage } = dto;
      const transaction = await this.transactionModel.create({
        trackingId: uniqueId,
        orderId: dto.orderId,
        totalAmount: amount,
        status: TransactionStatus.PENDING,
      });
      // console.log('transaction', transaction)

      // Safely convert totalAmount to a number
      const totalAmountNum = Number(transaction.totalAmount);

      // Fix to 2 decimals and convert to paise
      const fixedAmount = Math.round(totalAmountNum * 100);

      // Prepare the payload
      const data = {
        merchantId: this.merchantId,
        merchantTransactionId: uniqueId,
        name,
        amount: fixedAmount,
        redirectUrl: `${this.frontendBaseUrl}/${userLanguage}/${this.redirectPage}/${uniqueId}`,
        paymentInstrument: {
          type: 'PAY_PAGE',
        },
      };
      // console.log('transaction data:::::::', data);
      // Encode payload as Base64
      // const payload = JSON.stringify(data);
      const payloadMain = Buffer.from(JSON.stringify(data)).toString('base64');

      // Generate checksum
      const keyIndex = 1;
      const stringToHash = payloadMain + '/pg/v1/pay' + this.merchantKey;
      const sha256 = crypto
        .createHash('sha256')
        .update(stringToHash)
        .digest('hex');
      const checksum = sha256 + '###' + keyIndex;

      // Define request options
      const options = {
        method: 'POST',
        url: this.payUrl,
        headers: {
          accept: 'application/json',
          'Content-Type': 'application/json',
          'X-VERIFY': checksum,
        },
        data: {
          request: payloadMain,
        },
      };

      const response = await axios(options);
      // console.log('response::::::', response.data.data);
      return response.data.data;
    } catch (error) {
      // console.error('Error  payment:', error);
      console.error('Error initiating payment:', error.message);

      // Update transaction status to "Failed"
      await this.transactionModel.update(
        {
          status: TransactionStatus.FAILED,
        },
        { where: { trackingId: dto.uniqueId } },
      );
      console.error('Error initiating payment:', error.message);
      throw new HttpException(
        error.message || 'Payment initiation failed',
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async checkStatus(trackingId: string) {
    try {
      console.log('checkStatus.....');
      // Prepare the string for checksum
      const merchantTransactionId = trackingId;
      const keyIndex = 1;
      const stringToHash =
        `/pg/v1/status/${this.merchantId}/${merchantTransactionId}` +
        this.merchantKey;
      // console.log('stringToHash:::::', stringToHash);
      const sha256 = crypto
        .createHash('sha256')
        .update(stringToHash)
        .digest('hex');
      const checksum = sha256 + '###' + keyIndex;

      // Define request options
      const options = {
        method: 'GET',
        url: `${this.statusUrl}/${this.merchantId}/${trackingId}`,
        headers: {
          accept: 'application/json',
          'Content-Type': 'application/json',
          'X-VERIFY': checksum,
          'X-MERCHANT-ID': this.merchantId,
        },
      };
      // console.log('options:::::', options);

      // Make the API call
      const response = await axios.request(options);

      // console.log('Payment status response:', response.data.data);
      if (response.data.success) {
        await this.orderModel.update(
          {
            status: OrderStatus.COMPLETED,
            PaymentStatus: PaymentStatus.PAID,
          },
          {
            where: {
              uniqueId: trackingId,
            },
          },
        );
        // Update the transaction status in the database
        await this.transactionModel.update(
          {
            status: TransactionStatus.SUCCESS,
            transactionData: response.data.data,
          },
          { where: { trackingId: merchantTransactionId } },
        );
        const transaction = await this.transactionModel.findOne({
          where: { trackingId },
          include: [
            {
              model: Order,
              as: 'order',
              where: {
                uniqueId: trackingId,
                status: OrderStatus.COMPLETED,
              },
            },
          ],
        });
        // console.log('transaction::::', transaction.order.userId);

        const existingSubscription = await this.userSubscriptionModel.findOne({
          where: {
            userId: transaction.order.userId,
            status: SubscriptionStatus.ACTIVE,
          },
        });

        if (existingSubscription) {
          await existingSubscription.update({
            status: SubscriptionStatus.INACTIVE,
          });
        }

        const subscriptionData = {
          userId: transaction.order.userId,
          subscriptionType: SubscriptionType.SINGLE,
          packageType: transaction.order.packageType,
          validFrom: new Date(),
          status: SubscriptionStatus.ACTIVE,
          validTill: this.calculateValidTillDate(transaction.order.packageType),
          packageId: transaction.order.packageId,
          totalAmount: transaction.order.totalAmount,
          // discountAmount: order.discountAmount,
          transactionId: transaction.id,
        };

        const createSubs =
          await this.userSubscriptionModel.create(subscriptionData);
        // console.log('createSubs::::', response.data, createSubs);
        const userDetails = await this.userModel.findByPk(
          transaction.order.userId,
          {
            include: [
              {
                model: AccountSettings,
                as: 'accountSettings',
              },
              {
                model: NotificationPreference,
                as: 'notificationPreference',
              },
            ],
          },
        );
        const userSubs = await this.userSubscriptionModel.findOne({
          where: {
            id: createSubs.id,
          },
          include: [
            {
              model: Package,
              as: 'package',
              include: [
                {
                  model: PackageDetails,
                  as: 'packageDetails',
                },
              ],
            },
          ],
        });
        const formattedDate = userSubs.validTill.toISOString().split('T')[0];
        const data = {
          title: notificationTitle.SUBSCRIPTION_ACTIVATED,
          type: notificationType.SUBSCRIPTION_ACTIVATED,
          receiverId: userDetails.id,
          prefrence: 'inAppNotifications',
          packageTitle: userSubs.package.title,
          validtill: formattedDate,
        };
        // console.log('data::::::', data);

        console.log(
          'transaction.order.totalAmount------------>',
          transaction.order.totalAmount,
        );

        await this.notificationService.sendNotification(data);

        if (userDetails.email) {
          await this.sendgridService.sendEmail(
            userDetails.email,
            'Payment Successful',
            Email.getTemplate({
              title: 'Payment Successful',
              userName: `${userDetails?.firstName} ${userDetails?.lastName}`,
              children: `
            <p>
             Your payment of ${transaction.order.totalAmount} was successful. Thank you!
          </p>
            `,
            }),
          );
        }
        if (userDetails.email) {
          await this.sendgridService.sendEmail(
            userDetails.email,
            'Subscription Activated',
            Email.getTemplate({
              title: Constants.Titles.SubscriptionActivated,
              userName: `${userDetails?.firstName} ${userDetails?.lastName}`,
              children: `
                <p>
               Your subscription is now active. Enjoy ${userSubs.package.title} features!
              </p>
                `,
            }),
          );
        }

        console.log(
          'userDetails.phoneNumber------------>',
          userDetails.phoneNumber,
        );
        console.log(
          'userDetails.notificationPreference.sms------------>',
          userDetails.notificationPreference.sms,
        );

        if (userDetails.phoneNumber && userDetails.notificationPreference.sms) {
          const templateId = TEMPLATE_IDS.Payment_successful;
          const phone = userDetails.phoneNumber;

          const payload = {
            phone,
            templateId,
            var1: `${userDetails.firstName} ${userDetails.lastName}`.trim(),
            var2: `${transaction.order.totalAmount}`,
          };
          await this.smsService.triggerSMS(payload, false);
        }
        return {
          details: response.data,
          usersubscription: userSubs,
        };
      } else {
        // Update the transaction status to "Failed"
        await this.orderModel.update(
          {
            status: OrderStatus.CANCELLED,
            PaymentStatus: PaymentStatus.UNPAID,
          },
          {
            where: {
              uniqueId: trackingId,
            },
          },
        );
        await this.transactionModel.update(
          { status: TransactionStatus.FAILED },
          { where: { trackingId: merchantTransactionId } },
        );

        return {
          status: 'Failed',
          details: response.data,
        };
      }
    } catch (error) {
      console.error('Error checking payment status:', error.message);
      throw new HttpException(
        `Failed to check payment status: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  calculateValidTillDate(packageType: string): Date {
    const date = new Date();
    switch (packageType) {
      case 'yearly':
        date.setFullYear(date.getFullYear() + 1);
        break;
      case 'monthly':
        date.setMonth(date.getMonth() + 1);
        break;
      case 'quarterly':
        date.setMonth(date.getMonth() + 3);
        break;
      default:
        throw new Error('Invalid package type');
    }
    return date;
  }
}
