import {
  BadRequestException,
  ConflictException,
  HttpStatus,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { User } from '../admin/authentication/schemas/user.schema';
import { Op } from 'sequelize';
// import { Permission } from '../sub-admin/permission.scheman.schema';
import {
  ERROR_MESSAGES,
  notificationMessage,
  notificationTitle,
  notificationType,
} from 'src/common/messages';
import {
  RegisterUserDTO,
  UpdateUserDTO,
  VerifyOtpDTO,
  SendOtpDto,
  LoginOtpDto,
} from './dto/registerUser.input.dto';
import { CustomException } from 'src/common/custom.exception';
import { CommonService } from 'src/common/common.service';
import { Connection } from '../connection/schemas/connection.schema';
import { Role } from '../roles/schemas/role.schema';
import { UserRole } from '../roles/schemas/usersRole.schema';
import { Constants, TEMPLATE_IDS } from 'src/common/constants';
import { Skill } from '../skill/schemas/skill.schema';
import { Job } from '../job/schemas/job.schema';
import { Category } from '../ category/schemas/category.schema';
import sequelize from 'sequelize';
import { AccountSettings } from './schemas/account-settings.schema';
import { NotificationPreference } from './schemas/notification-preferences.schema';
import { RequestQuotation } from '../service/schemas/quotation-request.schema';
import { UserCategories } from './schemas/user-subcategories.schema';
import { UserSubCategories } from './schemas/user-categories.schema';
import { NotificationService } from '../notification/notification.service';
import { I18nContext, I18nService } from 'nestjs-i18n';
import { Service, ServiceStatus } from '../service/schemas/service.schema';
import { Sequelize } from 'sequelize-typescript';
import { Tags } from '../service/schemas/tags.schema';
import {
  SubscriptionType,
  UserSubscription,
} from '../user-subscription/schemas/user-subscription.schema';
import {
  Package,
  PackageStatus,
  PackageType,
} from '../subscription/schemas/package.schema';
import { PackageDetails } from '../subscription/schemas/packagePermission.schema';
import { SubscriptionStatus } from 'src/common/utils/enum';
import { calculateValidTillDate, generateOTP } from 'src/common/utils/helper';
import { SendgridService } from '../sendgrid/sendgrid.service';
import { SmsService } from '../sms/sms.service';
import { Email } from 'src/common/email-template';
import { Cron, CronExpression } from '@nestjs/schedule';
import { UserJobs } from '../user-jobs/schemas/user-jobs.schema';

@Injectable()
export class UserManagementService {
  constructor(
    @InjectModel(Connection)
    private connectionModel: typeof Connection,
    @InjectModel(Job)
    private jobModel: typeof Job,
    @InjectModel(Service)
    private serviceModel: typeof Service,
    @InjectModel(AccountSettings)
    private accountSettingsModel: typeof AccountSettings,
    @InjectModel(NotificationPreference)
    private notificationPreferenceModel: typeof NotificationPreference,
    @InjectModel(User)
    private userRepository: typeof User,
    @InjectModel(UserCategories)
    private userCategoriesRepository: typeof UserCategories,
    @InjectModel(UserSubCategories)
    private UserSubCategoriesRepository: typeof UserSubCategories,
    @InjectModel(UserSubscription)
    private userSubscriptionRepository: typeof UserSubscription,
    @InjectModel(Package)
    private packageRepository: typeof Package,
    @InjectModel(Role)
    private roleRepository: typeof Role,
    @InjectModel(UserRole)
    private userRoleRepository: typeof UserRole,
    private readonly authService: CommonService,
    private readonly notificationService: NotificationService,
    private readonly i18n: I18nService,
    private readonly sendgridService: SendgridService,
    private readonly smsService: SmsService,
  ) {}

  @Cron(CronExpression.EVERY_DAY_AT_NOON)
  async handleJobPostingCheck() {
    const today = new Date();
    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(today.getMonth() - 1); // Calculate the date 1 month ago

    const users = await this.userRepository.findAll({
      where: {
        isVerified: true,
        isActive: true,
      },
      include: [
        {
          model: UserJobs,
          as: 'userJobs',
          required: false,
          where: {
            createdAt: {
              [Op.gte]: oneMonthAgo,
            },
          },
        },
        {
          model: NotificationPreference,
          as: 'notificationPreference',
          required: false,
        },
        {
          model: Role,
          as: 'roles', // Alias for the User-Role relationship
          required: true,
          attributes: ['id', 'roleName'], // Fetch only required fields
          where: {
            roleName: {
              [Op.in]: ['buyer', 'seller', 'contractor', 'architect', 'labour'], // Filter specific roles
            },
          },
          through: { attributes: [] }, // Assuming a UserRoles junction table
        },
      ],
      having: Sequelize.literal('COUNT("userJobs"."userId") = 0'),
      group: [
        'User.id',
        'userJobs.id',
        'notificationPreference.id',
        'roles.id',
        'roles.roleName',
      ],
    });

    for (const user of users) {
      if (user && user.notificationPreference?.inAppNotifications) {
        const data = {
          type: notificationType.JOB_POST,
          receiverId: user.id,
          prefrence: 'inAppNotifications',
        };
        await this.notificationService.sendNotification(data);
      }
    }
  }

  async associateSkills(skills = [], user: any) {
    await user.setSkills(skills);
  }
  async associateTags(Tags = [], user: any) {
    await user.setTags(Tags);
  }

  async associateCategory(categories: number[] = [], user: any) {
    try {
      const existingCategories = await user.getCategories();
      // console.log('existingCategories:::::', existingCategories);
      const existingServices = await Service.findAll({
        attributes: ['id', 'title', 'categoryId'],
        where: {
          status: ServiceStatus.ACTIVE,
          userId: user.id,
          categoryId: existingCategories.map((cat: any) => cat.id),
        },
        include: [
          {
            model: Category,
            as: 'category',
            attributes: ['id', 'name'],
          },
        ],
      });

      if (existingServices.length > 0) {
        // console.log('existingServices:::::', existingServices);
        const servicesWithCategory = existingServices.map(
          (service) => service.categoryId,
        );

        const missingCategoryIds = servicesWithCategory.filter(
          (categoryId) => !categories.includes(categoryId),
        );
        if (missingCategoryIds.length > 0) {
          const conflictingDetails = existingServices
            .filter((service) =>
              missingCategoryIds.includes(service.categoryId),
            )
            .map(
              (service) =>
                `Service Name: "${service.title}" (Category: ${service.category.name})`,
            )
            .join(', ');

          throw new Error(
            `Service exist with categories: ${conflictingDetails}`,
          );
        }
      }

      if (existingCategories.length > 0) {
        await user.removeCategories(existingCategories);
      }

      await user.setCategories(categories);
    } catch (error) {
      throw new Error(`${error.message}`);
    }
  }

  async associateSubCategory(subCategories: number[] = [], user: any) {
    try {
      const existingSubCategories = await user.getSubCategories();

      const existingServices = await Service.findAll({
        attributes: ['id', 'title', 'subCategoryId'],
        where: {
          status: ServiceStatus.ACTIVE,
          userId: user.id,
          subCategoryId: existingSubCategories.map((sub: any) => sub.id),
        },
        include: [
          {
            model: Category,
            as: 'subCategory',
            attributes: ['id', 'name'],
          },
        ],
      });

      if (existingServices.length > 0) {
        const servicesWithSubCategory = existingServices.map(
          (service) => service.subCategoryId,
        );

        const missingSubCategoryIds = servicesWithSubCategory.filter(
          (subCategoryId) => !subCategories.includes(subCategoryId),
        );

        if (missingSubCategoryIds.length > 0) {
          const conflictingDetails = existingServices
            .filter((service) =>
              missingSubCategoryIds.includes(service.subCategoryId),
            )
            .map(
              (service) =>
                `Service Name: "${service.title}" (subCategory: ${service.subCategory.name})`,
            )
            .join(', ');

          throw new Error(
            `Service exist with subCategories: ${conflictingDetails}`,
          );
        }
      }

      if (existingSubCategories.length > 0) {
        await user.removeSubCategories(existingSubCategories);
      }

      await user.setSubCategories(subCategories);
    } catch (error) {
      throw new Error(`${error.message}`);
    }
  }
  async deleteCategory(userId: number) {
    await this.userCategoriesRepository.destroy({
      where: {
        userId: userId,
      },
    });
  }

  async deleteSubCategory(userId: number) {
    await this.UserSubCategoriesRepository.destroy({
      where: {
        userId: userId,
      },
    });
  }
  async listUsers(
    limit: number,
    offset: number,
    filters?: any,
    userGeolocation?: any,
  ): Promise<any> {
    const rolesToInclude = [
      'labour',
      'seller',
      'buyer',
      'contractor',
      'architect',
    ];
    const user = await this.userRepository.findOne({
      where: { id: filters?.loggedInUser },
      include: [
        {
          model: UserSubscription,
          as: 'subscription',
          include: [
            {
              model: Package,
              as: 'package',
              include: [
                {
                  model: PackageDetails,
                  as: 'packageDetails',
                },
              ],
            },
          ],
        },
      ],
    });

    const userRadius =
      user.subscription.package.packageDetails.dataValues.radiusLimit;

    const where: any = {
      isDeleted: false,
    };
    if (filters?.search) {
      where[Op.or] = [
        { firstName: { [Op.iLike]: `%${filters.search}%` } },
        { lastName: { [Op.iLike]: `%${filters.search}%` } },
        sequelize.where(
          sequelize.fn(
            'concat',
            sequelize.col('firstName'),
            ' ',
            sequelize.col('lastName'),
          ),
          {
            [Op.iLike]: `%${filters?.search}%`,
          },
        ),
        Sequelize.literal(`
  EXISTS (
    SELECT 1
    FROM "UserTags" AS ut
    JOIN "Tags" AS t ON ut."tagId" = t."id"
    WHERE ut."userId" = "User"."id"
    AND t."name" ILIKE '%${filters.search}%'
  )
`),
        Sequelize.literal(`
  EXISTS (
    SELECT 1
    FROM "UserCategories" AS uc
    JOIN "Categories" AS c ON uc."categoryId" = c."id"
    WHERE uc."userId" = "User"."id"
    AND c."name" ILIKE '%${filters.search}%'
  )
`),
        Sequelize.literal(`
  EXISTS (
    SELECT 1
    FROM "UserSubCategories" AS usc
    JOIN "Categories" AS c ON usc."categoryId" = c."id"
    WHERE usc."userId" = "User"."id"
    AND c."name" ILIKE '%${filters.search}%'
  )
`),
      ];
    }
    const roleWhere: any = {};
    if (filters?.role) {
      roleWhere.roleName = {
        [Op.iLike]: `%${filters.role}%`,
      };
    } else {
      roleWhere.roleName = {
        [Op.in]: rolesToInclude,
      };
    }
    if (filters?.loggedInUser) {
      where.id = {
        [Op.and]: [
          { [Op.ne]: filters?.loggedInUser },
          {
            [Op.notIn]: Sequelize.literal(`
              (SELECT "blockedId" FROM "UserBlocks" WHERE "blockerId" = ${filters?.loggedInUser})
            `),
          },
          {
            [Op.notIn]: Sequelize.literal(`
              (SELECT "blockerId" FROM "UserBlocks" WHERE "blockedId" = ${filters?.loggedInUser})
            `),
          },
        ],
      };
    }
    let latitude: number, longitude: number, distance: number;
    if (filters?.geolocation && filters?.radius) {
      latitude = filters?.geolocation[1];
      longitude = filters?.geolocation[0];
      distance = filters?.radius * 1000;
    } else if (filters?.geolocation) {
      latitude = filters?.geolocation[1];
      longitude = filters?.geolocation[0];
      distance = userRadius * 1000;
    } else if (filters?.radius && userGeolocation) {
      latitude = userGeolocation.coordinates[1];
      longitude = userGeolocation.coordinates[0];
      distance = filters?.radius * 1000;
    } else if (userGeolocation) {
      latitude = userGeolocation.coordinates[1];
      longitude = userGeolocation.coordinates[0];
      distance = userRadius * 1000;
    } else {
      throw new CustomException(
        ERROR_MESSAGES.UPDATE_LOCATION,
        HttpStatus.BAD_REQUEST,
      );
    }
    where[Op.and] = [
      ...(where[Op.and] || []),
      Sequelize.where(
        Sequelize.literal(`
      ST_DWithin(
        "User"."geolocation"::geography,
        ST_SetSRID(ST_MakePoint(${longitude}, ${latitude}), 4326)::geography,
        ${distance}
      )
    `),
        true,
      ),
    ];
    if (filters?.startDate || filters?.endDate) {
      const dateCondition = sequelize.where(
        sequelize.fn('date', sequelize.col('User.createdAt')),
        {
          ...(filters?.startDate && { [Op.gte]: filters.startDate }),
          ...(filters?.endDate && { [Op.lte]: filters.endDate }),
        },
      );
      where[Op.and].push(dateCondition);
    }
    const result = await this.userRepository.findAndCountAll({
      where,
      include: [
        {
          model: Tags,
          as: 'tags',
          through: { attributes: [] },
          required: false,
        },
        {
          model: Role,
          as: 'roles',
          where: roleWhere,
          through: { attributes: [] },
        },
        {
          model: Category,
          through: {
            attributes: [],
          },
          as: 'categories',
          attributes: ['id', 'name'],
        },
        {
          model: Category,
          through: {
            attributes: [],
          },
          as: 'subCategories',
          attributes: ['id', 'name', 'parentId'],
        },
        {
          model: UserSubscription,
          as: 'subscription',
          where: {
            status: {
              [Op.in]: [SubscriptionStatus.ACTIVE],
            },
          },
          required: false,
          include: [
            {
              model: Package,
              as: 'package',
              include: [
                {
                  model: PackageDetails,
                  as: 'packageDetails',
                },
              ],
            },
          ],
        },
      ],
      attributes: {
        exclude: [
          'password',
          'address1',
          'address2',
          'otp',
          'verifyOTP',
          'updatedAt',
        ],
        include: [
          [
            sequelize.literal(`(
              SELECT CAST(ROUND(AVG(rating) * 2) / 2 AS DECIMAL(2,1))
              FROM "ReviewRatings" AS rr
              WHERE rr."revieweeId" = "User"."id"
            )`),
            'averageRating',
          ],
        ],
      },
      limit,
      offset,
      order: [
        [
          Sequelize.literal(`
      ST_Distance(
        "User"."geolocation"::geography,
        ST_SetSRID(ST_MakePoint(${longitude}, ${latitude}), 4326)::geography
      )
    `),
          'DESC',
        ],
      ],
      distinct: true,
      logging: true,
    });
    const sortedUsers = await this.sortUsersBySubscription(result.rows);
    const { count } = result;
    return { data: sortedUsers, totalCount: count };
  }

  async listSellers(
    limit: number,
    offset: number,
    filters?: any,
  ): Promise<any> {
    const rolesToInclude = ['seller'];

    const where: any = { ...filters, isDeleted: false };
    const roleWhere: any = {
      roleName: {
        [Op.in]: rolesToInclude,
      },
    };
    const result = await this.userRepository.findAndCountAll({
      where,
      include: [
        {
          model: Role,
          as: 'roles',
          where: roleWhere,
          through: { attributes: [] },
        },
      ],
      attributes: {
        include: [
          [
            sequelize.literal(`(
              SELECT CAST(ROUND(AVG(rating) * 2) / 2 AS DECIMAL(2,1))
              FROM "ReviewRatings" AS rr
              WHERE rr."revieweeId" = "User"."id"
            )`),
            'averageRating',
          ],
        ],
        exclude: [
          'password',
          'address1',
          'address2',
          'otp',
          'verifyOTP',
          'updatedAt',
        ],
      },
      limit,
      offset,
      order: [['createdAt', 'DESC']],
      distinct: true,
    });

    const { rows, count } = result;
    return { data: rows, totalCount: count };
  }

  async blockOrUnblockUser(userId: number): Promise<{ message: string }> {
    const user = await this.userRepository.findByPk(userId);
    if (!user) throw new NotFoundException(ERROR_MESSAGES.USER_NOT_FOUND);
    user.isActive = !user.isActive;
    await user.save();
    return {
      message: `User ${user.isActive ? 'Unblocked' : 'Blocked'} successfully`,
    };
  }

  async deleteUser(user: User): Promise<{ message: string }> {
    user.isDeleted = true;
    await user.save();
    return { message: `User deleted successfully` };
  }

  async getUser(loggedInUserId: number, reciverId: number): Promise<User> {
    const attributes: any = [
      'id',
      'firstName',
      'lastName',
      'email',
      'gender',
      'profilePicture',
      'dateOfBirth',
      'phoneNumber',
      'secondaryPhoneNumber',
      'firmName',
      'officeNumber',
      'gstIn',
      'address1',
      'address2',
      'state',
      'city',
      'pincode',
      'ratePerDay',
      'workingDays',
      'openingTime',
      'closingTime',
      'isActive',
      'geolocation',
      'website',
      'bio',
      'type',
      [
        sequelize.literal(`(
          CASE WHEN EXISTS (
            SELECT 1
            FROM "connections"
            WHERE (
              ("requester_id" = ${loggedInUserId}  AND "receiver_id" = "User"."id") OR
              ("receiver_id" = ${loggedInUserId}  AND "requester_id" = "User"."id")
            )
            AND "status" = 'accepted'
          ) THEN true ELSE false END
        )`),
        'isInConnections',
      ],
      [
        sequelize.literal(`(
        SELECT CAST(ROUND(AVG(rating) * 2) / 2 AS DECIMAL(2,1))
        FROM "ReviewRatings" AS rr
        WHERE rr."revieweeId" = "User"."id"
      )`),
        'averageRating',
      ],
      [
        sequelize.literal(`(
          CASE WHEN EXISTS (
            SELECT 1
            FROM "ReviewRatings"
            WHERE
              "revieweeId" = ${loggedInUserId} AND
              "reviewerId" = ${reciverId}
          ) THEN true ELSE false END
        )`),
        'isReviewRequested',
      ],
      [
        sequelize.literal(`(
          CASE WHEN EXISTS (
            SELECT 1
            FROM "ReviewRatings"
            WHERE
              "revieweeId" = ${loggedInUserId} AND
              "reviewerId" = ${reciverId}
               AND "ReviewRatings"."status" = 'active'
          ) THEN true ELSE false END
        )`),
        'isReviewed',
      ],
    ];
    const user = await this.userRepository.findByPk(reciverId, {
      attributes: attributes,
      include: [
        {
          model: Skill,
          as: 'skills',
          through: { attributes: [] },
          attributes: ['id', 'name'],
          required: false,
        },
        {
          model: Tags,
          as: 'tags',
          through: { attributes: [] },
          required: false,
        },
        {
          model: Role,
          as: 'roles',
          through: { attributes: [] },
          attributes: ['id', 'roleName'],
          required: false,
        },
        {
          model: Category,
          through: {
            attributes: [],
          },
          as: 'categories',
          attributes: [
            'id',
            'name',
            'parentId',
            'roleId',
            'createdAt',
            [
              Sequelize.literal(
                `(SELECT COUNT(*) FROM "Categories" AS subcategory WHERE subcategory."parentId" = "categories"."id") > 0`,
              ),
              'subCategoryExist',
            ],
          ],
        },
        {
          model: Category,
          through: {
            attributes: [],
          },
          as: 'subCategories',
          attributes: ['id', 'name', 'parentId'],
        },
        {
          model: RequestQuotation,
          as: 'receivedQuotations',
          attributes: ['id'], // You can include more fields if needed
          where: {
            userId: loggedInUserId,
            requestedTo: reciverId,
            serviceId: null,
          },
          required: false,
        },
        {
          model: NotificationPreference,
          as: 'notificationPreference',
          required: false,
        },
        {
          model: AccountSettings,
          as: 'accountSettings',
          required: false,
        },
      ],
    });
    if (!user) {
      throw new NotFoundException(ERROR_MESSAGES.USER_NOT_FOUND);
    }

    const resp: any = user.toJSON();
    if (resp?.receivedQuotations?.length > 0) {
      resp.hasRequestedQuotation = true;
    } else {
      resp.hasRequestedQuotation = false;
    }
    return resp;
  }
  async getUserCategory(userId: number): Promise<User> {
    return this.userRepository.findByPk(userId, {
      attributes: ['id', 'firstName', 'lastName'],
      include: [
        {
          model: Category,
          through: {
            attributes: [],
          },
          as: 'categories',
          attributes: ['id', 'name'],
        },
        {
          model: Category,
          through: {
            attributes: [],
          },
          as: 'subCategories',
          attributes: ['id', 'name', 'parentId'],
        },
      ],
    });
  }
  async getUserAccountSttings(userId: number): Promise<User> {
    return this.userRepository.findByPk(userId, {
      attributes: ['id'],
      include: [
        {
          model: AccountSettings,
          as: 'accountSettings',
        },
        {
          model: NotificationPreference,
          as: 'notificationPreference',
        },
      ],
    });
  }
  async updateUserAccountSettings(
    userId: number,
    data: any,
    registration: boolean,
  ) {
    let accountSettings = await this.accountSettingsModel.findOne({
      where: { userId },
    });
    if (!accountSettings) {
      accountSettings = await this.accountSettingsModel.create({
        ...data.accountSettings,
        userId: userId,
      });
    } else await accountSettings.update(data.accountSettings);
    let notificationPreference = await this.notificationPreferenceModel.findOne(
      { where: { userId } },
    );
    if (!notificationPreference) {
      notificationPreference = await this.notificationPreferenceModel.create({
        ...data.notificationPreference,
        userId: userId,
      });
    } else await notificationPreference.update(data.notificationPreferences);

    if (!registration) {
      const data = {
        type: notificationType.UPDATE_NOTIFICATION_PREFERENCE,
        receiverId: userId,
        prefrence: 'inAppNotifications',
      };
      await this.notificationService.sendNotification(data);
    }
    const res = {
      id: userId,
      accountSettings,
      notificationPreference,
    };
    return res;
  }

  async findByEmailOrPhone(emailOrPhone: string): Promise<User> {
    return this.userRepository.findOne({
      where: {
        [Op.or]: [{ email: emailOrPhone }, { phoneNumber: emailOrPhone }],
      },
    });
  }
  async validateUser(identifier: string, pass: string): Promise<any> {
    const user = await this.findByEmailOrPhone(identifier);
    if (user && user.password === pass) {
      const { ...result } = user;
      return result;
    }
    return null;
  }

  async updateUserAssociations(
    userId: number,
    roleId?: number,
    categoryId?: number[],
    subCategoryId?: number[],
  ) {
    // const user = await this.userRepository.findOne({ where: { id: userId } });

    // if (!user) {
    //   throw new Error('User not found');
    // }
    if (roleId) {
      const userRole = await this.userRoleRepository.findOne({
        where: { userId: userId },
      });
      if (userRole) {
        await this.userRoleRepository.destroy({
          where: { userId: userId },
        });
      }
    }
    if (categoryId && categoryId.length) {
      await this.deleteCategory(userId);
    }
    if (subCategoryId && subCategoryId.length) {
      await this.deleteSubCategory(userId);
    }
    console.log('User associations deleted successfully');
  }

  registerUser = async (dto: RegisterUserDTO) => {
    try {
      const { identifier } = dto;

      const user = await this.userRepository.findOne({
        where: {
          isDeleted: false,
          isActive: true,
          // isVerified: true,
          [Op.or]: [{ email: identifier }, { phoneNumber: identifier }],
        },
      });
      if (user) {
        if (user.isVerified) {
          const message = this.i18n.t(
            'test.errorMessage.USER_ALREADY_VERIFIED',
            {
              lang: I18nContext.current().lang,
            },
          );
          throw new ConflictException(message);
          // throw new ConflictException('User already exists and is verified.');
        }
        await this.updateUserAssociations(
          user.id,
          dto.roleId,
          dto.category,
          dto.subCategory,
        );
        const otp = generateOTP(identifier);
        (user.firstName = dto.firstName),
          (user.lastName = dto.lastName),
          (user.otp = otp),
          (user.isActive = true),
          (user.type = dto.type),
          console.log('updated user::::::::::', user);

        if (identifier.includes('@')) {
          user.email = identifier.toLowerCase();
        } else if (/^\d{10}$/.test(identifier)) {
          user.phoneNumber = identifier;
        } else {
          const message = this.i18n.t('test.errorMessage.INVALID_IDENTIFIER', {
            lang: I18nContext.current().lang,
          });
          throw new Error(message);
        }
        if (user.email) {
          await this.sendgridService.sendEmail(
            user.email,
            'Verify Your Account with OTP',
            Email.getTemplate({
              title: Constants.Titles.VerificationOTP,
              userName: `${user.firstName} ${user.lastName}`,
              children: `
          <p>
          For your account's security, please verify it using the one-time
          password (OTP) below:
        </p>
        <div class="otp">${otp}</div>
      <p>
          This OTP is valid for a limited time and can only be used once.
          <br />
          If you didn’t request this, contact our
          <a href="mailto:<EMAIL>">support team</a> immediately.
        </p>
        <p>Thank you for helping us keep your account secure.</p>

          `,
            }),
          );
        }
        if (dto.identifier === user.phoneNumber) {
          const templateId = TEMPLATE_IDS.Otp_Sms;
          const phone = user.phoneNumber;

          const payload = {
            phone,
            templateId,
            var1: user.otp,
          };
          await this.smsService.triggerSMS(payload, true);
        }
        await user.save();

        if (dto.roleId) {
          const roleExist = await this.roleRepository.findOne({
            where: { id: dto.roleId },
          });
          if (!roleExist) {
            const message = this.i18n.t('test.errorMessage.ROLE_NOT_EXIST', {
              lang: I18nContext.current().lang,
            });
            throw new Error(message);
          }
          await this.userRoleRepository.create({
            userId: user.id,
            roleId: roleExist.id,
          });
        }
        const { id, firstName, lastName, email, phoneNumber } = user;
        if (dto.category && dto.category.length) {
          await this.associateCategory(dto.category, user);
        }
        if (dto.subCategory && dto.subCategory.length) {
          await this.associateSubCategory(dto.subCategory, user);
        }
        const settingsData = {
          accountSettings: {
            appLanguage: dto.appLanguage,
            communicationLanguage: dto.appLanguage,
            profileVisibility: 'public',
          },
          notificationPreference: {
            jobAlerts: true,
            newBidRequest: true,
            newQuotation: true,
            inAppNotifications: true,
            sms: true,
            email: true,
          },
        };

        await this.updateUserAccountSettings(user.id, settingsData, true);
        const value = {
          type: notificationType.REGISTRATION_SUCCESSFUL,
          title: notificationTitle.REGISTRATION_SUCCESSFUL,
          message: notificationMessage.REGISTRATION_SUCCESSFUL,
        };
        await this.notificationService.sendNotification(value);
        const message = this.i18n.t('test.successMessage.OTP_SENT', {
          lang: I18nContext.current().lang,
        });
        return {
          message: message,
          user: { id, firstName, lastName, email, phoneNumber },
        };
      } else {
        const otp = generateOTP(identifier);
        console.log('otp::::::', otp);
        const dataToSave: any = {
          firstName: dto.firstName,
          lastName: dto.lastName,
          otp: otp,
          isActive: true,
          type: dto.type,
        };

        if (identifier.includes('@')) {
          dataToSave.email = identifier.toLowerCase();
        } else if (/^\d{10}$/.test(identifier)) {
          dataToSave.phoneNumber = identifier;
          console.log('data to save', dataToSave);
        } else {
          const message = this.i18n.t('test.errorMessage.INVALID_IDENTIFIER', {
            lang: I18nContext.current().lang,
          });
          throw new Error(message);
        }
        const newUser = await this.userRepository.create(dataToSave);
        console.log('newUser:::::', newUser.phoneNumber);
        if (newUser.email) {
          await this.sendgridService.sendEmail(
            newUser.email,
            'Verify Your Account with OTP',
            Email.getTemplate({
              title: Constants.Titles.VerificationOTP,
              userName: `${newUser.firstName} ${newUser.lastName}`,
              children: `
          <p>
          For your account's security, please verify it using the one-time
          password (OTP) below:
        </p>
        <div class="otp">${otp}</div>
        <p>
        This OTP is valid for a limited time and can only be used once.
        <br />
        If you didn’t request this, contact our
        <a href="mailto:<EMAIL>">support team</a> immediately.
        </p>
        <p>Thank you for helping us keep your account secure.</p>

        `,
            }),
          );
        }
        if (identifier === newUser.phoneNumber) {
          const templateId = TEMPLATE_IDS.Otp_Sms;
          const phone = newUser.phoneNumber;

          const payload = {
            phone,
            templateId,
            var1: newUser.otp,
          };
          await this.smsService.triggerSMS(payload, true);
        }

        if (dto.roleId) {
          const roleExist = await this.roleRepository.findOne({
            where: { id: dto.roleId },
          });
          if (!roleExist) {
            const message = this.i18n.t('test.errorMessage.ROLE_NOT_EXIST', {
              lang: I18nContext.current().lang,
            });
            throw new Error(message);
          }
          await this.userRoleRepository.create({
            userId: newUser.id,
            roleId: roleExist.id,
          });
        }
        const { id, firstName, lastName, email, phoneNumber } = newUser;
        if (dto.category && dto.category.length) {
          await this.associateCategory(dto.category, newUser);
        }
        if (dto.subCategory && dto.subCategory.length) {
          await this.associateSubCategory(dto.subCategory, newUser);
        }

        const settingsData = {
          accountSettings: {
            appLanguage: dto.appLanguage,
            communicationLanguage: dto.appLanguage,
            profileVisibility: 'public',
          },
          notificationPreference: {
            jobAlerts: true,
            newBidRequest: true,
            newQuotation: true,
            inAppNotifications: true,
            sms: true,
            email: true,
          },
        };

        await this.updateUserAccountSettings(newUser.id, settingsData, true);
        const message = this.i18n.t('test.successMessage.OTP_SENT', {
          lang: I18nContext.current().lang,
        });
        return {
          message: message,
          user: { id, firstName, lastName, email, phoneNumber },
        };
      }
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  };

  updateUser = async (id: number, dto: UpdateUserDTO) => {
    const user = await this.userRepository.findByPk(id, {
      include: [
        {
          model: Category,
          through: {
            attributes: [],
          },
          as: 'categories',
          attributes: ['id', 'name', 'parentId', 'roleId', 'createdAt'],
        },
        {
          model: Category,
          through: {
            attributes: [],
          },
          as: 'subCategories',
          attributes: ['id', 'name', 'parentId'],
        },
        {
          model: NotificationPreference,
          as: 'notificationPreference',
        },
      ],
    });
    if (!user) {
      const message = this.i18n.t('test.errorMessage.USER_NOT_FOUND', {
        lang: I18nContext.current().lang,
      });
      throw new NotFoundException(message);
    }

    const {
      email,
      phoneNumber,
      category,
      subCategory,
      skills,
      tags,
      geolocation,
      ...rest
    } = dto;

    if (email && email !== user.email) {
      const existingUserWithEmail = await this.userRepository.findOne({
        where: { email },
      });
      if (existingUserWithEmail) {
        const message = this.i18n.t('test.errorMessage.EMAIL_ALREADY_EXISTS', {
          lang: I18nContext.current().lang,
        });
        throw new CustomException(message, HttpStatus.UNAUTHORIZED);
      }
      user.email = email;
    }

    if (phoneNumber && phoneNumber !== user.phoneNumber) {
      const existingUserWithPhone = await this.userRepository.findOne({
        where: { phoneNumber, isVerified: true },
      });
      if (existingUserWithPhone) {
        throw new CustomException(
          ERROR_MESSAGES.PHONE_NO_ALREADY_EXISTS,
          HttpStatus.UNAUTHORIZED,
        );
      }
      user.phoneNumber = phoneNumber;
    }
    if (dto.gstIn) {
      const existingGstIn = await this.userRepository.findOne({
        where: {
          gstIn: dto.gstIn,
        },
      });
      if (existingGstIn) {
        throw new CustomException(
          ERROR_MESSAGES.GSTIN_NO_ALREADY_EXISTS,
          HttpStatus.BAD_REQUEST,
        );
      }
    }
    if (geolocation) {
      await user.update({
        geolocation: geolocation
          ? {
              type: 'Point',
              coordinates: geolocation,
            }
          : null,
      });
    }
    Object.assign(user, rest);
    if (
      user.email &&
      user.notificationPreference.email &&
      dto.firstName &&
      dto.lastName
    ) {
      await this.sendgridService.sendEmail(
        user.email,
        'Profile Update Confirmation',
        Email.getTemplate({
          title: 'Profile Updated Successfully',
          userName: `${user.firstName} ${user.lastName}`,
          children: `
        <p> Your profile settings have been successfully updated.</p>
        `,
        }),
      );
    }
    if (
      user.notificationPreference.inAppNotifications &&
      dto.firstName &&
      dto.lastName
    ) {
      const data = {
        type: notificationType.UPDATE_PROFILE_SETTINGS,
        receiverId: user.id,
        prefrence: 'inAppNotifications',
      };
      await this.notificationService.sendNotification(data);
    }

    await user.save();
    if (skills && skills.length) {
      const skillIds: any = [];
      for (let i = 0; i < skills.length; i++) {
        skillIds.push(skills[i].value);
      }
      await this.associateSkills(skillIds, user);
    }
    if (tags && tags.length) {
      const tagIds: any = [];
      for (let i = 0; i < tags.length; i++) {
        tagIds.push(tags[i].value);
      }
      await this.associateTags(tagIds, user);
    }
    if (category && category.length) {
      const categoriesExist = user.categories.map((category) => category.id);
      const newCategoryIds = category;
      const isEqual =
        categoriesExist.length === newCategoryIds.length &&
        categoriesExist.sort().join(',') === newCategoryIds.sort().join(',');
      if (!isEqual) {
        try {
          await this.associateCategory(category, user);
        } catch (error) {
          console.error(error.message);

          if (error.message) {
            throw new BadRequestException(error.message);
          }
        }
      }
    }
    if (subCategory && subCategory.length) {
      const subCategoriesExist = user.subCategories.map(
        (subcategory) => subcategory.id,
      );
      const newSubCategoryIds = subCategory;
      const isEqual =
        subCategoriesExist.length === newSubCategoryIds.length &&
        subCategoriesExist.sort().join(',') ===
          newSubCategoryIds.sort().join(',');
      if (!isEqual) {
        try {
          await this.associateSubCategory(subCategory, user);
        } catch (error) {
          console.error(error.message);
          if (error.message) {
            throw new BadRequestException(error.message);
          }
        }
      }
    }

    const updatedUser = await this.userRepository.findOne({
      where: { id: user.id },
      attributes: {
        exclude: ['password', 'otp', 'verifyOTP'],
      },
      include: [
        {
          model: Role,
          as: 'roles',
          through: { attributes: [] },
          attributes: ['id', 'roleName'],
          required: false,
        },
        {
          model: Tags,
          as: 'tags',
          through: { attributes: [] },
          required: false,
        },
        {
          model: Category,
          through: {
            attributes: [],
          },
          as: 'categories',
          attributes: [
            'id',
            'name',
            'parentId',
            'roleId',
            'createdAt',
            [
              Sequelize.literal(
                `(SELECT COUNT(*) FROM "Categories" AS subcategory WHERE subcategory."parentId" = "categories"."id") > 0`,
              ),
              'subCategoryExist',
            ],
          ],
        },
        {
          model: Category,
          through: {
            attributes: [],
          },
          as: 'subCategories',
          attributes: ['id', 'name', 'parentId'],
        },
        {
          model: NotificationPreference,
          as: 'notificationPreference',
          required: false,
        },
        {
          model: AccountSettings,
          as: 'accountSettings',
          required: false,
        },
      ],
    });
    return { user: updatedUser };
  };

  async verifyOtp(dto: VerifyOtpDTO): Promise<{ token: string; user: User }> {
    const { identifier, otp } = dto;

    const user = await this.userRepository.findOne({
      where: {
        [Op.or]: [{ email: identifier }, { phoneNumber: identifier }],
        isVerified: false,
        otp: otp,
        isDeleted: false,
      },
      include: [
        {
          model: Role,
          as: 'roles', // Ensure this matches your alias for the roles association
          attributes: ['id', 'roleName'],
          through: { attributes: [] }, // Assuming a junction table UserRoles
        },
      ],
    });
    if (!user) {
      throw new UnauthorizedException('Invalid identifier or OTP');
    }

    // OTP is valid, generate a token
    const token = await this.authService.generateToken(user);

    user.isVerified = true;
    user.otp = null;
    await user.save();

    const packageExist = await this.packageRepository.findOne({
      where: {
        status: PackageStatus.ACTIVE,
        packageType: PackageType.MONTHLY,
        title: 'free',
        role: user.roles[0].roleName,
      },
    });
    if (packageExist) {
      const subscriptionData = {
        userId: user.id,
        subscriptionType: SubscriptionType.SINGLE,
        packageType: packageExist.packageType,
        validFrom: new Date(),
        status: SubscriptionStatus.ACTIVE,
        validTill: calculateValidTillDate(packageExist.packageType),
        packageId: packageExist.id,
        totalAmount: packageExist.price,
      };
      const createFreeTrial =
        await this.userSubscriptionRepository.create(subscriptionData);
      console.log('free subscription', createFreeTrial);
    }
    const data = {
      title: notificationTitle.REGISTRATION_SUCCESSFUL,
      message: notificationMessage.REGISTRATION_SUCCESSFUL,
      type: notificationType.REGISTRATION_SUCCESSFUL,
      receiverId: user.id,
      prefrence: 'inAppNotifications',
    };
    await this.notificationService.sendNotification(data);

    const updatedUser = await this.userRepository.findOne({
      where: {
        id: user.id,
      },
      attributes: {
        include: [
          [
            sequelize.literal(`(
              SELECT COUNT(*)
              FROM "connections" AS c
              WHERE c."requester_id" = "User"."id"
              AND c."status" = 'active'
              AND DATE_TRUNC('month', c."created_at") = DATE_TRUNC('month', CURRENT_DATE)
            )`),
            'connectionRequestCount',
          ],
          [
            sequelize.literal(`(
              SELECT COUNT(*)
              FROM "services" AS s
              WHERE s."userId" = "User"."id"
              AND s."status" = 'active'
              AND DATE_TRUNC('month', s."createdAt") = DATE_TRUNC('month', CURRENT_DATE)
            )`),
            'productCount',
          ],
          [
            sequelize.literal(`(
        SELECT CAST(ROUND(AVG(rating) * 2) / 2 AS DECIMAL(2,1))
        FROM "ReviewRatings" AS rr
        WHERE rr."revieweeId" = "User"."id"
      )`),
            'averageRating',
          ],
          [
            sequelize.literal(`(
          SELECT COUNT(*)
          FROM "RequestQuotations" AS rq
          WHERE rq."userId" = "User"."id"
          AND DATE_TRUNC('month', rq."createdAt") = DATE_TRUNC('month', CURRENT_DATE)
          )`),
            'requestedQuotationCount',
          ],
          [
            sequelize.literal(`(
            SELECT COUNT(*)
            FROM "ReviewRatings" AS rr
            WHERE rr."reviewerId" = "User"."id" AND rr."parentId" IS NULL
            AND DATE_TRUNC('month', rr."createdAt") = DATE_TRUNC('month', CURRENT_DATE)
            )`),
            'reviewRatingCount',
          ],
          [
            sequelize.literal(`(
              SELECT COUNT(*)
              FROM "UserJobBids" AS ujb
              WHERE ujb."userId" = "User"."id"
              AND ujb."status" = 'accepted'
              AND DATE_TRUNC('month', ujb."createdAt") = DATE_TRUNC('month', CURRENT_DATE)
        )`),
            'JobBidCount',
          ],
        ],
        exclude: ['otp'],
      },
      include: [
        {
          model: Role,
          as: 'roles', // Ensure this matches your alias for the roles association
          attributes: ['id', 'roleName'],
          through: { attributes: [] }, // Assuming a junction table UserRoles
        },
        {
          model: Category,
          through: {
            attributes: [],
          },
          as: 'categories',
        },
        {
          model: Category,
          through: {
            attributes: [],
          },
          as: 'subCategories',
        },
        {
          model: AccountSettings,
          as: 'accountSettings',
          attributes: ['appLanguage', 'communicationLanguage'],
          required: false,
        },
        {
          model: UserSubscription,
          as: 'subscription',
          where: { status: SubscriptionStatus.ACTIVE },
          required: false,
          include: [
            {
              model: Package,
              as: 'package',
              include: [
                {
                  model: PackageDetails,
                  as: 'packageDetails',
                },
              ],
            },
          ],
        },
      ],
    });
    if (user.email) {
      await this.sendgridService.sendEmail(
        user.email,
        'Welcome to Apna Builder',
        Email.getTemplate({
          title: Constants.Titles.WelcomeToApnaBuilder,
          userName: `${user.firstName} ${user.lastName}`,
          children: `
        <p>Thank you for joining Apna Builder! We're excited to have you as part of our community. Let’s get started with your journey.</p>
    <p>If you have any questions or need assistance, our friendly support team is just a click away. You can reach us at <span class="support"><EMAIL></span>.</p>
    <p>We look forward to providing you with an exceptional experience.</p>
        `,
        }),
      );
    }

    if (dto.identifier === user.phoneNumber) {
      const templateId = TEMPLATE_IDS.Welcome_Sms;
      const phone = user.phoneNumber;

      const payload = {
        phone,
        templateId,
        var1: `${user.firstName} ${user.lastName}`.trim(),
      };
      await this.smsService.triggerSMS(payload, false);
    }
    return { token, user: updatedUser };
  }

  // @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT) // Run every day mid-night
  // async checkIncompleteProfiles() {
  //   const where = {
  //     [Op.or]: [
  //       { geolocation: null },
  //       { workingDays: null },
  //       { gender: null },
  //       { gstIn: null },
  //       { website: null },
  //       { address1: null },
  //       { address2: null },
  //       { profilePicture: null },
  //     ],
  //   };
  //   const users = await this.userRepository.findAll({
  //     where: where,
  //     include: [
  //       {
  //         model: NotificationPreference,
  //         as: 'notificationPreference',
  //       },
  //     ],
  //   });

  //   const currentTime = new Date();
  //   users.forEach(async (user) => {
  //     const signupTime = new Date(user.createdAt);
  //     const hoursSinceSignup =
  //       (currentTime.getTime() - signupTime.getTime()) / (1000 * 60 * 60);

  //     if (hoursSinceSignup <= 24) {
  //       if (user.email) {
  //         try {
  //           await this.sendgridService.sendEmail(
  //             user.email,
  //             'Complete Your Profile',
  //             Email.getTemplate({
  //               title: Constants.Titles.CompleteYourProfile,
  //               userName: `${user.firstName} ${user.lastName}`,
  //               children: `
  //       <p>We noticed you haven’t completed your profile. Complete it now to unlock all features.</p>
  //       `,
  //             }),
  //           );
  //         } catch (error) {
  //           console.log('error', error);
  //           throw new ConflictException(
  //             'Not able to send OTP, Please try again after some time',
  //           );
  //         }
  //       }
  //       if (user.notificationPreference?.inAppNotifications) {
  //         const data = {
  //           title: notificationTitle.UPDATE_PROFILE,
  //           message: notificationMessage.UPDATE_PROFILE,
  //           type: notificationType.UPDATE_PROFILE,
  //           receiverId: user.id,
  //           prefrence: 'inAppNotifications',
  //         };
  //         await this.notificationService.sendNotification(data);
  //       }
  //     }
  //   });
  // }

  async sendOtp(sendOtpDto: SendOtpDto): Promise<{ message: string }> {
    const { identifier } = sendOtpDto;

    const user = await this.userRepository.findOne({
      where: {
        isDeleted: false,
        [Op.or]: [{ email: identifier }, { phoneNumber: identifier }],
      },
    });

    if (!user) {
      // throw new NotFoundException('User not found');
      const message = this.i18n.t('test.errorMessage.USER_NOT_FOUND', {
        lang: I18nContext.current().lang,
      });
      throw new NotFoundException(message);
    }

    if (!user.isVerified) {
      const message = this.i18n.t('test.errorMessage.USER_NOT_VERIFIED', {
        lang: I18nContext.current().lang,
      });
      throw new BadRequestException(message);
      // throw new BadRequestException('User is not verified');
    }
    if (!user.isActive) {
      const message = this.i18n.t('test.errorMessage.USER_BLOCKED', {
        lang: I18nContext.current().lang,
      });
      throw new BadRequestException(message);
      // throw new BadRequestException('User is Blocked');
    }

    const otp = generateOTP(identifier);
    if (sendOtpDto.identifier === user.email) {
      await this.sendgridService.sendEmail(
        user.email,
        'Login OTP',
        Email.getTemplate({
          title: 'Login OTP',
          userName: `${user?.firstName} ${user?.lastName}`,
          children: `
        <p>
        For your account's security, please verify it using the one-time
        password (OTP) below:
      </p>
      <div class="otp">${otp}</div>
    <p>
        This OTP is valid for a limited time and can only be used once.
        <br />
        If you didn’t request this, contact our
        <a href="mailto:<EMAIL>">support team</a> immediately.
      </p>
      <p>Thank you for helping us keep your account secure.</p>

        `,
        }),
      );
    }
    if (sendOtpDto.identifier === user.phoneNumber) {
      const templateId = TEMPLATE_IDS.Otp_Sms;
      const phone = user.phoneNumber;

      const payload = {
        phone,
        templateId,
        var1: otp,
      };
      await this.smsService.triggerSMS(payload, true);
      // console.log('sms :::::::::::::::::::', sms);
    }
    await this.userRepository.update({ otp }, { where: { id: user.id } });
    return { message: 'otp sent successfully' };
  }

  async loginOtp(dto: LoginOtpDto): Promise<{ token: string; user: User }> {
    const { identifier, otp } = dto;

    const user = await this.userRepository.findOne({
      where: {
        isDeleted: false,
        [Op.or]: [{ email: identifier }, { phoneNumber: identifier }],
      },
      attributes: {
        include: [
          [
            sequelize.literal(`(
              SELECT COUNT(*)
              FROM "connections" AS c
              WHERE c."requester_id" = "User"."id"
              AND DATE_TRUNC('month', c."created_at") = DATE_TRUNC('month', CURRENT_DATE)
            )`),
            'connectionRequestCount',
          ],
          [
            sequelize.literal(`(
              SELECT COUNT(*)
              FROM "services" AS s
              WHERE s."userId" = "User"."id"
              AND s."status" = 'active'
              AND DATE_TRUNC('month', s."createdAt") = DATE_TRUNC('month', CURRENT_DATE)
            )`),
            'productCount',
          ],
          [
            sequelize.literal(`(
        SELECT CAST(ROUND(AVG(rating) * 2) / 2 AS DECIMAL(2,1))
        FROM "ReviewRatings" AS rr
        WHERE rr."revieweeId" = "User"."id"
      )`),
            'averageRating',
          ],
          [
            sequelize.literal(`(
          SELECT COUNT(*)
          FROM "RequestQuotations" AS rq
          WHERE rq."userId" = "User"."id"
          AND DATE_TRUNC('month', rq."createdAt") = DATE_TRUNC('month', CURRENT_DATE)
        )`),
            'requestedQuotationCount',
          ],
          [
            sequelize.literal(`(
          SELECT COUNT(*)
          FROM "ReviewRatings" AS rr
          WHERE rr."reviewerId" = "User"."id"
          AND rr."parentId" IS NULL
          AND DATE_TRUNC('month', rr."createdAt") = DATE_TRUNC('month', CURRENT_DATE)
        )`),
            'reviewRatingCount',
          ],
          [
            sequelize.literal(`(
    SELECT COUNT(*)
    FROM "UserJobBids" AS ujb
    WHERE ujb."userId" = "User"."id"
    AND EXTRACT(MONTH FROM ujb."createdAt") = EXTRACT(MONTH FROM CURRENT_DATE)
    AND EXTRACT(YEAR FROM ujb."createdAt") = EXTRACT(YEAR FROM CURRENT_DATE)
  )`),
            'JobBidCount',
          ],
          [
            sequelize.literal(`(
    SELECT COUNT(*)
    FROM "UserJobs" AS uj
    WHERE uj."userId" = "User"."id"
    AND EXTRACT(MONTH FROM uj."createdAt") = EXTRACT(MONTH FROM CURRENT_DATE)
    AND EXTRACT(YEAR FROM uj."createdAt") = EXTRACT(YEAR FROM CURRENT_DATE)
  )`),
            'workerJobCount',
          ],
        ],
      },
      include: [
        {
          model: Role,
          as: 'roles', // Ensure this matches your alias for the roles association
          attributes: ['id', 'roleName'],
          through: { attributes: [] }, // Assuming a junction table UserRoles
        },
        {
          model: Category,
          through: {
            attributes: [],
          },
          as: 'categories',
        },
        {
          model: Category,
          through: {
            attributes: [],
          },
          as: 'subCategories',
        },
        {
          model: AccountSettings,
          as: 'accountSettings',
          attributes: ['appLanguage', 'communicationLanguage'],
          required: false,
        },
        {
          model: UserSubscription,
          as: 'subscription',
          where: {
            status: {
              [Op.in]: [
                SubscriptionStatus.ACTIVE,
                SubscriptionStatus.CANCELLED,
              ],
            },
          },
          required: false,
          include: [
            {
              model: Package,
              as: 'package',
              include: [
                {
                  model: PackageDetails,
                  as: 'packageDetails',
                },
              ],
            },
          ],
        },
      ],
    });
    // console.log('logeed in user:::::', user);
    if (!user) {
      const message = this.i18n.t('test.errorMessage.USER_NOT_FOUND', {
        lang: I18nContext.current().lang,
      });
      throw new NotFoundException(message);
      // throw new NotFoundException('User not found');
    }

    if (user.otp !== otp) {
      const message = this.i18n.t('test.errorMessage.OTP_INVALID', {
        lang: I18nContext.current().lang,
      });
      throw new UnauthorizedException(message);
      // throw new UnauthorizedException('Invalid OTP');
    }

    await this.userRepository.update(
      { otp: null, isVerified: true, lastLogin: new Date() }, // Changed from lastlogin to lastLogin
      { where: { id: user.id } },
    );

    const token = await this.authService.generateToken(user);

    return { token, user };
  }

  async sendConnection(
    requesterId: number,
    receiverId: number,
  ): Promise<Connection> {
    const existingConnection = await this.connectionModel.findOne({
      where: {
        requester_id: requesterId,
        receiver_id: receiverId,
        status: 'pending',
      },
    });

    if (existingConnection) {
      throw new ConflictException('Connection request already sent.');
    }

    const connection = await this.connectionModel.create({
      requester_id: requesterId,
      receiver_id: receiverId,
      status: 'pending',
      created_at: new Date(),
      updated_at: new Date(),
    });

    return connection;
  }

  async getUsersExcludingAdminsAndConnected(
    senderId: number,
    page: number,
    limit: number,
    search: string,
    globalFilter: any,
  ): Promise<{ users: User[]; total: number }> {
    const sender = await this.userRepository.findOne({
      where: {
        id: senderId,
      },
      include: [
        {
          model: Role,
        },
        {
          model: UserSubscription,
          as: 'subscription',
          where: {
            status: {
              [Op.in]: [
                SubscriptionStatus.ACTIVE,
                SubscriptionStatus.INACTIVE,
                SubscriptionStatus.CANCELLED,
                SubscriptionStatus.EXPIRED,
              ],
            },
          },
          required: false,
          include: [
            {
              model: Package,
              as: 'package',
              include: [
                {
                  model: PackageDetails,
                  as: 'packageDetails',
                },
              ],
            },
          ],
        },
      ],
    });
    if (!sender) {
      throw new NotFoundException('Sender not found');
    }

    const excludeRoles: string[] = [
      Constants.Roles.ADMIN,
      Constants.Roles.SUPER_ADMIN,
    ];
    let includeRoles: string[] = [];

    sender.roles.forEach((role) => {
      switch (role.roleName) {
        case Constants.Roles.SELLER:
          includeRoles.push(
            Constants.Roles.BUYER,
            Constants.Roles.ARCHITECT,
            Constants.Roles.CONTRACTOR,
          );
          break;
        case Constants.Roles.LABOUR:
          includeRoles.push(
            Constants.Roles.BUYER,
            Constants.Roles.SELLER,
            Constants.Roles.CONTRACTOR,
          );
          break;
        default:
          includeRoles = includeRoles.concat(
            Object.values(Constants.Roles).filter(
              (roleName) => !excludeRoles.includes(roleName),
            ),
          );
          break;
      }
    });

    const sentConnections = await this.connectionModel.findAll({
      where: {
        [Op.or]: [{ requester_id: senderId }, { receiver_id: senderId }],
      },
      attributes: ['requester_id', 'receiver_id'],
    });
    // console.log('sentConnections::::::', sentConnections);

    const connectedUserIds = sentConnections.map((connection) => {
      return connection.requester_id === senderId
        ? connection.receiver_id
        : connection.requester_id;
    });
    // console.log('connectedUserIds:::::', connectedUserIds);

    const offset = (page - 1) * limit;

    const searchCondition = search
      ? {
          [Op.or]: [
            sequelize.where(
              sequelize.fn(
                'concat',
                sequelize.col('firstName'),
                ' ',
                sequelize.col('lastName'),
              ),
              {
                [Op.iLike]: `%${search}%`,
              },
            ),
            { email: { [Op.iLike]: `%${search}%` } },
          ],
        }
      : {};

    const attributes: any = [
      'id',
      'firstName',
      'lastName',
      'city',
      'state',
      'email',
      'profilePicture',
      [
        sequelize.literal(`(
        SELECT CAST(ROUND(AVG(rating) * 2) / 2 AS DECIMAL(2,1))
              FROM "ReviewRatings" AS rr
              WHERE rr."revieweeId" = "User"."id"
            )`),
        'averageRating',
      ],
    ];
    const userRadius = sender.subscription.package.packageDetails.radiusLimit;
    const [userLon, userLat] = sender.geolocation.coordinates;
    let latitude: number, longitude: number, distance: number;

    if (globalFilter?.geolocation && globalFilter?.radius) {
      latitude = globalFilter.geolocation[1];
      longitude = globalFilter.geolocation[0];
      distance = globalFilter.radius * 1000;
    } else if (globalFilter?.geolocation) {
      latitude = globalFilter.geolocation[1];
      longitude = globalFilter.geolocation[0];
      distance = userRadius * 1000;
    } else if (globalFilter?.radius && userLat && userLon) {
      latitude = userLat;
      longitude = userLon;
      distance = globalFilter.radius * 1000;
    } else if (userLat && userLon) {
      latitude = userLat;
      longitude = userLon;
      distance = userRadius * 1000;
    } else {
      throw new CustomException(
        ERROR_MESSAGES.UPDATE_LOCATION,
        HttpStatus.BAD_REQUEST,
      );
    }

    const { rows: users, count: total } =
      await this.userRepository.findAndCountAll({
        include: [
          {
            model: Role,
            as: 'roles',
            attributes: ['roleName'],
            where: {
              roleName: {
                [Op.in]: includeRoles,
              },
            },
            through: { attributes: [] },
          },
        ],
        where: {
          id: {
            [Op.notIn]: [senderId, ...connectedUserIds],
          },
          isDeleted: false,
          isVerified: true,
          isActive: true,
          ...searchCondition,
          [Op.and]: [
            Sequelize.literal(`
              "User"."id" NOT IN (SELECT "blockedId" FROM "UserBlocks" WHERE "blockerId" = ${senderId})
            `),
            Sequelize.literal(`
              "User"."id" NOT IN (SELECT "blockerId" FROM "UserBlocks" WHERE "blockedId" = ${senderId})
            `),
            sequelize.where(
              sequelize.literal(`
            ST_DWithin(
              "geolocation"::geography,
              ST_SetSRID(ST_MakePoint(${longitude}, ${latitude}), 4326)::geography,
              ${distance}
            )
          `),
              true,
            ),
          ],
        },
        attributes: attributes,
        offset,
        limit,
        logging: true,
      });

    return { users, total };
  }

  async getSentConnectionRequests(
    userId: number,
    page: number,
    limit: number,
  ): Promise<{ connections: Connection[]; total: number }> {
    const offset = (page - 1) * limit;
    const { count, rows } = await this.connectionModel.findAndCountAll({
      where: { requester_id: userId },
      offset,
      limit,
    });

    return { connections: rows, total: count };
  }

  async resendOtp(resendOtpDto: SendOtpDto): Promise<{ message: string }> {
    const { identifier } = resendOtpDto;

    const user = await this.userRepository.findOne({
      where: {
        [Op.or]: [{ email: identifier }, { phoneNumber: identifier }],
      },
    });
    if (!user) {
      throw new BadRequestException('User not found');
    }
    if (!user.isActive) {
      throw new BadRequestException('User is Blocked');
    }

    const otp = generateOTP(identifier);
    if (identifier === user.email) {
      await this.sendgridService.sendEmail(
        user.email,
        'Login OTP',
        Email.getTemplate({
          title: 'Login OTP',
          userName: `${user?.firstName} ${user?.lastName}`,
          children: `
        <p>
        For your account's security, please verify it using the one-time
        password (OTP) below:
      </p>
      <div class="otp">${otp}</div>
    <p>
        This OTP is valid for a limited time and can only be used once.
        <br />
        If you didn’t request this, contact our
        <a href="mailto:<EMAIL>">support team</a> immediately.
      </p>
      <p>Thank you for helping us keep your account secure.</p>

        `,
        }),
      );
    }
    if (identifier === user.phoneNumber) {
      const templateId = TEMPLATE_IDS.Otp_Sms;
      const phone = user.phoneNumber;

      const payload = {
        phone,
        templateId,
        var1: otp,
      };
      await this.smsService.triggerSMS(payload, true);
    }
    await this.userRepository.update({ otp }, { where: { id: user.id } });
    return {
      message: 'OTP resent successfully',
    };
  }

  private async fetchPaginatedData(
    model: any,
    where: any,
    attributes: any,
    include: any[] = [],
    order: any[] = [['createdAt', 'DESC']],
  ) {
    const data = await model.findAll({
      where,
      attributes,
      include,
      order,
      logging: true,
    });

    // Check if there is enough data on the requested page; if not, fetch the last page
    return data;
  }

  async globalSearchDropdown(LoggedUser: any, filters: any) {
    // Get user and radius information
    const { userRadius } = await this.getUserWithSubscription(LoggedUser.id);

    // Process geolocation and distance parameters
    const { latitude, longitude, distance } = this.processGeolocationParams(
      filters,
      LoggedUser,
      userRadius,
    );

    // Fetch data in parallel
    const [users, services, jobs] = await Promise.all([
      this.fetchUsersForGlobalSearch(
        LoggedUser,
        filters,
        latitude,
        longitude,
        distance,
      ),
      this.fetchServicesForGlobalSearch(
        LoggedUser,
        filters,
        latitude,
        longitude,
        distance,
      ),
      this.fetchJobsForGlobalSearch(
        LoggedUser,
        filters,
        latitude,
        longitude,
        distance,
      ),
    ]);

    // Sort results by subscription
    const sortedUsers = users ? await this.sortUsersBySubscription(users) : [];
    const sortedServices = services
      ? await this.sortServicesBySubscription(services)
      : [];
    const sortedJobs = jobs ? await this.sortJobsBySubscription(jobs) : [];

    return { users: sortedUsers, services: sortedServices, jobs: sortedJobs };
  }

  // Helper function to get user with subscription details
  private async getUserWithSubscription(userId: number) {
    const userExist = await this.userRepository.findOne({
      where: { id: userId },
      include: [
        {
          model: UserSubscription,
          as: 'subscription',
          where: { status: { [Op.in]: [SubscriptionStatus.ACTIVE] } },
          required: true,
          include: [
            {
              model: Package,
              as: 'package',
              include: [{ model: PackageDetails, as: 'packageDetails' }],
            },
          ],
        },
      ],
    });

    if (!userExist) {
      throw new NotFoundException(
        'User not found or has no active subscription',
      );
    }

    // Fix for radius - ensure we have a valid number
    const userRadius =
      parseFloat(
        userExist.subscription.package.packageDetails.dataValues.radiusLimit,
      ) || 10;

    return { userExist, userRadius };
  }

  // Process geolocation parameters
  private processGeolocationParams(
    filters: any,
    LoggedUser: any,
    packageRadius: number,
  ) {
    const { geolocation, radius } = filters;
    let latitude: number, longitude: number, distance: number;

    console.log(
      'LoggedUser.geolocation?.coordinates....',
      LoggedUser.geolocation.coordinates[1],
    );
    console.log(
      'LoggedUser.geolocation?.coordinates....',
      LoggedUser.geolocation.coordinates[0],
    );
    console.log('packageRadius....', packageRadius * 1000);

    // Case 1: User provided both geolocation and radius
    if (geolocation && radius) {
      latitude = parseFloat(geolocation[1]);
      longitude = parseFloat(geolocation[0]);
      // Use the user-provided radius (don't limit by package radius)
      distance = parseFloat(radius) * 1000;
    }
    // Case 2: User provided only geolocation
    else if (geolocation) {
      latitude = parseFloat(geolocation[1]);
      longitude = parseFloat(geolocation[0]);
      // Use subscription radius
      distance = packageRadius * 1000;
    }
    // Case 3: User provided only radius but has geolocation in profile
    else if (radius && LoggedUser.geolocation?.coordinates) {
      latitude = parseFloat(LoggedUser.geolocation.coordinates[1]);
      longitude = parseFloat(LoggedUser.geolocation.coordinates[0]);
      // Use the user-provided radius (don't limit by package radius)
      distance = parseFloat(radius) * 1000;
    }
    // Case 4: User has geolocation in profile but no radius specified
    else if (LoggedUser.geolocation?.coordinates) {
      latitude = parseFloat(LoggedUser.geolocation.coordinates[1]);
      longitude = parseFloat(LoggedUser.geolocation.coordinates[0]);
      // Use subscription radius
      distance = packageRadius * 1000;
    }
    // Case 5: No geolocation available
    else {
      throw new CustomException(
        ERROR_MESSAGES.UPDATE_LOCATION,
        HttpStatus.BAD_REQUEST,
      );
    }

    // Validate coordinates and distance
    if (
      isNaN(latitude) ||
      isNaN(longitude) ||
      isNaN(distance) ||
      latitude < -90 ||
      latitude > 90 ||
      longitude < -180 ||
      longitude > 180
    ) {
      throw new CustomException(
        'Invalid geolocation or radius parameters',
        HttpStatus.BAD_REQUEST,
      );
    }

    return { latitude, longitude, distance };
  }

  // Fetch users for global search
  private async fetchUsersForGlobalSearch(
    LoggedUser: any,
    filters: any,
    latitude: number,
    longitude: number,
    distance: number,
  ) {
    console.log(
      'Search parameters - latitude:',
      latitude,
      'longitude:',
      longitude,
      'distance:',
      distance,
    );

    const { search } = filters;
    const whereUser: any = {
      isVerified: true,
      isActive: true,
      isDeleted: false,
    };

    // Add search conditions
    if (search) {
      whereUser[Op.or] = [
        { firstName: { [Op.iLike]: `%${search}%` } },
        { lastName: { [Op.iLike]: `%${search}%` } },
        sequelize.where(
          sequelize.fn(
            'concat',
            sequelize.col('firstName'),
            ' ',
            sequelize.col('lastName'),
          ),
          { [Op.iLike]: `%${search}%` },
        ),
        sequelize.where(sequelize.col('"categories"."name"'), {
          [Op.iLike]: `%${search}%`,
        }),
        sequelize.where(sequelize.col('"subCategories"."name"'), {
          [Op.iLike]: `%${search}%`,
        }),
        sequelize.where(sequelize.col('"tags"."name"'), {
          [Op.iLike]: `%${search}%`,
        }),
      ];
    }

    // Add user blocking conditions
    if (LoggedUser?.id) {
      whereUser.id = {
        [Op.and]: [
          { [Op.ne]: LoggedUser.id },
          {
            [Op.notIn]: Sequelize.literal(`
              (SELECT "blockedId" FROM "UserBlocks" WHERE "blockerId" = ${LoggedUser.id})
            `),
          },
          {
            [Op.notIn]: Sequelize.literal(`
              (SELECT "blockerId" FROM "UserBlocks" WHERE "blockedId" = ${LoggedUser.id})
            `),
          },
        ],
      };
    }

    // Add geolocation filter - ensure longitude and latitude are in correct order
    // ST_MakePoint takes (longitude, latitude) in that order
    whereUser[Op.and] = Sequelize.where(
      Sequelize.literal(`
        ST_DWithin(
          "geolocation"::geography,
          ST_SetSRID(ST_MakePoint(${longitude}, ${latitude}), 4326)::geography,
          ${distance}
        )
      `),
      true,
    );

    // Include distance calculation in the attributes
    const attributes = {
      exclude: [
        'gender',
        'bio',
        'phoneNumber',
        'dateOfBirth',
        'address1',
        'address2',
        'firmName',
        'officeNumber',
        'website',
        'gstIn',
        'website',
        'password',
        'otp',
        'verifyOTP',
        'workingDays',
        'updatedAt',
        'closingTime',
        'openingTime',
        'secondaryPhoneNumber',
        'ratePerDay',
        'adminRoleId',
        'createdBy',
        'role',
      ],
      include: [
        [
          sequelize.literal(`(
            SELECT CAST(ROUND(AVG(rating) * 2) / 2 AS DECIMAL(2,1))
            FROM "ReviewRatings" AS rr
            WHERE rr."revieweeId" = "User"."id"
          )`),
          'averageRating',
        ],
        [
          // Calculate distance in kilometers with 2 decimal precision
          sequelize.literal(`
            CAST(
              ST_Distance(
                "geolocation"::geography,
                ST_SetSRID(ST_MakePoint(${longitude}, ${latitude}), 4326)::geography
              ) / 1000 AS DECIMAL(10,2)
            )
          `),
          'distance',
        ],
      ],
    };

    const users = await this.fetchPaginatedData(
      this.userRepository,
      whereUser,
      attributes,
      [
        {
          model: Tags,
          as: 'tags',
          through: { attributes: [] },
          attributes: ['name'],
          required: false,
        },
        {
          model: Role,
          as: 'roles',
          attributes: ['roleName'],
          through: { attributes: [] },
        },
        {
          model: Category,
          through: { attributes: [] },
          attributes: ['name'],
          as: 'categories',
        },
        {
          model: Category,
          through: { attributes: [] },
          attributes: ['name'],
          as: 'subCategories',
        },
        {
          model: UserSubscription,
          as: 'subscription',
          where: { status: { [Op.in]: [SubscriptionStatus.ACTIVE] } },
          attributes: ['subscriptionType', 'packageType', 'status'],
          required: false,
        },
      ],
      [
        [sequelize.literal('distance'), 'ASC'], // Sort by distance
      ],
    );

    return users;
  }

  // Fetch services for global search
  private async fetchServicesForGlobalSearch(
    LoggedUser: any,
    filters: any,
    latitude: number,
    longitude: number,
    distance: number,
  ) {
    const { search } = filters;
    const whereService: any = {
      userId: { [Op.ne]: LoggedUser.id },
    };

    // Add search conditions
    if (search) {
      whereService[Op.or] = [
        { title: { [Op.iLike]: `%${search}%` } },
        sequelize.where(sequelize.col('"tags"."name"'), {
          [Op.iLike]: `%${search}%`,
        }),
        sequelize.where(sequelize.col('"category"."name"'), {
          [Op.iLike]: `%${search}%`,
        }),
        sequelize.where(sequelize.col('"subCategory"."name"'), {
          [Op.iLike]: `%${search}%`,
        }),
      ];
    }

    // Add distance calculation
    const attributes = [
      'id',
      'title',
      'description',
      'createdAt',
      [
        sequelize.literal(`
          CAST(
            ST_Distance(
              "user"."geolocation"::geography,
              ST_SetSRID(ST_MakePoint(${longitude}, ${latitude}), 4326)::geography
            ) / 1000 AS DECIMAL(10,2)
          )
        `),
        'distance',
      ],
    ];

    return this.fetchPaginatedData(
      this.serviceModel,
      whereService,
      attributes,
      [
        {
          model: Category,
          as: 'category',
          attributes: ['name'],
        },
        {
          model: Category,
          as: 'subCategory',
          attributes: ['name'],
        },
        {
          model: Tags,
          as: 'tags',
          through: { attributes: [] },
          attributes: ['id', 'name'],
        },
        {
          model: User,
          as: 'user',
          attributes: [
            'id',
            'firstName',
            'lastName',
            'profilePicture',
            'geolocation',
            [
              sequelize.literal(`(
                SELECT CAST(ROUND(AVG(rating) * 2) / 2 AS DECIMAL(2,1))
                FROM "ReviewRatings" AS rr
                WHERE rr."revieweeId" = "user"."id"
              )`),
              'averageRating',
            ],
          ],
          where: Sequelize.where(
            Sequelize.literal(`
              ST_DWithin(
                "user"."geolocation"::geography,
                ST_SetSRID(ST_MakePoint(${longitude}, ${latitude}), 4326)::geography,
                ${distance}
              )
            `),
            true,
          ),
          include: [
            {
              model: UserSubscription,
              as: 'subscription',
              where: { status: { [Op.in]: [SubscriptionStatus.ACTIVE] } },
              attributes: ['subscriptionType', 'packageType', 'status'],
              required: true,
            },
          ],
        },
      ],
      [
        [sequelize.literal('distance'), 'ASC'], // Sort by distance
      ],
    );
  }

  // Fetch jobs for global search
  private async fetchJobsForGlobalSearch(
    LoggedUser: any,
    filters: any,
    latitude: number,
    longitude: number,
    distance: number,
  ) {
    const { search } = filters;
    const whereJob: any = {
      userId: { [Op.ne]: LoggedUser.id },
    };

    // Add search conditions
    if (search) {
      whereJob[Op.or] = [
        { title: { [Op.iLike]: `%${search}%` } },
        sequelize.where(sequelize.col('"criteria"."name"'), {
          [Op.iLike]: `%${search}%`,
        }),
        sequelize.where(sequelize.col('"subCategory"."name"'), {
          [Op.iLike]: `%${search}%`,
        }),
      ];
    }

    // Add geolocation filter - ensure longitude and latitude are in correct order
    whereJob[Op.and] = Sequelize.where(
      Sequelize.literal(`
        ST_DWithin(
          "Job"."geolocation"::geography,
          ST_SetSRID(ST_MakePoint(${longitude}, ${latitude}), 4326)::geography,
          "Job"."radius" * 1000
        )
        AND
        ST_DWithin(
          ST_SetSRID(ST_MakePoint(${longitude}, ${latitude}), 4326)::geography,
          "Job"."geolocation"::geography,
          ${distance}
        )
      `),
      true,
    );

    // Add distance calculation
    const attributes = [
      'id',
      'title',
      'description',
      'geolocation',
      'radius',
      'state',
      'city',
      'pincode',
      'createdAt',
      [
        sequelize.literal(`
          CAST(
            ST_Distance(
              "Job"."geolocation"::geography,
              ST_SetSRID(ST_MakePoint(${longitude}, ${latitude}), 4326)::geography
            ) / 1000 AS DECIMAL(10,2)
          )
        `),
        'distance',
      ],
    ];

    return this.fetchPaginatedData(
      this.jobModel,
      whereJob,
      attributes,
      [
        {
          model: Category,
          as: 'criteria',
          attributes: ['name'],
        },
        {
          model: Category,
          as: 'subCategory',
          attributes: ['name'],
        },
        {
          model: User,
          as: 'createdBy',
          attributes: [
            'id',
            'firstName',
            'lastName',
            'geolocation',
            [
              sequelize.literal(`(
                SELECT CAST(ROUND(AVG(rating) * 2) / 2 AS DECIMAL(2,1))
                FROM "ReviewRatings" AS rr
                WHERE rr."revieweeId" = "createdBy"."id"
              )`),
              'averageRating',
            ],
          ],
          include: [
            {
              model: UserSubscription,
              as: 'subscription',
              where: { status: { [Op.in]: [SubscriptionStatus.ACTIVE] } },
              attributes: ['subscriptionType', 'packageType', 'status'],
              required: true,
            },
          ],
        },
        {
          model: Role,
          as: 'lookingFor',
          where: { roleName: LoggedUser.roles[0].roleName },
          attributes: ['id', 'roleName'],
        },
      ],
      [
        [sequelize.literal('distance'), 'ASC'], // Sort by distance
      ],
    );
  }

  async sortUsersBySubscription(users: any[]) {
    const subscriptionComparator = (a: any, b: any) => {
      const aSubscription = a.subscription?.package?.title?.toLowerCase();
      const bSubscription = b.subscription?.package?.title?.toLowerCase();
      if (aSubscription === null || aSubscription === undefined) {
        return 1;
      }
      if (bSubscription === null || bSubscription === undefined) {
        return -1;
      }

      // Sort Premium users first, then Basic, then others
      if (aSubscription === 'premium' && bSubscription !== 'premium') {
        return -1;
      } else if (aSubscription !== 'premium' && bSubscription === 'premium') {
        return 1;
      } else if (aSubscription === 'basic' && bSubscription !== 'basic') {
        return -1;
      } else if (aSubscription !== 'basic' && bSubscription === 'basic') {
        return 1;
      } else if (aSubscription === 'free' && bSubscription !== 'free') {
        return -1;
      } else if (aSubscription !== 'free' && bSubscription === 'free') {
        return 1;
      } else if (aSubscription === 'default' && bSubscription !== 'default') {
        return -1;
      } else if (aSubscription !== 'default' && bSubscription === 'default') {
        return 1;
      } else {
        return a.id - b.id;
      }
    };

    // Sort the users using the custom comparator
    return users.sort(subscriptionComparator);
  }

  async sortServicesBySubscription(services: any[]) {
    return services.sort((a: any, b: any) => {
      const aSubscription = a.user?.subscription?.package?.title?.toLowerCase();
      const bSubscription = b.user?.subscription?.package?.title?.toLowerCase();

      // Sort Premium first, then Basic, then others
      if (aSubscription === 'premium' && bSubscription !== 'premium') {
        return -1;
      } else if (aSubscription !== 'premium' && bSubscription === 'premium') {
        return 1;
      } else if (aSubscription === 'basic' && bSubscription !== 'basic') {
        return -1;
      } else if (aSubscription !== 'basic' && bSubscription === 'basic') {
        return 1;
      } else if (aSubscription === 'free' && bSubscription !== 'free') {
        return -1;
      } else if (aSubscription !== 'free' && bSubscription === 'free') {
        return 1;
      } else if (aSubscription === 'default' && bSubscription !== 'default') {
        return -1;
      } else if (aSubscription !== 'default' && bSubscription === 'default') {
        return 1;
      } else {
        // If subscription titles are the same or undefined, leave order unchanged
        return 0;
      }
    });
  }
  async sortJobsBySubscription(jobs: any[]) {
    const subscriptionComparator = (a: any, b: any) => {
      const aSubscription =
        a.createdBy?.subscription?.package?.title?.toLowerCase();
      const bSubscription =
        b.createdBy?.subscription?.package?.title?.toLowerCase();
      if (aSubscription === null || aSubscription === undefined) {
        return 1;
      }
      if (bSubscription === null || bSubscription === undefined) {
        return -1;
      }

      // Sort Premium users first, then Basic, then others
      if (aSubscription === 'premium' && bSubscription !== 'premium') {
        return -1;
      } else if (aSubscription !== 'premium' && bSubscription === 'premium') {
        return 1;
      } else if (aSubscription === 'basic' && bSubscription !== 'basic') {
        return -1;
      } else if (aSubscription !== 'basic' && bSubscription === 'basic') {
        return 1;
      } else if (aSubscription === 'free' && bSubscription !== 'free') {
        return -1;
      } else if (aSubscription !== 'free' && bSubscription === 'free') {
        return 1;
      } else if (aSubscription === 'default' && bSubscription !== 'default') {
        return -1;
      } else if (aSubscription !== 'default' && bSubscription === 'default') {
        return 1;
      } else {
        return a.id - b.id;
      }
    };

    // Sort the users using the custom comparator
    return jobs.sort(subscriptionComparator);
  }
}
