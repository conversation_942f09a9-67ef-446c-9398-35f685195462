import {
  Table,
  Column,
  Model,
  ForeignKey,
  DataType,
  BelongsTo,
  HasMany,
  BelongsToMany,
} from 'sequelize-typescript';
import { User } from 'src/modules/admin/authentication/schemas/user.schema';
import { RequestQuotation } from './quotation-request.schema';
import { Tags } from './tags.schema';
import { ServiceTags } from './service-tags.schema';
import { Category } from 'src/modules/ category/schemas/category.schema';

export enum ServiceStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DELETED = 'deleted',
}

@Table({ tableName: 'services' })
export class Service extends Model<Service> {
  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  title: string;
  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  thumbnail: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  description: string;

  @Column({
    type: DataType.ENUM,
    values: Object.values(ServiceStatus),
    defaultValue: ServiceStatus.ACTIVE,
    allowNull: false,
  })
  status: ServiceStatus;

  @Column({
    type: DataType.ARRAY(DataType.STRING),
    allowNull: true,
  })
  images: string[];

  @ForeignKey(() => User)
  @Column({ allowNull: false })
  userId: number;

  @BelongsTo(() => User, { as: 'user', foreignKey: 'userId' })
  user: User;

  @BelongsToMany(() => Tags, () => ServiceTags)
  tags: Tags[];

  @ForeignKey(() => Category)
  @Column({ allowNull: false })
  categoryId: number;

  @ForeignKey(() => Category)
  @Column({ allowNull: true })
  subCategoryId: number;

  @BelongsTo(() => Category, { foreignKey: 'categoryId', as: 'category' })
  category: Category;

  @BelongsTo(() => Category, { foreignKey: 'subCategoryId', as: 'subCategory' })
  subCategory: Category;

  @HasMany(() => RequestQuotation, 'serviceId')
  requestQuotations: RequestQuotation[];
}
