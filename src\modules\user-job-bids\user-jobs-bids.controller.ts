import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Query,
  UseGuards,
  Request,
  Patch,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';

import { JWTGuard } from 'src/guards/common/jwt.guard';
import { UserJobBidService } from './user-job-bids.servics';
import { BidInviteDto } from './dto/create-bid-invite.dto';
import { UserJobBidStatus, UserJobBidType } from './utills/enum';
import { UserJobBids } from './schemas/user-job-bids.schema';
import { Job } from '../job/schemas/job.schema';
import { User } from '../admin/authentication/schemas/user.schema';
import { BiddingDetail } from '../job/schemas/bidding-details.schema';
import { Role } from '../roles/schemas/role.schema';
import { BidsService } from './bids.service';
import { Bids } from './schemas/bids.schema';
import { BidPlaceDto } from './dto/place-bid.dto';
import { PatchUpdateStatusDto } from './dto/patch-update-status.dto';
import { JobStatus } from 'src/common/utils/enum';
import { literal, Sequelize } from 'sequelize';
import sequelize from 'sequelize';
import { UserJobStatus } from '../user-jobs/utils/enum';
import { Op } from 'sequelize';
import { SendgridService } from '../sendgrid/sendgrid.service';
import { Email } from 'src/common/email-template';
import { InjectModel } from '@nestjs/sequelize';
import { Category } from '../ category/schemas/category.schema';
import { ReviewRating } from '../review-rating/schemas/review-rating.schema';
import { Notification } from '../notification/schema/notification.schema';
import { NotificationPreference } from '../user-management/schemas/notification-preferences.schema';
@ApiTags('User-job-bids')
@Controller('user-job-bids')
export class UserJobBidsController {
  constructor(
    private readonly userJobBidService: UserJobBidService,
    private readonly bidsService: BidsService,
    @InjectModel(User)
    private userRepository: typeof User,
    private readonly sendgridService: SendgridService,
    @InjectModel(Job)
    private jobModel: typeof Job,
    @InjectModel(Notification)
    private notificationModel: typeof Notification,
  ) {}

  @Post('/:userId/bid-invite')
  @ApiOperation({ summary: 'Send a bid invite' })
  @ApiBearerAuth()
  @UseGuards(JWTGuard)
  async sendBidInvite(
    @Body() bidInviteDto: BidInviteDto,
    @Param('userId', ParseIntPipe) userId: number,
  ): Promise<UserJobBids[]> {
    const getAllUserJobs = await this.userJobBidService.getAll({
      where: {
        userId,
        jobId: bidInviteDto.jobIds,
        status: { [Op.in]: [UserJobBidStatus.CLOSED, UserJobStatus.ACCEPTED] },
        // attributes: ['jobId'],
      },
    });
    const userJobsIds = getAllUserJobs.map((userJob) => userJob.jobId);
    if (userJobsIds.length) {
      if (userJobsIds.length === bidInviteDto.jobIds.length) {
        throw new HttpException(
          'User is already associated with selected jobs',
          HttpStatus.CONFLICT,
        );
      }
      bidInviteDto.jobIds = bidInviteDto.jobIds.filter(
        (jobId) => !userJobsIds.includes(jobId),
      );
    }
    const payload = bidInviteDto.jobIds.map((jobId) => {
      return {
        userId,
        jobId,
        status: UserJobBidStatus.INVITED,
        type: UserJobBidType.INVITED,
      };
    });
    const userDetails = await this.userRepository.findByPk(userId);
    // if (userDetails.email) {
    //   await this.sendgridService.sendEmail(
    //     userDetails.email,
    //     'New Bid on Your Job',
    //     Email.getTemplate({
    //       title: 'You Have a New Bid!',
    //       userName: `${userDetails.firstName} ${userDetails.lastName}`,
    //       children: `
    //   <p>
    //   You’ve received a new bid invitation. Check it out now.
    // </p>
    //   `,
    //     }),
    //   );
    // }
    if (userDetails.email) {
      await this.sendgridService.sendEmail(
        userDetails.email,
        'New Bid on Your Job',
        Email.getTemplate({
          title: 'You Have a New Bid!',
          userName: `${userDetails.firstName} ${userDetails.lastName}`,
          children: `
      <p>
        A new bid has been placed on your job. Check it out now.
    </p>
      `,
        }),
      );
    }
    return await this.userJobBidService.bulkCreate(payload);
  }

  @Get()
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get  list of all job-bids of logged in user',
  })
  @ApiQuery({ name: 'limit', required: false, type: String })
  @ApiQuery({ name: 'page', required: false, type: String })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({
    name: 'type',
    required: false,
    type: String,
    enum: Object.values(UserJobBidType),
  })
  @ApiQuery({
    name: 'status',
    required: false,
    type: String,
    enum: [
      UserJobBidStatus.INVITED,
      UserJobBidStatus.ACCEPTED,
      UserJobBidStatus.REJECTED,
      UserJobBidStatus.CLOSED,
    ],
  })
  @ApiQuery({
    name: 'by',
    required: false,
    type: String,
    enum: ['other', 'jobCreator'],
  })
  @ApiResponse({
    status: 200,
    description: 'Return list of all bids.',
  })
  async getAllJobsBids(
    @Request() req,
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Query('type') type = null,
    @Query('status') status = null,
    @Query('search') search = null,
    @Query('by') by = null,
  ): Promise<{ userJobs: any[]; total: number }> {
    const includeModels: any = [
      {
        model: Job,
        as: 'job',
        attributes: ['id', 'title', 'description', 'radius', 'geolocation'],
        include: [
          {
            model: Category,
            as: 'criteria',
            attributes: ['id', 'name', 'parentId'],
          },
          {
            model: Category,
            as: 'subCategory',
            attributes: ['id', 'name', 'parentId'],
          },
          {
            model: BiddingDetail,
            as: 'biddingDetail',
            attributes: ['id', 'minRange', 'maxRange', 'startDate', 'endDate'],
          },
          {
            model: User,
            as: 'createdBy',
            attributes: [
              'firstName',
              'lastName',
              'email',
              'role',
              'phoneNumber',
              'profilePicture',
              [
                sequelize.literal(`(
                SELECT CAST(ROUND(AVG(rating) * 2) / 2 AS DECIMAL(2,1))
                FROM "ReviewRatings" AS rr
                WHERE rr."revieweeId" = "job->createdBy"."id"
              )`),
                'averageRating',
              ],
            ],
          },
        ],
      },
    ];
    if (type === UserJobBidType.SELF_PLACED) {
      includeModels.push({
        model: Bids,
        as: 'bids',
        limit: 1,
        order: [['createdAt', 'DESC']],
        attributes: [
          'id',
          'firstName',
          'lastName',
          'contactNumber',
          'email',
          'amount',
          'description',
          'workCompletionDurationInDays',
        ],
      });
    }
    const includeJob: any = {
      model: Job,
      as: 'job',
      attributes: [
        'id',
        'title',
        'description',
        'radius',
        'geolocation',
        'city',
        'state',
      ],
      include: [
        {
          model: Category,
          as: 'criteria',
          attributes: ['id', 'name', 'parentId'],
        },
        {
          model: Category,
          as: 'subCategory',
          attributes: ['id', 'name', 'parentId'],
        },
        {
          model: BiddingDetail,
          as: 'biddingDetail',
          attributes: [
            'id',
            'minRange',
            'maxRange',
            [
              Sequelize.literal(`
                FLOOR(EXTRACT(EPOCH FROM ("endDate" - NOW())) / 86400)::INTEGER
              `),
              'daysRemaining',
            ],
            [
              Sequelize.literal(`
                FLOOR((EXTRACT(EPOCH FROM ("endDate" - NOW())) / 3600) % 24)::INTEGER
              `),
              'hoursRemaining',
            ],
          ],
        },
        {
          model: User,
          as: 'createdBy',
          attributes: [
            'firstName',
            'lastName',
            'email',
            'role',
            'phoneNumber',
            'profilePicture',
            [
              sequelize.literal(`(
                SELECT CAST(ROUND(AVG(rating) * 2) / 2 AS DECIMAL(2,1))
                FROM "ReviewRatings" AS rr
                WHERE rr."revieweeId" = "job->createdBy"."id"
              )`),
              'averageRating',
            ],
          ],
        },
      ],
    };
    const geolocation = req?.user?.geolocation;
    if (geolocation) {
      const [userLon, userLat] = geolocation?.coordinates;

      includeJob.attributes.push([
        sequelize.literal(`
      FLOOR(6371 * acos(
        cos(radians(${userLat})) *
        cos(radians(ST_Y("job".geolocation::geometry))) *
        cos(radians(ST_X("job".geolocation::geometry)) - radians(${userLon})) +
        sin(radians(${userLat})) *
        sin(radians(ST_Y("job".geolocation::geometry)))
      ))
    `),
        'distance',
      ]);
    }
    let query: any = {
      where: {
        userId: req.user.id,
        ...(type && { type: type }),
        ...(status && { status: status }),
        ...(search && {
          '$job.title$': { [Op.iLike]: `%${search}%` },
        }),
      },
      include: [
        includeJob,
        {
          model: Bids,
          as: 'bids',
          limit: 1,
          order: [['createdAt', 'DESC']],
          attributes: [
            'id',
            'firstName',
            'lastName',
            'contactNumber',
            'email',
            'amount',
            'description',
            'workCompletionDurationInDays',
          ],
        },
        {
          model: User,
          as: 'user',
          attributes: [
            'id',
            'firstName',
            'lastName',
            'email',
            'phoneNumber',
            [
              sequelize.literal(`(
                SELECT CAST(ROUND(AVG(rating) * 2) / 2 AS DECIMAL(2,1))
                FROM "ReviewRatings" AS rr
                WHERE rr."revieweeId" = "user"."id"
              )`),
              'averageRating',
            ],
          ],
        },
      ],
      attributes: ['id', 'status', 'type', 'createdAt', 'updatedAt'],
      order: [['updatedAt', 'DESC']],
      offset: (page - 1) * limit,
      limit,
      distinct: true,
      logging: true,
    };
    if (by === 'jobCreator') {
      includeJob.where = { userId: req.user.id };
      query = {
        where: {
          ...(status && { status: status }),
          ...(search && {
            '$job.title$': { [Op.iLike]: `%${search}%` },
          }),
        },
        include: [
          includeJob,
          {
            model: Bids,
            as: 'bids',
            limit: 1,
            order: [['createdAt', 'DESC']],
            attributes: [
              'id',
              'firstName',
              'lastName',
              'contactNumber',
              'email',
              'amount',
              'description',
              'workCompletionDurationInDays',
            ],
          },
          {
            model: User,
            as: 'user',
            attributes: [
              'id',
              'firstName',
              'lastName',
              'email',
              'phoneNumber',
              [
                sequelize.literal(`(
                SELECT CAST(ROUND(AVG(rating) * 2) / 2 AS DECIMAL(2,1))
                FROM "ReviewRatings" AS rr
                WHERE rr."revieweeId" = "user"."id"
              )`),
                'averageRating',
              ],
            ],
          },
        ],
      };
    }
    const getCount = await this.userJobBidService.getAll({
      attributes: [
        [
          literal(
            `COUNT(CASE WHEN type = '${UserJobBidType.INVITED}' THEN 1 ELSE NULL END)`,
          ),
          'newBidsCount',
        ],
        [
          literal(
            `COUNT(CASE WHEN type = '${UserJobBidType.SELF_PLACED}' THEN 1 ELSE NULL END)`,
          ),
          'myBidsCount',
        ],
        [
          literal(
            `COUNT(CASE WHEN status = '${UserJobBidStatus.ACCEPTED}' THEN 1 ELSE NULL END)`,
          ),
          'myJobsCount',
        ],
      ],
      where: { userId: req.user.id },
      raw: true,
    });
    const result = await this.userJobBidService.getAllWithCount(query);
    const res = {
      ...result,
      ...getCount[0],
    };
    return res;
  }

  @Get('count')
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get  count of job-bids invites',
  })
  @ApiResponse({
    status: 200,
    description: 'Return count of all bid requests.',
  })
  async getInvitedBidsCount(@Request() req) {
    const query = {
      where: {
        userId: req.user.id,
        type: UserJobBidType.INVITED,
      },
    };
    return this.userJobBidService.getCount(query);
  }
  @Get(':id')
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get job bid invite by id' })
  @ApiParam({ name: 'id', required: true, type: String })
  @ApiResponse({
    status: 200,
    description: 'Return job bid by ID.',
  })
  @ApiResponse({
    status: 404,
    description: 'Job bid not found.',
  })
  async getJobBidById(
    @Request() req,
    @Param('id') id: string,
  ): Promise<UserJobBids> {
    console.log('req.user.id', req.user.id);
    const jobBid = await this.userJobBidService.getOne({
      where: {
        id,
      },
      include: [
        {
          model: Job,
          as: 'job',
          attributes: [
            'id',
            'title',
            'description',
            'radius',
            'images',
            'geolocation',
            'city',
            'state',
            'pincode',
          ],
          include: [
            {
              model: Role,
              as: 'lookingFor',
              attributes: ['id', 'roleName'],
            },
            {
              model: Category,
              as: 'criteria',
              attributes: ['id', 'name', 'parentId'],
            },
            {
              model: Category,
              as: 'subCategory',
              attributes: ['id', 'name', 'parentId'],
            },
            {
              model: User,
              as: 'createdBy',
              include: [
                {
                  model: Role,
                  as: 'roles',
                  through: { attributes: [] },
                  attributes: ['id', 'roleName'],
                  required: false,
                },
                {
                  model: Category,
                  through: {
                    attributes: [],
                  },
                  as: 'categories',
                  attributes: ['id', 'name'],
                },
                {
                  model: Category,
                  through: {
                    attributes: [],
                  },
                  as: 'subCategories',
                  attributes: ['id', 'name', 'parentId'],
                },
              ],
              attributes: [
                'id',
                'firstName',
                'lastName',
                'email',
                'role',
                'phoneNumber',
                'profilePicture',
                'gender',
              ],
            },
            {
              model: BiddingDetail,
              as: 'biddingDetail',
              attributes: [
                'id',
                'minRange',
                'maxRange',
                'startDate',
                'endDate',
              ],
            },
            {
              model: ReviewRating,
              as: 'reviewRating',
              attributes: ['review', 'rating'],
            },
          ],
        },
        {
          model: Bids,
          as: 'bids',
          limit: 1,
          order: [['createdAt', 'DESC']],
          attributes: [
            'id',
            'firstName',
            'lastName',
            'contactNumber',
            'email',
            'amount',
            'description',
            'workCompletionDurationInDays',
          ],
        },
        {
          model: User,
          as: 'user',
          include: [
            {
              model: Role,
              as: 'roles',
              through: { attributes: [] },
              attributes: ['id', 'roleName'],
              required: false,
            },
            {
              model: Category,
              through: {
                attributes: [],
              },
              as: 'categories',
              attributes: ['id', 'name'],
            },
            {
              model: Category,
              through: {
                attributes: [],
              },
              as: 'subCategories',
              attributes: ['id', 'name', 'parentId'],
            },
          ],
          attributes: [
            'id',
            'firstName',
            'lastName',
            'email',
            'role',
            'phoneNumber',
            'profilePicture',
            [
              sequelize.literal(`(
                SELECT CAST(ROUND(AVG(rating) * 2) / 2 AS DECIMAL(2,1))
                FROM "ReviewRatings" AS rr
                WHERE rr."revieweeId" = "user"."id"
              )`),
              'averageRating',
            ],
          ],
        },
      ],
      attributes: ['id', 'status', 'isPaid', 'userId'],
    });

    if (!jobBid) {
      throw new HttpException('Job bid invite not found', HttpStatus.NOT_FOUND);
    }
    const hasNotification = await this.notificationModel.findOne({
      where: {
        receiverId: jobBid.job.createdBy.id,
        type: 'review_requested',
        additional: {
          jobId: jobBid.job.id,
        },
      },
    });
    let notificationExists = false;
    if (hasNotification) {
      notificationExists = true;
    }

    const bidDetails = jobBid.toJSON() as any;
    bidDetails.hasNotification = notificationExists;

    if (req.user.id === jobBid.userId) {
      bidDetails.user = bidDetails.job.createdBy;
      delete bidDetails.userId;
      console.log('bidDetails111', bidDetails);
      return bidDetails;
    }
    console.log('bidDetails', bidDetails);
    return bidDetails;
  }

  async patchStatus(
    @Request() req,
    @Body() payload: PatchUpdateStatusDto,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<UserJobBids> {
    const getUserJob = await this.userJobBidService.getOne({
      where: {
        userId: req.user.id,
        id: id,
      },
    });
    if (!getUserJob) {
      throw new HttpException('Job bid does not exist', HttpStatus.NOT_FOUND);
    }
    return this.userJobBidService.patchUpdateStatus(getUserJob, payload.status);
  }

  @Post(':id/initiate-discussion')
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Initiate a bid discussion' })
  @ApiResponse({
    status: 201,
    description: 'Bid discussion started successfully.',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async initiateBidDisscussion(
    @Request() req,
    @Param('id', ParseIntPipe) id: number,
  ) {
    const getUserJob = await this.userJobBidService.getOne({
      where: {
        id: id,
      },
    });
    if (!getUserJob) {
      throw new HttpException('Job bid does not exist', HttpStatus.NOT_FOUND);
    }
    return this.userJobBidService.patchUpdateStatus(
      getUserJob,
      UserJobBidStatus.DISCUSSION,
    );
  }

  // @Post('submit')
  // @ApiOperation({ summary: 'submit a bid' })
  // @ApiBearerAuth()
  // @UseGuards(JWTGuard)
  // async placeBid(
  //   @Body() bidPlaceDto: BidPlaceDto,
  //   @Request() req,
  // ): Promise<Bids> {
  //   const payload: any = {
  //     ...bidPlaceDto,
  //   };

  //   if (bidPlaceDto.jobId) {
  //     let getUserBid = await this.userJobBidService.getOne({
  //       where: {
  //         userId: req.user.id,
  //         jobId: bidPlaceDto.jobId,
  //       },
  //       attributes: ['id'],
  //     });
  //     const jobDetails = await this.jobModel.findByPk(bidPlaceDto.jobId);
  //     if (!getUserBid) {
  //       getUserBid = await this.userJobBidService.create({
  //         userId: req.user.id,
  //         jobId: bidPlaceDto.jobId,
  //         status: UserJobBidStatus.SUBMITTED,
  //         type: UserJobBidType.SELF_PLACED,
  //         jobOwnerId: jobDetails.userId,
  //       });
  //     }

  //     const userDetails = await this.userRepository.findByPk(jobDetails.userId);
  //     // if (userDetails.email) {
  //     //   await this.sendgridService.sendEmail(
  //     //     userDetails.email,
  //     //     'New Bid on Your Job',
  //     //     Email.getTemplate({
  //     //       title: 'You Have a New Bid!',
  //     //       userName: `${userDetails.firstName} ${userDetails.lastName}`,
  //     //       children: `
  //     //   <p>
  //     //   You’ve received a new bid invitation. Check it out now.
  //     //   A new bid has been placed on your job. Check it out now.
  //     // </p>
  //     //   `,
  //     //     }),
  //     //   );
  //     // }

  //     if (userDetails.email) {
  //       await this.sendgridService.sendEmail(
  //         userDetails.email,
  //         'New Bid on Your Job',
  //         Email.getTemplate({
  //           title: 'You Have a New Bid!',
  //           userName: `${userDetails.firstName} ${userDetails.lastName}`,
  //           children: `
  //       <p>
  //       A new bid has been placed on your job. Check it out now.
  //     </p>
  //       `,
  //         }),
  //       );
  //     }
  //     payload.userJobBidId = getUserBid.id;
  //   }
  //   return await this.bidsService.create(payload, bidPlaceDto.jobId);
  // }

  @Post('submit')
  @ApiOperation({ summary: 'submit a bid' })
  @ApiBearerAuth()
  @UseGuards(JWTGuard)
  async placeBid(
    @Body() bidPlaceDto: BidPlaceDto,
    @Request() req,
  ): Promise<Bids> {
    const payload: any = {
      ...bidPlaceDto,
    };

    if (bidPlaceDto.jobId) {
      let getUserBid = await this.userJobBidService.getOne({
        where: {
          userId: req.user.id,
          jobId: bidPlaceDto.jobId,
        },
        attributes: ['id'],
      });
      const jobDetails = await this.jobModel.findByPk(bidPlaceDto.jobId);
      if (!getUserBid) {
        getUserBid = await this.userJobBidService.create({
          userId: req.user.id,
          jobId: bidPlaceDto.jobId,
          status: UserJobBidStatus.SUBMITTED,
          type: UserJobBidType.SELF_PLACED,
          jobOwnerId: jobDetails.userId,
        });
      }

      const userDetails = await this.userRepository.findOne({
        where: { id: jobDetails.userId },
        include: [
          {
            model: NotificationPreference,
            as: 'notificationPreference',
            required: false,
          },
        ],
      });

      // Check if user is inactive for 14+ days
      const fourteenDaysAgo = new Date();
      fourteenDaysAgo.setDate(fourteenDaysAgo.getDate() - 14);

      // Add debugging logs
      console.log('User last login:', userDetails.lastLogin);
      console.log('Fourteen days ago:', fourteenDaysAgo);
      console.log(
        'Is user inactive?',
        !userDetails.lastLogin ||
          new Date(userDetails.lastLogin) < fourteenDaysAgo,
      );
      console.log(
        'Email notifications enabled?',
        userDetails.notificationPreference?.email === true && userDetails.email,
      );

      const isInactiveUser =
        !userDetails.lastLogin ||
        new Date(userDetails.lastLogin) < fourteenDaysAgo;
      const hasEmailNotificationsEnabled =
        userDetails.notificationPreference?.email === true && userDetails.email;
      console.log('isInactiveUser:', isInactiveUser);
      console.log(
        'hasEmailNotificationsEnabled:',
        hasEmailNotificationsEnabled,
      );

      if (hasEmailNotificationsEnabled && isInactiveUser) {
        console.log('Sending email notification');
        await this.sendgridService.sendEmail(
          userDetails.email,
          'New Bid on Your Job',
          Email.getTemplate({
            title: 'You Have a New Bid!',
            userName: `${userDetails.firstName} ${userDetails.lastName}`,
            children: `
            <p>
              A new bid has been placed on your job. Check it out now.
            </p>
          `,
          }),
        );
      }

      payload.userJobBidId = getUserBid.id;
    }
    return await this.bidsService.create(payload, bidPlaceDto.jobId);
  }

  @Get(':id/submit')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Fetch bid history' })
  @ApiResponse({ status: 200, description: 'Bid history fetched successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiQuery({ name: 'limit', required: false, type: String })
  @ApiQuery({ name: 'page', required: false, type: String })
  async getSubmittedBids(
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<{ bids: Bids[]; total: number }> {
    const whereQuery = {
      where: {
        ...(id && {
          userJobBidId: id,
        }),
      },
      order: [['createdAt', 'ASC']],
      offset: (page - 1) * limit,
      limit,
    };
    return this.bidsService.getAllWithCount(whereQuery);
  }

  @Get('/by-job/:jobId')
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user job bids by bid job id' })
  @ApiParam({ name: 'jobId', required: true, type: String })
  @ApiQuery({
    name: 'type',
    required: false,
    type: String,
    enum: Object.values(UserJobBidType),
  })
  @ApiQuery({
    name: 'status',
    required: false,
    type: String,
    description: `please enter status as ${UserJobStatus.REQUESTED}, ${UserJobStatus.INTERESTED},${UserJobStatus.ACCEPTED},${UserJobStatus.REJECTED}`,
  })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'limit', required: false, type: String })
  @ApiQuery({ name: 'page', required: false, type: String })
  @ApiResponse({
    status: 200,
    description: 'Return job bid by job id',
  })
  @ApiResponse({
    status: 404,
    description: 'Job bids not found.',
  })
  async getJobBids(
    @Request() req,
    @Param('jobId') jobId: number,
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Query('type') type: string,
    @Query('status') status = null,
    @Query('search') search: string,
  ): Promise<{ userJobs: UserJobBids[]; total: number }> {
    const includeModels: any = [
      {
        model: Job,
        as: 'job',
        attributes: ['title'],
        include: [
          {
            model: Category,
            as: 'criteria',
            attributes: ['id', 'name', 'parentId'],
          },
          {
            model: Category,
            as: 'subCategory',
            attributes: ['id', 'name', 'parentId'],
          },
          {
            model: User,
            as: 'createdBy',
            attributes: [
              'firstName',
              'lastName',
              'email',
              'role',
              'phoneNumber',
              'profilePicture',
            ],
          },
        ],
      },
      {
        model: User,
        as: 'user',
        where: search
          ? {
              [Op.or]: [
                { '$user.firstName$': { [Op.iLike]: `%${search}%` } },
                { '$user.lastName$': { [Op.iLike]: `%${search}%` } },
                sequelize.where(
                  sequelize.fn(
                    'concat',
                    sequelize.col('user.firstName'),
                    ' ',
                    sequelize.col('user.lastName'),
                  ),
                  {
                    [Op.iLike]: `%${search}%`,
                  },
                ),
              ],
            }
          : undefined,
        include: [
          {
            model: Role,
            attributes: ['roleName'],
            through: { attributes: [] },
          },
          {
            model: Category,
            through: {
              attributes: [],
            },
            as: 'categories',
            attributes: ['id', 'name'],
          },
          {
            model: Category,
            through: {
              attributes: [],
            },
            as: 'subCategories',
            attributes: ['id', 'name', 'parentId'],
          },
        ],
        attributes: [
          'id',
          'firstName',
          'lastName',
          'email',
          'phoneNumber',
          'profilePicture',
          'state',
          'city',
          'pincode',
          [
            sequelize.literal(`(
        SELECT CAST(ROUND(AVG(rating) * 2) / 2 AS DECIMAL(2,1))
        FROM "ReviewRatings" AS rr
        WHERE rr."revieweeId" = "user"."id"
      )`),
            'averageRating',
          ],
        ],
      },
      {
        model: Bids,
        as: 'bids',
        limit: 1,
        order: [['createdAt', 'DESC']],
        attributes: [
          'id',
          'firstName',
          'lastName',
          'contactNumber',
          'email',
          'amount',
          'description',
          'workCompletionDurationInDays',
        ],
      },
    ];

    const whereCondition: any = {
      jobId,
    };

    if (type) {
      whereCondition.type = type;
      whereCondition.status = {
        [Op.notIn]: ['accepted', 'rejected'],
      };
    } else if (status) {
      whereCondition.status = status;
    }

    const getCount = await this.userJobBidService.getAll({
      attributes: [
        [
          literal(
            `COUNT(CASE WHEN type = '${UserJobBidType.INVITED}' AND status NOT IN ('accepted', 'rejected') THEN 1 ELSE NULL END)`,
          ),
          'invitedBidsCount',
        ],
        [
          literal(
            `COUNT(CASE WHEN type = '${UserJobBidType.SELF_PLACED}' AND status NOT IN ('accepted', 'rejected') THEN 1 ELSE NULL END)`,
          ),
          'submittedBidsCount',
        ],
        [
          literal(
            `COUNT(CASE WHEN type In ('${UserJobBidType.SELF_PLACED}','${UserJobBidType.INVITED}') AND status = 'accepted' THEN 1 ELSE NULL END)`,
          ),
          'submittedAcceptedBidsCount',
        ],
        [
          literal(
            `COUNT(CASE WHEN type In ('${UserJobBidType.SELF_PLACED}','${UserJobBidType.INVITED}') AND status = 'rejected' THEN 1 ELSE NULL END)`,
          ),
          'submittedRejectedBidsCount',
        ],
      ],
      where: { jobId: jobId },
      raw: true,
    });
    const result = await this.userJobBidService.getAllWithCount({
      where: whereCondition,
      include: includeModels,
      attributes: ['id', 'status', 'createdAt'],
      order: [['createdAt', 'DESC']],
      offset: (page - 1) * limit,
      limit,
      distinct: true,
    });
    const res = {
      ...result,
      ...getCount[0],
    };
    return res;
  }

  @Patch(':id/accept-reject')
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'accept reject bid' })
  @ApiResponse({
    status: 200,
    description: 'bid status updated',
  })
  async acceptReject(
    @Request() req,
    @Body() payload: PatchUpdateStatusDto,
    @Param('id', ParseIntPipe) id: number,
  ) {
    const getUserJob = await this.userJobBidService.getOne({
      where: {
        id: id,
      },
      include: [
        {
          model: Job,
          as: 'job',
          attributes: ['id', 'status'],
          include: [
            {
              model: User,
              as: 'createdBy',
              include: [
                {
                  model: Role,
                  as: 'roles',
                  through: { attributes: [] },
                  attributes: ['roleName'],
                },
              ],
              attributes: [
                'id',
                'firstName',
                'lastName',
                'email',
                'phoneNumber',
                'profilePicture',
              ],
            },
          ],
        },
        {
          model: User,
          as: 'user',
          attributes: ['id'],
        },
      ],
    });
    if (!getUserJob) {
      throw new HttpException('Job bid does not exist', HttpStatus.NOT_FOUND);
    }
    if (getUserJob.job.status === JobStatus.CLOSED) {
      throw new HttpException(
        'Bidding closed for this job',
        HttpStatus.BAD_REQUEST,
      );
    }
    const res = await this.userJobBidService.patchUpdateStatus(
      getUserJob,
      payload.status,
    );
    const user = await this.userJobBidService.getUserJobBidCount(
      getUserJob.user.id,
    );
    return {
      userJobBid: res,
      userJobBidCount: user,
    };
  }

  @Patch(':userJobBidId/payment-status')
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  async verifyJobPayment(
    @Request() req,
    @Param('userJobBidId', ParseIntPipe) userJobBidId: number,
  ) {
    return await this.userJobBidService.verifyPayment(
      userJobBidId,
      req.user.id,
    );
  }
}
