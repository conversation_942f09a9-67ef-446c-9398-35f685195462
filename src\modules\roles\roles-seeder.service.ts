import { Injectable, OnModuleInit } from '@nestjs/common'; // Your role model
import { Constants } from 'src/common/constants';
import { Role } from './schemas/role.schema';
// import { Roles } from '../'; // Import your predefined role

@Injectable()
export class RoleSeedService implements OnModuleInit {
  // This will be triggered when the module is initialized
  async onModuleInit() {
    // const count = await Role.count(); // Check how many roles already exist
    // if (count === 0) {
    //   await this.seedRoles(); // If no roles exist, seed them
    // }
  }

  // Seeding the predefined roles into the database
  seedRoles = async () => {
    console.log('Seeding roles...');
    const rolesData = Object.values(Constants.Roles).map((name) => ({
      roleName: name, // Directly use the roleName
    }));
    console.log('rolesData', rolesData);
    // Bulk insert the roles into the database
    const insertedRoles = await Role.bulkCreate(rolesData, {
      returning: true, // Return the inserted rows
    });

    console.log('Roles have been seeded successfully:', insertedRoles);
    return insertedRoles;
  };
}
