import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Put,
  Query,
  Request,
  UseGuards,
} from '@nestjs/common';
import { UserManagementService } from './user-management.service';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiQuery,
  <PERSON>piTags,
  ApiParam,
} from '@nestjs/swagger';
import { AdminGuard } from 'src/guards/admin/admin.guard';
import { User } from '../admin/authentication/schemas/user.schema';

import { JWTGuard } from 'src/guards/common/jwt.guard';
import {
  RegisterUserDTO,
  UpdateUserDTO,
  VerifyOtpDTO,
  LoginOtpDto,
  SendOtpDto,
  GlobalSearchDto,
} from './dto/registerUser.input.dto';
import { AdminPermissions } from 'src/common/decorators/permissions.decorator';
import { Modules } from 'src/common/utils/enum';
import { SendConnectionDto } from '../connection/dto/send-connection.dto';
import { Connection } from '../connection/schemas/connection.schema';
import { Op } from 'sequelize';
import { CreateUserSettingsDto } from './dto/account-settings.dto';

@ApiTags('User Management')
@Controller('user-management')
export class UserManagementController {
  constructor(private readonly userManagementService: UserManagementService) {}

  @Get()
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'limit', required: false, type: String })
  @ApiQuery({ name: 'page', required: false, type: String })
  @ApiQuery({ name: 'role', required: false, type: String })
  @ApiQuery({
    name: 'startDate',
    required: false,
    type: String,
    description: 'Filter users from this start date (YYYY-MM-DD)',
  })
  @ApiQuery({
    name: 'endDate',
    required: false,
    type: String,
    description: 'Filter users until this end date (YYYY-MM-DD)',
  })
  @ApiOperation({
    summary: 'List Users',
  })
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  async getAllUsers(
    @Request() req,
    @Query() query: GlobalSearchDto,
    @Query('role') role?: string,
    @Query('limit') limit?: string,
    @Query('page') page?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ): Promise<{ data: User[]; totalCount: number }> {
    const { geolocation, radius, search } = query;
    const limitValue = limit ? parseInt(limit, 10) : 10;
    const offsetValue = page ? parseInt(page, 10) : 1;
    const parsedGeolocation = geolocation?.map(Number);
    const filters = {
      loggedInUser: req.user.id,
      geolocation: parsedGeolocation,
      radius,
      search,
      role,
      startDate,
      endDate,
    };
    const userGeolocation = req.user.geolocation;
    return this.userManagementService.listUsers(
      limitValue,
      (offsetValue - 1) * limitValue,
      filters,
      userGeolocation,
    );
  }
  @Get('sellers')
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'limit', required: false, type: String })
  @ApiQuery({ name: 'offset', required: false, type: String })
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'List Sellers',
  })
  @UseGuards(JWTGuard)
  async getNearBySelleres(
    @Request() req,
    @Query('limit') limit?: string,
    @Query('offset') offset?: string,
  ): Promise<{ data: User[]; totalCount: number }> {
    const limitValue = limit ? parseInt(limit, 10) : 10;
    const offsetValue = offset ? parseInt(offset, 10) : 1;
    const filters = {
      ...((req.user.state || req.user.city) && {
        [Op.or]: [{ state: req.user.state }, { city: req.user.city }],
      }),
    };
    return this.userManagementService.listSellers(
      limitValue,
      (offsetValue - 1) * limitValue,
      filters,
    );
  }

  @Get('/:userId')
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get user details',
  })
  async viewUser(
    @Param('userId', ParseIntPipe) userId: number,
    @Request() req,
  ) {
    return this.userManagementService.getUser(req.user.id, userId);
  }

  @UseGuards(JWTGuard, AdminGuard)
  @AdminPermissions(Modules.USER_MANAGEMENT)
  @Put('/:userId/block')
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Block or Unblock user',
  })
  async blockOrUnblockUser(
    @Param('userId', ParseIntPipe) userId: number,
  ): Promise<{ message: string }> {
    return this.userManagementService.blockOrUnblockUser(userId);
  }

  @Post('/register')
  @ApiOperation({
    summary: 'Create User',
  })
  async registerUser(@Body() body: RegisterUserDTO) {
    return this.userManagementService.registerUser(body);
  }

  @Put('/:UserId/update')
  @ApiOperation({
    summary: 'Update User',
  })
  async UpdateUser(
    @Body() body: UpdateUserDTO,
    @Param('UserId') userId: string,
  ) {
    return this.userManagementService.updateUser(parseInt(userId), body);
  }

  @Put('/verifyOtp')
  @ApiOperation({
    summary: 'verify Otp',
  })
  async verifyOtp(@Body() body: VerifyOtpDTO) {
    const token = await this.userManagementService.verifyOtp(body);
    return { message: 'Registration successful', token };
  }

  @Post('login-otp')
  async sendOtp(@Body() sendOtpDto: SendOtpDto) {
    const message = await this.userManagementService.sendOtp(sendOtpDto);
    return message;
  }

  @Put('verifylogin')
  async login(@Body() loginOtpDto: LoginOtpDto) {
    const token = await this.userManagementService.loginOtp(loginOtpDto);
    return { message: 'Login successful', token };
  }

  @Post('/send')
  @ApiOperation({ summary: 'Send a connection request' })
  // @UseGuards(JWTGuard,AdminGuard)
  async sendConnection(@Body() sendConnectionDto: SendConnectionDto) {
    return this.userManagementService.sendConnection(
      sendConnectionDto.requesterId,
      sendConnectionDto.receiverId,
    );
  }

  @Get('/user/suggestion_connection')
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary:
      'Get all users excluding admins, super admins, and those who received a connection from the specified sender',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of records per page',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Search term to filter users',
  })
  async getUsersExcludingAdminsAndConnected(
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Query('search') search = null,
    @Request() req,
    @Query() query: GlobalSearchDto,
  ): Promise<{ users: User[]; total: number }> {
    const { geolocation, radius } = query;
    const parsedGeolocation = geolocation?.map(Number);
    const globalFilter = {
      geolocation: parsedGeolocation,
      radius,
    };
    return this.userManagementService.getUsersExcludingAdminsAndConnected(
      req.user.id,
      page,
      limit,
      search,
      globalFilter,
    );
  }

  @Get('sent/:userId')
  @ApiOperation({ summary: 'Get all sent connection requests by a user' })
  @ApiParam({ name: 'userId', required: true, description: 'User ID' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of records per page',
  })
  async getSentConnectionRequests(
    @Param('userId', ParseIntPipe) userId: number,
    @Query('page', ParseIntPipe) page = 1,
    @Query('limit', ParseIntPipe) limit = 10,
  ): Promise<{ connections: Connection[]; total: number }> {
    return this.userManagementService.getSentConnectionRequests(
      userId,
      page,
      limit,
    );
  }

  @Get('/user/category')
  @ApiOperation({
    summary: 'Get all category and sub category of logged in user',
  })
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  async userCategory(@Request() req) {
    return this.userManagementService.getUserCategory(req.user.id);
  }
  @Get('/user/account-settings')
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get account settings of logged in user',
  })
  async userAccountSetting(@Request() req) {
    return this.userManagementService.getUserAccountSttings(req.user.id);
  }
  @Put('/user/account-settings')
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Update  account settings of logged in user',
  })
  async updateUserAccountSetting(
    @Body() body: CreateUserSettingsDto,
    @Request() req,
  ) {
    return this.userManagementService.updateUserAccountSettings(
      req.user.id,
      body,
      false,
    );
  }

  @Patch('resend-otp')
  async resendOtp(@Body() resendOtpDto: SendOtpDto) {
    return this.userManagementService.resendOtp(resendOtpDto);
  }

  @Delete('/user')
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'delete user account',
  })
  async deleteUser(@Request() req) {
    return this.userManagementService.deleteUser(req.user);
  }

  // @Get('/all/global-search')
  // // @ApiQuery({ name: 'search', required: false, type: String })
  // // @ApiQuery({ name: 'geolocation', required: false, type: String })
  // // @ApiQuery({ name: 'radius', required: false, type: Number })
  // // @ApiQuery({ name: 'limit', required: false, type: Number })
  // // @ApiQuery({ name: 'page', required: false, type: Number })
  // @UseGuards(JWTGuard)
  // @ApiBearerAuth()
  // async globalSearch(
  //   @Request() req,
  //   @Query() query: GlobalSearchDto,
  // ) {
  //   const { geolocation, globalSearch, radius } = query;

  //   // Parse geolocation if it's defined
  //   const parsedGeolocation = geolocation?.map(Number);

  //   const globalFilter = {
  //     geolocation: parsedGeolocation,
  //     radius,
  //     globalSearch,
  //   };
  //   const user = req.user;
  //   return this.userManagementService.globalSearch(user, globalFilter);
  // }

  @Get('/global-search/dropdown')
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  async globalSearchDropdown(@Request() req, @Query() query: GlobalSearchDto) {
    const { geolocation, radius, search } = query;
    const parsedGeolocation = geolocation?.map(Number);
    const filters = {
      search,
      geolocation: parsedGeolocation,
      radius,
    };
    const user = req.user;
    return this.userManagementService.globalSearchDropdown(user, filters);
  }
}
