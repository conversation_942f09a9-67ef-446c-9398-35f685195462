import {
  Controller,
  Post,
  Put,
  Get,
  Body,
  Param,
  ParseIntPipe,
  Query,
  UseGuards,
  Request,
  Patch,
} from '@nestjs/common';
import { ServiceService } from './service.service';
import {
  CreateServiceDto,
  keywordDto,
  UpdateServiceDto,
  UpdateServiceStatusDto,
} from './dto/service.dto';
import {
  ApiOperation,
  ApiTags,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import {
  RequestQuotationDto,
  SendQuotationDto,
  UpdateIsReadDto,
  UpdateQuotationStatusDto,
} from './dto/quotation.dto';
import { Tags } from './schemas/tags.schema';
import { JWTGuard } from 'src/guards/common/jwt.guard';
import { Service } from './schemas/service.schema';
import { GlobalSearchDto } from '../user-management/dto/registerUser.input.dto';

@ApiTags('services')
@Controller('services')
export class ServiceController {
  constructor(private readonly serviceService: ServiceService) {}

  @Post()
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new service' })
  async createService(
    @Request() req,
    @Body() createServiceDto: CreateServiceDto,
  ) {
    return this.serviceService.createService(createServiceDto, req.user.id);
  }

  @Put('/update')
  @ApiOperation({ summary: 'Update an existing service' })
  async updateService(@Body() updateServiceDto: UpdateServiceDto) {
    return this.serviceService.updateService(updateServiceDto);
  }

  @Patch('/delete')
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'delete an existing service' })
  async deleteService(
    @Request() req,
    @Body() updateServiceStatusDto: UpdateServiceStatusDto,
  ) {
    return this.serviceService.deleteService(
      req.user.id,
      updateServiceStatusDto,
    );
  }

  @Get()
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all services' })
  @ApiQuery({ name: 'limit', required: false, type: String })
  @ApiQuery({ name: 'page', required: false, type: String })
  async getAllServices(
    @Request() req,
    @Query() query: GlobalSearchDto,
    @Query('page') page = 1,
    @Query('limit') limit = 10,
  ): Promise<{ services: Service[]; total: number }> {
    const role = req.user.roles[0].roleName;
    const { geolocation, radius, search } = query;
    const parsedGeolocation = geolocation?.map(Number);
    const filters = {
      loggedInUser: req.user.id,
      geolocation: parsedGeolocation,
      radius,
      search,
    };
    return this.serviceService.getAllServices(
      page,
      limit,
      role,
      req.user,
      filters,
    );
  }

  @Get(':serviceId/')
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get service by ID' })
  async getServiceById(
    @Request() req,
    @Param('serviceId', ParseIntPipe) serviceId: number,
  ) {
    return this.serviceService.getServiceById(serviceId, req.user.id);
  }

  @Get(':userId/product')
  @ApiOperation({ summary: 'Get service by userID' })
  @ApiQuery({ name: 'limit', required: false, type: String })
  @ApiQuery({ name: 'page', required: false, type: String })
  async getServiceByUserId(
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Param('userId', ParseIntPipe) userId: number,
  ) {
    return this.serviceService.getServiceByUserId(userId, page, limit);
  }
}

@ApiTags('Service Tags')
@Controller('tag')
export class TagController {
  constructor(private readonly serviceService: ServiceService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new service tag' })
  async createTags(@Body() createTagDto: keywordDto): Promise<Tags> {
    return this.serviceService.createServiceTag(createTagDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all service Tags' })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'page', required: false, type: Number })
  async getAllServicetags(@Query('page') page = 1, @Query('limit') limit = 10) {
    return this.serviceService.getAllServicetags(page, limit);
  }

  @Put('/update')
  @ApiOperation({ summary: 'Update an existing service tag' })
  async updateServicetag(@Body() updatekeywordDto: keywordDto) {
    return this.serviceService.updateServiceTag(updatekeywordDto);
  }

  @Get('/:id')
  @ApiOperation({ summary: 'Get service tags by ID' })
  async getServiceTagById(@Param('id', ParseIntPipe) id: number) {
    return this.serviceService.getServiceTagById(id);
  }
}

/////////////////////////// OUOTATION /////////////////////////////

@ApiTags('Quotation')
@Controller('quotation')
export class QuotationController {
  constructor(private readonly serviceService: ServiceService) {}

  @Post('/create-quotation')
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @ApiQuery({ name: 'requestedTo', required: false })
  @ApiQuery({ name: 'serviceId', required: false })
  async createQuotation(
    @Request() req,
    @Body() dto: RequestQuotationDto,
    @Query('requestedTo') requestedTo?: number, // requestedTo as a query parameter
    @Query('serviceId') serviceId?: number, // serviceId as a query parameter
  ) {
    return this.serviceService.requestQuotation(
      dto,
      req.user.id,
      requestedTo,
      serviceId,
    );
  }

  @Get('get-all')
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all requested quotation' })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'page', required: false, type: Number })
  async getAllRequestedQuotation(
    @Request() req,
    @Query('search') search?: string,
    @Query('page') page = 1,
    @Query('limit') limit = 10,
  ) {
    return this.serviceService.getAllRequestedQuotation(
      req.user.id,
      req.user.roles[0].roleName,
      page,
      limit,
      search,
    );
  }

  @Get('/:id')
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get requested quotation by ID' })
  async getRequestQuotationById(
    @Param('id', ParseIntPipe) id: number,
    @Request() req,
  ) {
    return this.serviceService.getRequestedQuotationById(id, req.user.id);
  }
  @Patch(':quotationId/status')
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  async updateQuotationStatus(
    @Param('quotationId', ParseIntPipe) quotationId: number,
    @Body() updateQuotationStatusDto: UpdateQuotationStatusDto,
  ) {
    return await this.serviceService.updateStatus(
      quotationId,
      updateQuotationStatusDto,
    );
  }
  @Patch(':quotationId/payment-status')
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  async verifyQutationPayment(
    @Request() req,
    @Param('quotationId', ParseIntPipe) quotationId: number,
  ) {
    return await this.serviceService.verifyPayment(quotationId, req.user.id);
  }

  @Post('send_quotation')
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'send a quotation' })
  @ApiQuery({ name: 'quotationId' })
  async sendQuotation(
    @Request() req,
    @Query('quotationId') quotationId: number,
    @Body() sendQuotation: SendQuotationDto,
  ) {
    return this.serviceService.sendQuotation(
      req.user.id,
      quotationId,
      sendQuotation,
    );
  }

  @Get()
  @ApiOperation({ summary: 'Get all service Tags' })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'page', required: false, type: Number })
  async getAll(@Query('page') page = 1, @Query('limit') limit = 10) {
    return this.serviceService.getRecievedQuotations(page, limit);
  }

  @Patch(':id')
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Update is Read a quotation and recieved quotation',
  })
  async updateIsRead(
    @Request() req,
    @Param('id', ParseIntPipe) id: number,
    @Body() query: UpdateIsReadDto,
  ) {
    return this.serviceService.updateIsRead(id, req.user.id, query);
  }
}
