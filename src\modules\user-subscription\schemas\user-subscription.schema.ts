import {
  Table,
  Column,
  Model,
  ForeignKey,
  BelongsTo,
  DataType,
} from 'sequelize-typescript';
import { User } from 'src/modules/admin/authentication/schemas/user.schema';
import { Transaction } from './transaction.schema';
import { SubscriptionStatus } from 'src/common/utils/enum';
import { Package } from 'src/modules/subscription/schemas/package.schema';

export enum SubscriptionType {
  SINGLE = 'single',
  DUAL = 'dual',
}

@Table
export class UserSubscription extends Model {
  @Column
  subscriptionType: string;

  @Column
  packageType: string;

  @Column({
    type: DataType.ENUM,
    values: Object.values(SubscriptionStatus),
    defaultValue: SubscriptionStatus.ACTIVE,
  })
  status: string;

  @Column
  validTill: Date;

  @Column
  validFrom: Date;

  @Column({ defaultValue: false })
  isCanceled: boolean;

  @Column({ defaultValue: false })
  sevenDayReminderSent: boolean;

  @Column({ defaultValue: false })
  oneDayReminderSent: boolean;

  @Column({
    type: DataType.DECIMAL(10, 2),
  })
  totalAmount: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
  })
  discountAmount: number;

  @ForeignKey(() => Transaction)
  @Column
  transactionId: number;

  @BelongsTo(() => Transaction)
  transaction: Transaction;

  @ForeignKey(() => User)
  @Column
  userId: number;

  @BelongsTo(() => User)
  user: User;

  @ForeignKey(() => Package)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  packageId: number;

  @BelongsTo(() => Package)
  package: Package;
}
