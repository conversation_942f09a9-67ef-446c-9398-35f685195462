// // src/auth/strategies/google.strategy.ts
// import { PassportStrategy } from '@nestjs/passport';
// import { Strategy } from 'passport-google-oauth20';
// import { Injectable } from '@nestjs/common';

// @Injectable()
// export class GoogleStrategy extends PassportStrategy(Strategy, 'google') {
//   constructor() {
//     super({
//       clientID: process.env.GOOGLE_CLIENT_ID,
//       clientSecret: process.env.GOOGLE_CLIENT_SECRET,
//       callbackURL: 'http://localhost:3000/auth/google/callback',
//       scope: ['email', 'profile'],
//       passReqToCallback: true,
//     });
//   }

//   async validate(
//     req: any,
//     accessToken: string,
//     refreshToken: string,
//     profile: any,
//     // eslint-disable-next-line @typescript-eslint/ban-types
//     done: Function,
//   ) {
//     const { id, name, emails, photos } = profile;
//     const role = req.query.role || 'user';
//     // console.log('profile', profile);
//     // console.log('role', role);
//     const user = {
//       id,
//       email: emails[0].value,
//       firstName: name.givenName,
//       lastName: name.familyName,
//       picture: photos[0].value,
//       accessToken,
//       role,
//     };
//     done(null, user);
//   }
// }
