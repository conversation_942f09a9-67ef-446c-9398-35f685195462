import { Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';

@Injectable()
export class CommonService {
  private secretKey: string;
  private keysDir: string;
  constructor(private jwt: JwtService) {
    this.secretKey = process.env.JWT_SECRET;
  }
  generateToken = async (user: any): Promise<string> => {
    try {
      const tokenPayload: any = { _id: user.id, name: user.name };
      const options: any = {
        secret: process.env.SECRET_KEY,
        expiresIn: '45d',
      };
      const token = this.jwt.sign(tokenPayload, options);
      return token;
    } catch (err) {
      throw err;
    }
  };

  verifyToken = async (token: string) => {
    try {
      const decoded = await this.jwt.verify(token, {
        secret: process.env.SECRET_KEY,
      });
      return decoded;
    } catch (err) {
      throw new UnauthorizedException();
    }
  };
}
