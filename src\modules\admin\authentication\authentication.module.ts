import { Module } from '@nestjs/common';
import { AuthenticationController } from './authentication.controller';
import { AuthenticationService } from './authentication.service';
import { User } from './schemas/user.schema';
import { SequelizeModule } from '@nestjs/sequelize';
import { CommonService } from 'src/common/common.service';
import { ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
// import { Permission } from 'src/modules/sub-admin/permission.schema';
import { AdminRole } from '../schemas/admin-role.schemas';
import { UserRole } from 'src/modules/roles/schemas/usersRole.schema';
import { Role } from 'src/modules/roles/schemas/role.schema';

@Module({
  imports: [
    SequelizeModule.forFeature([User, AdminRole, UserRole, Role]),
    JwtModule.registerAsync({
      inject: [ConfigService],
      useFactory: (config: ConfigService) => {
        return {
          secret: config.get<string>('JWT_SECRT'),
          signOptions: { expiresIn: config.get<string | number>('JWT_EXPIRE') },
        };
      },
    }),
  ],
  controllers: [AuthenticationController],
  providers: [AuthenticationService, CommonService],
})
export class AuthenticationModule {}
