import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';

import { Category } from './schemas/category.schema';
import { User } from '../admin/authentication/schemas/user.schema';

@Injectable()
export class CategoryService {
  constructor(
    @InjectModel(Category)
    private categoryModel: typeof Category,
  ) {}

  async getCategory(query: any): Promise<Category> {
    return this.categoryModel.findOne({ where: query });
  }

  async getCategoriesWithCount(
    query: any,
  ): Promise<{ categories: Category[]; total: number }> {
    const { count, rows } = await this.categoryModel.findAndCountAll(query);

    return { categories: rows, total: count };
  }
  async getSubCategoriesWithCount(
    query: any,
    page: number,
    limit: number,
    userId?: number,
  ): Promise<{ categories: Category[]; total: number }> {
    const offset = (page - 1) * limit;

    const whereQuery: any = {
      where: query,
      include: [
        {
          model: Category,
          as: 'subCategories',
          include: userId
            ? [
                {
                  model: User,
                  as: 'userSubCategories',
                  where: { id: userId },
                  required: true,
                  attributes: [],
                  through: { attributes: [] },
                },
              ]
            : [],
        },
      ],
      attributes: ['id', 'name', 'parentId', 'roleId', 'createdAt'],
      order: [['createdAt', 'DESC']],
      offset,
      limit,
    };

    const { count, rows } =
      await this.categoryModel.findAndCountAll(whereQuery);

    return { categories: rows, total: count };
  }
}
