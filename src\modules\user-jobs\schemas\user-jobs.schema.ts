import {
  Column,
  Model,
  Table,
  ForeignKey,
  DataType,
  BelongsTo,
} from 'sequelize-typescript';
import { User } from 'src/modules/admin/authentication/schemas/user.schema';
import { Job } from 'src/modules/job/schemas/job.schema';

@Table
export class UserJobs extends Model<UserJobs> {
  @ForeignKey(() => User)
  @Column
  userId: number;

  @ForeignKey(() => Job)
  @Column
  jobId: number;

  @Column({
    type: DataType.STRING,
  })
  status: string;

  @Column
  isInterested: boolean;

  @BelongsTo(() => User)
  user: User;

  @ForeignKey(() => User)
  @Column
  acceptedById: number;

  @BelongsTo(() => User, { foreignKey: 'acceptedById', as: 'acceptedBy' })
  acceptedBy: User;

  @BelongsTo(() => Job)
  job: Job;
}
