import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { User } from '../admin/authentication/schemas/user.schema';
import { UserSubscriptionService } from './user-subscription.service';
import { Package } from '../subscription/schemas/package.schema';
import {
  OrderController,
  TransactionController,
  UserSubscriptionController,
} from './user-subscription.controller';
import { UserSubscription } from './schemas/user-subscription.schema';
import { Order } from './schemas/order.schema';
import { Transaction } from './schemas/transaction.schema';
import { PaymentService } from '../payment/payment.service';
import { ScheduleModule } from '@nestjs/schedule';
import { SendgridModule } from '../sendgrid/sendgrid.module';
import { SmsModule } from '../sms/sms.module';
import { NotificationModule } from '../notification/notification.module';

@Module({
  imports: [
    SequelizeModule.forFeature([
      User,
      Package,
      UserSubscription,
      Order,
      Transaction,
    ]),
    ScheduleModule.forRoot(),
    SendgridModule,
    SmsModule,
    NotificationModule,
  ],
  providers: [UserSubscriptionService, PaymentService],
  controllers: [
    UserSubscriptionController,
    OrderController,
    TransactionController,
  ],
})
export class UserSubscriptionModule {}
