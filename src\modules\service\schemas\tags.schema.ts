import {
  Table,
  Column,
  Model,
  DataType,
  BelongsToMany,
} from 'sequelize-typescript';
import { Service } from './service.schema';
import { ServiceTags } from './service-tags.schema';
import { User } from 'src/modules/admin/authentication/schemas/user.schema';
import { UserTags } from 'src/modules/user-management/schemas/user-tags.schema';

export enum ServiceTagStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

@Table
export class Tags extends Model {
  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  name: string;

  @Column({
    type: DataType.ENUM,
    values: Object.values(ServiceTagStatus),
    defaultValue: ServiceTagStatus.ACTIVE,
    allowNull: false,
  })
  status: ServiceTagStatus;

  @BelongsToMany(() => Service, () => ServiceTags)
  services: Service[];

  @BelongsToMany(() => User, () => UserTags)
  users: User[];
}
