import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
} from '@nestjs/common';
import { AdsManagerService } from './ads-manager.service';
import {
  AdsManagerDto,
  TrackImpressionDto,
  UpdateAdsStatusDto,
} from './dto/ads-manager.dto';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { AdsStatus } from './schemas/ads-manager.schema';
import { AdsDisplay } from './schemas/ads-display.schema';
import { AdsClick } from './schemas/ads-click.schema';
import { AdminGuard } from 'src/guards/admin/admin.guard';
import { AdminPermissions } from 'src/common/decorators/permissions.decorator';
import { Modules } from 'src/common/utils/enum';
import { JWTGuard } from 'src/guards/common/jwt.guard';
// import { SuperAdminAuthGuard } from 'lib/guards/super-admin-guard';

@ApiTags('AdsManager')
@Controller('ads-manager')
export class AdsManagerController {
  constructor(private readonly adsManagerService: AdsManagerService) {}

  @UseGuards(AdminGuard)
  @AdminPermissions(Modules.ADS_MANAGER)
  @ApiBearerAuth()
  @Post('/add')
  @ApiOperation({
    summary: 'AdsManager - Create a new Ads Manager entry with file upload',
  })
  @ApiResponse({
    status: 201,
    description: 'The subscription has been successfully created.',
  })
  async create(@Body() createAdsManagerData: AdsManagerDto) {
    return await this.adsManagerService.createAdsManager(createAdsManagerData);
  }

  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @Get()
  @ApiOperation({ summary: 'AdsManager - Get all Ads with pagination' })
  @ApiResponse({
    status: 200,
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'number' },
              title: { type: 'string' },
              description: { type: 'string' },
              slot: { type: 'string' },
              from_date: { type: 'date' },
              to_date: { type: 'date' },
              start_time: { type: 'string' },
              end_time: { type: 'string' },
              cta_external_link: { type: 'string' },
              criteria: { type: 'string' },
              target_location: { type: 'string' },
              file: { type: 'string', format: 'binary' },
            },
          },
        },
        example: [
          {
            created_at: '2024-07-09T06:29:16.182Z',
            updated_at: '2024-07-09T06:29:16.182Z',
            id: 1,
            title: 'test',
            description: 'test',
            slot: 'test',
            from_date: '2022-01-01',
            to_date: '2022-01-31',
            start_time: '01:00 AM',
            end_time: '23:59 PM',
            cta_external_link: 'https://www.blackbox.ai/chat/jb8Ap9G',
            criteria: 'https://www.blackbox.ai/chat/jb8Ap9G',
            target_location: 'https://www.blackbox.ai/chat/jb8Ap9G',
            file_url: 'iVBORw0KGgoAAAANSUhEUgAAAmUAAALrCAYAAAC/',
            status: 'play',
          },
        ],
      },
    },
  })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({
    name: 'status',
    enum: AdsStatus,
    required: false,
    description: 'Filter ads by status',
  })
  async findAll(
    @Request() req,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('search') search?: string,
    @Query('criteria') criteria?: string,
    @Query('status') status?: AdsStatus,
  ) {
    const userRole = req.user.roles[0].roleName;
    return this.adsManagerService.findAll(
      page,
      limit,
      search,
      status,
      userRole,
    );
  }

  @UseGuards(AdminGuard)
  @AdminPermissions(Modules.ADS_MANAGER)
  @ApiBearerAuth()
  @Get('/admin-ads')
  @ApiOperation({ summary: 'AdsManager - Get all Ads with pagination' })
  @ApiResponse({
    status: 200,
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'number' },
              title: { type: 'string' },
              description: { type: 'string' },
              slot: { type: 'string' },
              from_date: { type: 'date' },
              to_date: { type: 'date' },
              start_time: { type: 'string' },
              end_time: { type: 'string' },
              cta_external_link: { type: 'string' },
              criteria: { type: 'string' },
              target_location: { type: 'string' },
              file: { type: 'string', format: 'binary' },
            },
          },
        },
        example: [
          {
            created_at: '2024-07-09T06:29:16.182Z',
            updated_at: '2024-07-09T06:29:16.182Z',
            id: 1,
            title: 'test',
            description: 'test',
            slot: 'test',
            from_date: '2022-01-01',
            to_date: '2022-01-31',
            start_time: '01:00 AM',
            end_time: '23:59 PM',
            cta_external_link: 'https://www.blackbox.ai/chat/jb8Ap9G',
            criteria: 'https://www.blackbox.ai/chat/jb8Ap9G',
            target_location: 'https://www.blackbox.ai/chat/jb8Ap9G',
            file_url: 'iVBORw0KGgoAAAANSUhEUgAAAmUAAALrCAYAAAC/',
            status: 'play',
          },
        ],
      },
    },
  })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({
    name: 'status',
    enum: AdsStatus,
    required: false,
    description: 'Filter ads by status',
  })
  async findAllAds(
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('search') search?: string,
    @Query('status') status?: AdsStatus,
  ) {
    return this.adsManagerService.findAllAds(page, limit, search, status);
  }

  // @UseGuards(SuperAdminAuthGuard )
  @ApiBearerAuth()
  @Get(':id')
  @ApiResponse({
    status: 200,
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'number' },
              title: { type: 'string' },
              description: { type: 'string' },
              slot: { type: 'string' },
              from_date: { type: 'date' },
              to_date: { type: 'date' },
              start_time: { type: 'string' },
              end_time: { type: 'string' },
              cta_external_link: { type: 'string' },
              criteria: { type: 'string' },
              target_location: { type: 'string' },
              file: { type: 'string', format: 'binary' },
            },
          },
        },
        example: {
          created_at: '2024-07-09T06:29:16.182Z',
          updated_at: '2024-07-09T06:29:16.182Z',
          id: 1,
          title: 'test',
          description: 'test',
          slot: 'test',
          from_date: '2022-01-01',
          to_date: '2022-01-31',
          start_time: '01:00 AM',
          end_time: '23:59 PM',
          cta_external_link: 'https://www.blackbox.ai/chat/jb8Ap9G',
          criteria: 'https://www.blackbox.ai/chat/jb8Ap9G',
          target_location: 'https://www.blackbox.ai/chat/jb8Ap9G',
          file_url: 'iVBORw0KGgoAAAANSUhEUgAAAmUAAALrCAYAAAC/',
          status: 'play',
        },
      },
    },
  })
  @ApiOperation({ summary: 'AdsManager - Get a Ads by ID' })
  @ApiParam({ name: 'id', required: true, type: Number })
  findOne(@Param('id') id: string) {
    return this.adsManagerService.findOne(+id);
  }

  // @UseGuards(SuperAdminAuthGuard )
  @UseGuards(AdminGuard)
  @AdminPermissions(Modules.ADS_MANAGER)
  @ApiBearerAuth()
  @Patch(':id')
  @ApiOperation({
    summary:
      'AdsManager - update a existing Ads Manager entry with file upload',
  })
  @ApiResponse({
    status: 200,
    description: 'The Ads has been successfully Update.',
  })
  @ApiParam({ name: 'id', required: true, type: Number })
  update(@Param('id') id: number, @Body() updateAdsManagerData: AdsManagerDto) {
    return this.adsManagerService.update(id, updateAdsManagerData);
  }

  // @UseGuards(SuperAdminAuthGuard )
  @UseGuards(AdminGuard)
  @AdminPermissions(Modules.ADS_MANAGER)
  @ApiBearerAuth()
  @Delete(':id')
  @ApiParam({ name: 'id', required: true, type: Number })
  @ApiOperation({ summary: 'AdsManager - Delete a Ads by ID' })
  @ApiResponse({
    status: 200,
    description: 'The Ads has been successfully deleted.',
  })
  remove(@Param('id') id: string) {
    try {
      return this.adsManagerService.remove(+id);
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Internal server error',
      };
    }
  }

  // @UseGuards(SuperAdminAuthGuard )
  @UseGuards(AdminGuard)
  @AdminPermissions(Modules.ADS_MANAGER)
  @ApiBearerAuth()
  @Patch(':id/status')
  @ApiOperation({ summary: 'Update the status of an AdsManager' })
  @ApiParam({
    name: 'id',
    type: Number,
    description: 'ID of the AdsManager to update',
  })
  @ApiResponse({
    status: 200,
    description: 'AdsManager status successfully updated',
    schema: {
      example: { message: 'Ads status successfully updated.' },
    },
  })
  @ApiResponse({ status: 404, description: 'AdsManager not found' })
  @ApiResponse({ status: 400, description: 'Invalid status value' })
  async updateAdsStatus(
    @Param('id') id: number,
    @Body() updateStatusData: UpdateAdsStatusDto,
  ) {
    return this.adsManagerService.updateAdsStatus(id, updateStatusData);
  }

  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @Post('/track-impression')
  @ApiOperation({ summary: 'Track an impression for an ad' })
  @ApiResponse({
    status: 201,
    description: 'Impression tracked successfully',
    type: AdsDisplay,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async trackImpression(
    @Request() req,
    @Body() trackImpressionDto: TrackImpressionDto,
  ): Promise<AdsDisplay> {
    const userId = req.user.id;
    return this.adsManagerService.trackImpression(trackImpressionDto, userId);
  }

  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @Post('/track-click')
  @ApiOperation({ summary: 'Track a click for an ad' })
  @ApiResponse({
    status: 201,
    description: 'Click tracked successfully',
    type: AdsClick,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async trackClick(
    @Request() req,
    @Body() trackClickDto: TrackImpressionDto,
  ): Promise<AdsClick> {
    const userId = req.user.id;
    return this.adsManagerService.trackClick(trackClickDto.adsId, userId);
  }

  @Get(':id/performance')
  @ApiOperation({ summary: 'Get performance metrics for an ad' })
  @ApiResponse({
    status: 200,
    description: 'Ad performance data',
    schema: {
      example: {
        impressions: 500,
        clicks: 50,
        ctr: 10,
      },
    },
  })
  @ApiResponse({ status: 404, description: 'Ad not found' })
  async getAdPerformance(@Param('id') adId: number): Promise<any> {
    return this.adsManagerService.getAdPerformance(adId);
  }

  // @UseGuards(SuperAdminAuthGuard )
  // @ApiBearerAuth()
  // @Get('/search/ads')
  // @ApiOperation({ summary: 'ads-manager - Get All Ads data by Title,ToDate and FromDate ' })
  // @ApiQuery({ name: 'title', required: false, type: String,example:"test"})
  // @ApiQuery({ name: 'toDate', required: false, type: Date,example:'YYYY-MM-DD' })
  // @ApiQuery({ name: 'fromDate', required: false, type: Date,example:'YYYY-MM-DD'})
  // @ApiQuery({ name: 'limit', required: false, type: Number,example:10 })
  // @ApiQuery({ name: 'page', required: false, type: Number,example:1 })
  // @ApiResponse({
  //   status: 200,
  //   content: {
  //     'application/json': {
  //       schema: {
  //         type: 'array',
  //         items: {
  //           type: 'object',
  //           properties: {
  //             id: { type: 'number' },
  //             title: { type: 'string' },
  //             description: { type: 'string' },
  //             slot: { type: 'string' },
  //             from_date: { type: 'date' },
  //             to_date: { type: 'date' },
  //             start_time: { type: 'string' },
  //             end_time: { type: 'string' },
  //             cta_external_link: { type: 'string' },
  //             criteria: { type: 'string' },
  //             target_location: { type: 'string' },
  //             file: { type: 'string', format: 'binary' }
  //           },
  //         },
  //       },
  //       example: [
  //         {
  //           created_at: "2024-07-09T06:29:16.182Z",
  //           updated_at: "2024-07-09T06:29:16.182Z",
  //           id: 1,
  //           title: "test",
  //           description: "test",
  //           slot: "test",
  //           from_date: "2022-01-01",
  //           to_date: "2022-01-31",
  //           start_time: "01:00 AM",
  //           end_time: "23:59 PM",
  //           cta_external_link: "https://www.blackbox.ai/chat/jb8Ap9G",
  //           criteria: "https://www.blackbox.ai/chat/jb8Ap9G",
  //           target_location: "https://www.blackbox.ai/chat/jb8Ap9G",
  //           file_url: "iVBORw0KGgoAAAANSUhEUgAAAmUAAALrCAYAAAC/",
  //           status: "play"
  //         },
  //         {
  //           created_at: "2024-07-09T06:29:16.182Z",
  //           updated_at: "2024-07-09T06:29:16.182Z",
  //           id: 2,
  //           title: "test 5",
  //           description: "test 5",
  //           slot: "test 5",
  //           from_date: "2022-01-01",
  //           to_date: "2022-01-31",
  //           start_time: "01:00 AM",
  //           end_time: "23:59 PM",
  //           cta_external_link: "https://www.blackbox.ai/chat/jb8Ap9G",
  //           criteria: "https://www.blackbox.ai/chat/jb8Ap9G",
  //           target_location: "https://www.blackbox.ai/chat/jb8Ap9G",
  //           file_url: "iVBORw0KGgoAAAANSUhEUgAAAmUAAALrCAYAAAC/",
  //           status: "play"
  //         }
  //       ],
  //     },
  //   },
  // })
  // async search(
  //   @Query('title') title: string,
  //   @Query('toDate') toDate: Date,
  //   @Query('fromDate') fromDate: Date,
  //   @Query('limit') limit: number = 10,
  //   @Query('page') page: number = 1,
  // ) {
  //   try {
  //     const result = await this.adsManagerService.adsSearch(title, toDate, fromDate, limit, page);
  //     return {
  //       success: true,
  //       data: result,
  //     };
  //   } catch (error) {
  //     return {
  //       success: false,
  //       error: error.message || 'Internal server error',
  //     };
  //   }
  // }
}
