import { Controller, Get, Post, Body, Param, Query } from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { CreateRoleDto } from './dto/create-role.dto';
import { RoleService } from './roles.service';
import { Role } from './schemas/role.schema';
import { Op } from 'sequelize';

@ApiTags('Role')
@Controller('role')
export class RoleController {
  constructor(private readonly roleService: RoleService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new role' })
  @ApiResponse({
    status: 201,
    description: 'The role has been successfully created.',
    type: Role,
  })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  async createRole(@Body() createRoleDto: CreateRoleDto) {
    return this.roleService.create(createRoleDto);
  }

  @ApiOperation({ summary: 'Fetch role list' })
  @ApiResponse({ status: 200, description: 'Role fetched successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiQuery({ name: 'limit', required: false, type: String })
  @ApiQuery({ name: 'page', required: false, type: String })
  @Get()
  async getSkills(
    @Query('page') page = 1,
    @Query('limit') limit = 10,
  ): Promise<{ roles: Role[]; total: number }> {
    const query = {
      roleName: {
        [Op.notIn]: ['super_admin', 'admin'],
      },
    };
    return this.roleService.getRolesWithCount(query, page, limit);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a role by ID' })
  @ApiResponse({ status: 200, description: 'Return the role', type: Role })
  @ApiResponse({ status: 404, description: 'Role not found' })
  findOne(@Param('id') id: number): Promise<Role> {
    return this.roleService.findOne(id);
  }

  // @Put(':id')
  // @ApiOperation({ summary: 'Update a role by ID' })
  // @ApiResponse({ status: 200, description: 'The role has been successfully updated.', type: Role })
  // @ApiResponse({ status: 404, description: 'Role not found' })
  // update(@Param('id') id: number, @Body() updateRoleDto: UpdateRoleDto): Promise<Role> {
  //   return this.roleService.update(id, updateRoleDto);
  // }

  // @Delete(':id')
  // @HttpCode(HttpStatus.NO_CONTENT)
  // @ApiOperation({ summary: 'Delete a role by ID' })
  // @ApiResponse({ status: 204, description: 'The role has been successfully deleted.' })
  // @ApiResponse({ status: 404, description: 'Role not found' })
  // remove(@Param('id') id: number): Promise<void> {
  //   return this.roleService.remove(id);
  // }
}
