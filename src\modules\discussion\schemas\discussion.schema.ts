import {
  Table,
  Model,
  BelongsTo,
  Column,
  ForeignKey,
  DataType,
  CreatedAt,
  UpdatedAt,
} from 'sequelize-typescript';
import { User } from 'src/modules/admin/authentication/schemas/user.schema';
import { DiscussionType } from '../utils/enum';
import { UserJobBids } from 'src/modules/user-job-bids/schemas/user-job-bids.schema';
import { RequestQuotation } from 'src/modules/service/schemas/quotation-request.schema';

@Table
export class Discussion extends Model<Discussion> {
  @Column({
    type: DataType.ENUM,
    values: Object.values(DiscussionType),
  })
  type: DiscussionType;

  @Column
  message: string;

  @ForeignKey(() => User)
  @Column
  senderId: number;

  @BelongsTo(() => User, 'senderId')
  sender: User;

  @ForeignKey(() => User)
  @Column
  receiverId: number;

  @BelongsTo(() => User, 'receiverId')
  receiver: User;

  @ForeignKey(() => UserJobBids)
  @Column
  userJobBidId: number;

  @BelongsTo(() => UserJobBids, 'userJobBidId')
  userJobBid: UserJobBids;

  @ForeignKey(() => RequestQuotation)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  requestQuotationId: number;

  @BelongsTo(() => RequestQuotation, 'requestQuotationId')
  quotation: RequestQuotation;

  @CreatedAt
  @Column
  createdAt: Date;

  @UpdatedAt
  @Column
  updatedAt: Date;
}
