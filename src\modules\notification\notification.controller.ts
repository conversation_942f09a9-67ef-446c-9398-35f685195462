import {
  Controller,
  Post,
  Get,
  Param,
  Delete,
  Body,
  Query,
  Request,
  UseGuards,
  NotFoundException,
  Put,
} from '@nestjs/common';
import { NotificationService } from './notification.service';
import { ApiBearerAuth, ApiQuery, ApiTags } from '@nestjs/swagger';
import { JWTGuard } from 'src/guards/common/jwt.guard';
import { UserDeviceToken } from './schema/user-device-token.schema';
import { createUserDeviceTokenDto } from './dto/notification.dto';
// import { CreateNotificationDto } from './dto/notification.dto';

@ApiTags('Notifications')
@Controller('notifications')
export class NotificationController {
  constructor(private readonly notificationService: NotificationService) {}

  @Get('/message-notifications')
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  // @ApiQuery({ name: 'limit', required: false, type: String })
  // @ApiQuery({ name: 'page', required: false, type: String })
  async getMessageNotifications(
    @Request() req,
    // @Query('page') page = 1,
    // @Query('limit') limit = 10,
  ) {
    // console.log('paginations::::', page, limit);
    const notifications =
      await this.notificationService.getMessageNotifications(req.user.id);
    return {
      data: notifications,
    };
  }

  // @Post()
  // async postCreate(@Body() createNotificationDto: CreateNotificationDto) {
  //   const notification = await this.notificationService.createNotification(
  //     createNotificationDto,
  //   );
  //   return {
  //     statusCode: 200,
  //     data: notification,
  //     message: 'Notification Created',
  //   };
  // }

  @Post('/:id/mark-read')
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  async postNotificationMarkAsRead(@Param('id') id: number, @Request() req) {
    const result = await this.notificationService.markNotificationAsRead(
      id,
      req.user.id,
    );
    return {
      statusCode: 200,
      message: result.message,
    };
  }

  @Get('/unread-count')
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  async getUnreadCount(@Request() req) {
    const count = await this.notificationService.getUnreadCount(req.user.id);
    return {
      count: count,
    };
  }

  @Get()
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @ApiQuery({ name: 'limit', required: false, type: String })
  @ApiQuery({ name: 'page', required: false, type: String })
  async getNotifications(
    @Request() req,
    @Query('page') page = 1,
    @Query('limit') limit = 10,
  ) {
    const notifications = await this.notificationService.getNotifications(
      req.user.id,
      page,
      limit,
    );
    return {
      data: notifications,
    };
  }

  @Get('/:id')
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  async getNotificationById(@Param('id') id: number, @Request() req) {
    const notification = await this.notificationService.getNotificationById(
      id,
      req.user.id,
    );
    return {
      data: notification,
    };
  }

  @Delete('/:id')
  async postNotificationDelete(@Param('id') id: number) {
    const result = await this.notificationService.deleteNotification(id);
    return {
      statusCode: 200,
      message: result.message,
    };
  }

  /////////////////////    USER DVICE TOKEN    //////////////////////

  // @Get()
  // async getAll(@Query() query: any): Promise<UserDeviceToken[]> {
  //   return await this.notificationService.getAll(query);
  // }

  @Get('getToken')
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  async getToken(@Request() req): Promise<UserDeviceToken> {
    console.log('req.user.id', req.user.id);
    const token = await this.notificationService.getToken(req.user.id);
    if (!token) {
      throw new NotFoundException('Token not found');
    }
    return token;
  }

  @Post('createToken')
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  async createToken(
    @Request() req,
    @Body() body: createUserDeviceTokenDto,
  ): Promise<UserDeviceToken> {
    return await this.notificationService.createToken(body, req.user.id);
  }

  @Put('updateToken')
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  async updateToken(
    @Request() req,
    @Body() body: createUserDeviceTokenDto,
  ): Promise<UserDeviceToken> {
    const existingToken = await this.notificationService.getToken(req.user.id);
    if (!existingToken) {
      throw new NotFoundException('Token not found');
    }
    return await this.notificationService.updateToken(existingToken, body);
  }

  @Delete('/deleteToken')
  async deleteToken(@Request() req) {
    const existingToken = await this.notificationService.getToken(req.user.id);
    if (!existingToken) {
      throw new NotFoundException('Token not found');
    }
    await this.notificationService.deleteToken(existingToken);
  }
}
