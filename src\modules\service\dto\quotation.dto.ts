import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsEmail,
  isEmail,
  IsEnum,
  IsIn,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import {
  RequestedQuotationStatus,
  serviceType,
  UrgencyLevel,
} from 'src/common/utils/enum';

class DetailsDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  companyName: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  email: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  phoneNumber: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  address1: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  address2: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  state: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  city: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  pincode: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  country: string;
}

export class ProductDetailsDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty()
  @IsString()
  // @IsNotEmpty()
  description: string;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  productId: number;

  // @ApiProperty()
  // @IsNumber()
  // @IsNotEmpty()
  // receivedQuotationId: number;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  unitCost: number;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  quantity: number;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  amount: number;
}

export class SendQuotationDto {
  @ApiProperty({ type: DetailsDto })
  @IsObject()
  @IsNotEmpty()
  companyDetails: DetailsDto;

  @ApiProperty({ type: DetailsDto })
  @IsObject()
  @IsNotEmpty()
  billingDetails: DetailsDto;

  @ApiProperty({ type: [ProductDetailsDto] })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => ProductDetailsDto)
  productDetails: ProductDetailsDto[];
}

export class RequestQuotationDto {
  @ApiProperty()
  @IsString()
  @IsOptional()
  title: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  budget: string;

  @ApiProperty({
    example: 'Construction',
    description: 'The type of the request',
  })
  @IsString()
  @IsOptional()
  type: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  size: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  address1: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  address2: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  city: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  state: string;
  @ApiProperty()
  @IsString()
  @IsOptional()
  pincode: string;

  @ApiProperty()
  @IsEnum(serviceType)
  @IsOptional()
  services: serviceType;

  @ApiProperty()
  @IsString()
  @IsOptional()
  quantity: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  preferredBrands: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  fileUrl: string;

  @ApiProperty()
  @IsEnum(UrgencyLevel)
  @IsOptional()
  urgencyLevel: UrgencyLevel;

  @ApiProperty()
  @IsString()
  @IsOptional()
  instructions: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  fullName: string;

  @ApiProperty()
  @IsEmail()
  @IsOptional()
  email: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  phoneNumber: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  contactMethod: string;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  negotiation: boolean;

  @ApiProperty()
  @IsString()
  @IsOptional()
  quoteValid: string;

  @ApiProperty()
  @IsInt()
  @IsOptional()
  validTill: number;

  @ApiProperty()
  @IsInt()
  categoryId: number;

  @ApiProperty()
  @IsOptional()
  @IsInt()
  subCategoryId: number;

  // @IsInt()
  // requestedTo: number;

  // @IsInt()
  // @IsOptional()
  // serviceId: number;
}

export class UpdateQuotationStatusDto {
  @ApiProperty({
    enum: [
      RequestedQuotationStatus.ACCEPTED,
      RequestedQuotationStatus.REJECTED,
    ],
    description: 'The status of the quotation',
  })
  @IsEnum(
    [RequestedQuotationStatus.ACCEPTED, RequestedQuotationStatus.REJECTED],
    {
      message: 'Status must be either "accepted" or "rejected".',
    },
  )
  status: RequestedQuotationStatus;
}

export class UpdateIsReadDto {
  @ApiProperty({ enum: ['quotationRequest', 'quotationReceived'] })
  @IsIn(['quotationRequest', 'quotationReceived'], {
    message: "Type must be 'request' or 'received'",
  })
  type: string;
}
