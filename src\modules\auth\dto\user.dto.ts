import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsEnum, IsNotEmpty, IsOptional } from 'class-validator';

export enum Role {
  SUPER_ADMIN = 'super_admin',
  ADMIN = 'admin',
  BUYER = 'buyer',
  SELLER = 'seller',
  LABOUR = 'labour',
  CONTRACTOR = 'contractor',
  ARCHITECT = 'architect',
}
export class SocialLoginDTO {
  @ApiProperty({ type: String })
  @IsNotEmpty()
  token: string;

  @ApiProperty({ type: String })
  @IsNotEmpty()
  @IsEnum(Role)
  role: string;

  @ApiProperty({ type: String })
  @IsOptional()
  firstName: string;

  @ApiProperty({ type: String })
  @IsOptional()
  lastName: string;

  @ApiProperty({ type: String })
  @IsOptional()
  @IsEmail()
  email: string;

  @ApiProperty({ type: String })
  @IsOptional()
  profilePicture: string;

  @ApiProperty({ type: String })
  @IsOptional()
  country: string;

  @ApiProperty({ type: String })
  @IsOptional()
  countryCode: string;
}
