// upload.controller.ts

import {
  BadRequestException,
  Controller,
  Get,
  Param,
  Post,
  UploadedFile,
  UploadedFiles,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import { ApiBody, ApiConsumes, ApiTags } from '@nestjs/swagger';
import { UploadDTO, UploadsDTO, UploadsResponse } from './dto';
import { UploadService } from './upload.service';

@ApiTags('Upload')
@Controller('upload')
export class UploadController {
  constructor(private readonly uploadService: UploadService) {}

  @Post('file')
  @ApiConsumes('multipart/form-data')
  @ApiBody({ type: UploadDTO })
  @UseInterceptors(FileInterceptor('file'))
  uploadFile(@UploadedFile() file: Express.Multer.File) {
    return this.uploadService.uploadFile(file);
  }

  @Post('images')
  @ApiConsumes('multipart/form-data')
  @ApiBody({ type: UploadsDTO })
  @UseInterceptors(
    FilesInterceptor('files', 1, {
      limits: { fileSize: 10 * 1024 * 1024 }, // Limit each file to 10MB
      fileFilter: (req, file, callback) => {
        // Allow only image files (png, jpg, jpeg)
        if (!file.mimetype.match(/\/(jpg|jpeg|png)$/)) {
          return callback(new Error('Only image files are allowed!'), false);
        }
        callback(null, true);
      },
    }),
  ) // Limit to 10 files and file size 10MB each
  async uploadMultipleImages(@UploadedFiles() files: Express.Multer.File[]) {
    if (!files || files.length === 0) {
      throw new BadRequestException('No files uploaded');
    }

    return this.uploadService.uploadMultipleImages(files);
  }

  @Get(':key')
  getFileByKey(@Param('key') key: string) {
    return this.uploadService.getFileByKey(key);
  }
}
