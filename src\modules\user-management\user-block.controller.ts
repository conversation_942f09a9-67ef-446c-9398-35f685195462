import {
  Controller,
  Param,
  UseGuards,
  Req,
  ForbiddenException,
  Patch,
  Query,
  Get,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';

import { JWTGuard } from 'src/guards/common/jwt.guard';
import { UserBlockService } from './user-block.service';
import { UserBlock } from './schemas/user-blocks.schema';

@ApiTags('User Management')
@Controller('user-management')
@UseGuards(JWTGuard)
@ApiBearerAuth()
export class UserBlockController {
  constructor(private readonly userBlockService: UserBlockService) {}

  @Patch(':userId/block-unblock')
  @ApiOperation({ summary: 'Block a user' })
  @ApiResponse({ status: 201, description: 'User blocked successfully' })
  async blockUser(
    @Req() req,
    @Param('userId') userIdToBlock: number,
  ): Promise<{ message: string }> {
    const blockerId = req.user.id;
    if (blockerId === userIdToBlock) {
      throw new ForbiddenException('You cannot block yourself');
    }
    return this.userBlockService.blockUnblockUser(blockerId, userIdToBlock);
  }
  @Get('/users/blocked')
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @ApiQuery({ name: 'limit', required: false, type: String })
  @ApiQuery({ name: 'page', required: false, type: String })
  @ApiOperation({
    summary: 'Get user details',
  })
  async blockedUsers(
    @Req() req,
    @Query('limit') limit?: string,
    @Query('page') page?: string,
  ): Promise<{ userBlock: UserBlock[]; total: number }> {
    const limitValue = limit ? parseInt(limit, 10) : 10;
    const offsetValue = page ? parseInt(page, 10) : 1;
    return this.userBlockService.getBlockedUsers(
      req.user.id,
      limitValue,
      (offsetValue - 1) * limitValue,
    );
  }
}
