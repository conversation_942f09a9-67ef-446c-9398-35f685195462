import { Injectable } from '@nestjs/common';

import { InjectModel } from '@nestjs/sequelize';
import { Bids } from './schemas/bids.schema';
import {
  notificationMessage,
  notificationTitle,
  notificationType,
} from 'src/common/messages';
import { Job } from '../job/schemas/job.schema';
import { NotificationService } from '../notification/notification.service';
import { User } from '../admin/authentication/schemas/user.schema';
import { SendgridService } from '../sendgrid/sendgrid.service';
import { Email } from 'src/common/email-template';
import { Constants } from 'src/common/constants';
import { NotificationPreference } from '../user-management/schemas/notification-preferences.schema';
@Injectable()
export class BidsService {
  constructor(
    @InjectModel(Bids)
    private placedBidsModel: typeof Bids,
    @InjectModel(Job)
    private jobModel: typeof Job,
    @InjectModel(User)
    private userModel: typeof User,
    private readonly sendgridService: SendgridService,
    private readonly notificationService: NotificationService,
  ) {}

  async create(createUserJobsDto: any, jobId: number): Promise<Bids> {
    const result = await this.placedBidsModel.create(createUserJobsDto);
    const job = await this.jobModel.findOne({
      where: { id: jobId },
    });
    const data = {
      title: notificationTitle.NEW_BID,
      message: notificationMessage.NEW_BID,
      type: notificationType.NEW_BID,
      jobTitle: job.title,
      receiverId: job.userId,
      additional: {
        jobId: job.id,
      },
      prefrence: 'newBidRequest',
    };
    await this.notificationService.sendNotification(data);

    // const userDetails = await this.userModel.findByPk(job.userId, {
    //   include: [
    //     {
    //       model: NotificationPreference,
    //       as: 'notificationPreference',
    //       required: false,
    //     },
    //   ],
    // });
    // if (userDetails.email && userDetails.notificationPreference.newBidRequest) {
    //   await this.sendgridService.sendEmail(
    //     userDetails.email,
    //     'New Bid Request Notification',
    //     Email.getTemplate({
    //       title: Constants.Titles.NewBidRequest,
    //       userName: `${userDetails?.firstName} ${userDetails.lastName}`,
    //       children: `
    //           <p>
    //            A new bid is placed on your job. Log in to view it.
    //         </p>
    //           `,
    //     }),
    //   );
    // }

    return result;
  }

  async getAllWithCount(query: any): Promise<{ bids: Bids[]; total: number }> {
    const { count, rows } = await this.placedBidsModel.findAndCountAll(query);

    return { bids: rows, total: count };
  }
  async getOne(query: any): Promise<Bids> {
    return this.placedBidsModel.findOne(query);
  }
  async deleteOne(query: any) {
    return this.placedBidsModel.destroy(query);
  }
  async patchUpdateStatus(existingData: any, status: string): Promise<Bids> {
    existingData.status = status;
    return existingData.save();
  }
}
