import { Global, Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { AdminGuard } from './admin/admin.guard';
// import { VendorGuard } from './vendor/vendor.guard';
import { ConfigService } from '@nestjs/config';
import { SequelizeModule } from '@nestjs/sequelize';
import { User } from 'src/modules/admin/authentication/schemas/user.schema';
import { CommonService } from 'src/common/common.service';
import { AuthenticationModule } from 'src/modules/admin/authentication/authentication.module';
// import { Permission } from 'src/modules/sub-admin//permission.schema';

@Global()
@Module({
  imports: [
    AuthenticationModule,
    SequelizeModule.forFeature([User]),
    JwtModule.registerAsync({
      inject: [ConfigService],
      useFactory: (config: ConfigService) => {
        return {
          secret: config.get<string>('JWT_SECRT'),
          signOptions: { expiresIn: config.get<string | number>('JWT_EXPIRE') },
        };
      },
    }),
  ],
  providers: [AdminGuard, CommonService],
  exports: [AdminGuard, CommonService],
})
export class GuardsModule {}
