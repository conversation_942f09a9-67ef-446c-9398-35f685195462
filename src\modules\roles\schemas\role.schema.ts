import {
  BelongsToMany,
  Column,
  DataType,
  HasMany,
  Model,
  Table,
} from 'sequelize-typescript';
import { User } from 'src/modules/admin/authentication/schemas/user.schema';
import { UserRole } from './usersRole.schema';
import { Job } from 'src/modules/job/schemas/job.schema';

@Table
export class Role extends Model {
  @Column({
    type: DataType.STRING,
    unique: true,
  })
  roleName: string;

  @HasMany(() => UserRole)
  userRoles: UserRole[];

  @BelongsToMany(() => User, () => UserRole)
  users: User[];

  @HasMany(() => Job)
  products!: Job[];
}
