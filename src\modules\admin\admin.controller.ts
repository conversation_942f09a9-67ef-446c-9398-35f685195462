import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards,
  BadRequestException,
  Patch,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiCreatedResponse,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { AdminPermissions } from 'src/common/decorators/permissions.decorator';
import { Modules } from 'src/common/utils/enum';
import { AdminGuard } from 'src/guards/admin/admin.guard';
import { AdminService } from './admin.service';
import { AdminRoleDto } from './dto/admin-role.dto';
import { SubAdminDto } from './dto/sub-admin.dto';

@ApiTags('admin')
@Controller('admin')
export class AdminController {
  constructor(private readonly adminService: AdminService) {}
  @Post('/role')
  @UseGuards(AdminGuard)
  @AdminPermissions(Modules.ADMINS)
  @ApiOperation({ summary: 'Create a new admin role' })
  @ApiCreatedResponse({ description: 'Admin role create successfully' })
  @ApiBearerAuth()
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        roleName: {
          type: 'string',
          example: 'Admin',
          description: 'Role name',
        },
        description: {
          type: 'string',
          example: 'Admin role description',
          description: 'Role description',
        },
        admins: {
          type: 'boolean',
          example: true,
          description: 'admins',
        },
        dashboard: {
          type: 'boolean',
          example: 'true',
          description: 'dashboard ',
        },
        user_management: {
          type: 'boolean',
          example: true,
          description: 'admins',
        },
        ads_management: {
          type: 'boolean',
          example: 'true',
          description: 'ads management ',
        },
        content_management: {
          type: 'boolean',
          example: true,
          description: 'content management',
        },
        subscription_management: {
          type: 'boolean',
          example: 'true',
          description: 'subscription management ',
        },
      },
      required: ['roleName', 'description'],
    },
    description: 'Create admin role data',
  })
  createRole(@Body() createRoleDto: AdminRoleDto) {
    return this.adminService.createRole(createRoleDto);
  }

  @Get('/role')
  @UseGuards(AdminGuard)
  @AdminPermissions(Modules.ADMINS)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all admin roles' })
  @ApiResponse({ status: 200, description: 'List of admin roles' })
  @ApiQuery({
    name: 'page',
    type: Number,
    description: 'Page number',
    required: false,
  })
  @ApiQuery({
    name: 'limit',
    type: Number,
    description: 'Number of items per page',
    required: false,
  })
  @ApiQuery({
    name: 'roleName',
    type: String,
    description: 'Filter by role name',
    required: false,
  })
  findAllRole(
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('roleName') roleName: string = '',
  ) {
    return this.adminService.findAllRole(page, limit, roleName);
  }

  @Get('/role/:id')
  @UseGuards(AdminGuard)
  @AdminPermissions(Modules.ADMINS)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get an admin role by ID' })
  @ApiResponse({ status: 200, description: 'Admin role details' })
  @ApiParam({ name: 'id', type: String, description: 'Admin role ID' })
  findOneRole(@Param('id') id: number) {
    return this.adminService.findOneRole(id);
  }

  @Patch('/role/:id')
  @UseGuards(AdminGuard)
  @AdminPermissions(Modules.ADMINS)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update an admin role' })
  @ApiResponse({ status: 200, description: 'Admin role update successfully' })
  @ApiParam({ name: 'id', type: String, description: 'Admin role ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        roleName: { type: 'string', example: 'Admin' },
        description: { type: 'string', example: 'Admin role description' },
      },
    },
    description: 'Update admin role data',
  })
  updateRole(@Param('id') id: number, @Body() updateRoleDto: AdminRoleDto) {
    return this.adminService.updateRole(id, updateRoleDto);
  }

  @Delete('/role/:id')
  @UseGuards(AdminGuard)
  @AdminPermissions(Modules.ADMINS)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete an admin role' })
  @ApiResponse({ status: 200, description: 'Admin role deleted successfully' })
  @ApiParam({ name: 'id', type: String, description: 'Admin role ID' })
  removeRole(@Param('id') id: number) {
    return this.adminService.removeRole(id);
  }

  /////////////      sub-admin        ///////////

  @Post('/sub-admin')
  @UseGuards(AdminGuard)
  @AdminPermissions(Modules.ADMINS)
  @ApiOperation({ summary: 'Create a new sub-admin ' })
  @ApiCreatedResponse({ description: 'sub-Admin create successfully' })
  @ApiBearerAuth()
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        firstName: { type: 'string', description: 'SubAdmin firstName' },
        lastName: { type: 'string', description: 'SubAdmin lastName' },
        email: { type: 'string', description: 'SubAdmin email' },
        password: { type: 'string', description: 'SubAdmin password' },
        phoneNumber: { type: 'string', description: 'SubAdmin phoneNumber' },
        adminRoleId: { type: 'string', description: 'SubAdmin Role Id' },
      },
    },
    description: 'Create sub-admin data',
  })
  createSubAdmin(@Body() createSubAdminData: SubAdminDto) {
    return this.adminService.createSubAdmin(createSubAdminData);
  }

  @Get('/sub-admin')
  @UseGuards(AdminGuard)
  @AdminPermissions(Modules.ADMINS)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all sub-admin' })
  @ApiResponse({ status: 200, description: 'List of sub-admin' })
  @ApiQuery({
    name: 'page',
    type: Number,
    description: 'Page number',
    required: false,
  })
  @ApiQuery({
    name: 'limit',
    type: Number,
    description: 'Number of items per page',
    required: false,
  })
  @ApiQuery({
    name: 'name',
    type: String,
    description: 'Filter by SubAdmin name',
    required: false,
  })
  findAllSubAdmin(
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('name') name: string = '',
  ) {
    return this.adminService.findAllSubAdmin(page, limit, name);
  }

  @Get('/sub-admin/:id')
  @UseGuards(AdminGuard)
  @AdminPermissions(Modules.ADMINS)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get an sub-admin by ID' })
  @ApiResponse({ status: 200, description: 'Sub-Admin details' })
  @ApiParam({ name: 'id', type: String, description: 'Sub-Admin ID' })
  findOneSubAdmin(@Param('id') id: number) {
    return this.adminService.findOneSubAdmin(id);
  }

  @Patch('/sub-admin/:id')
  @UseGuards(AdminGuard)
  @AdminPermissions(Modules.ADMINS)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update an sub-admin' })
  @ApiResponse({ status: 200, description: 'Sub-Admin updated successfully' })
  @ApiParam({ name: 'id', type: String, description: 'sub-Admin ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        firstName: { type: 'string', description: 'SubAdmin firstName' },
        lastName: { type: 'string', description: 'SubAdmin lastName' },
        email: { type: 'string', description: 'SubAdmin email' },
        password: { type: 'string', description: 'SubAdmin password' },
        phoneNumber: { type: 'string', description: 'SubAdmin phoneNumber' },
        adminRoleId: { type: 'string', description: 'SubAdmin Role Id' },
      },
    },
    description: 'Update Sub-admin  data',
  })
  updateSubAdmin(@Param('id') id: number, @Body() updateRoleDto: SubAdminDto) {
    return this.adminService.updateSubAdmin(id, updateRoleDto);
  }

  @Delete('/sub-admin/:id')
  @UseGuards(AdminGuard)
  @AdminPermissions(Modules.ADMINS)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete an sub-admin' })
  @ApiResponse({
    status: 200,
    description: 'sub-Admin  sub deleted successfully',
  })
  @ApiParam({ name: 'id', type: String, description: 'Sub-Admin ID' })
  removeSubAdmin(@Param('id') id: number) {
    return this.adminService.removeSubAdmin(id);
  }

  @UseGuards(AdminGuard)
  @AdminPermissions(Modules.ADMINS)
  @Put('/:adminId/block')
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Block or Unblock admin',
  })
  async blockOrUnblockUser(
    @Param('adminId') adminId: number,
  ): Promise<{ message: string }> {
    console.log('adminnnn....', adminId);
    return this.adminService.blockOrUnblockUser(adminId);
  }

  @UseGuards(AdminGuard)
  @AdminPermissions(Modules.DASHBOARD)
  @ApiBearerAuth()
  @Get('/dashboard/subscriptionCount')
  @ApiQuery({
    name: 'city',
    type: String,
    required: false,
  })
  @ApiQuery({
    name: 'state',
    type: String,
    required: false,
  })
  @ApiQuery({
    name: 'fromDate',
    type: String,
    required: false,
  })
  @ApiQuery({
    name: 'toDate',
    type: String,
    required: false,
  })
  async getSubscriptionCount(
    @Query('fromDate') fromDate?: string, // Filter: From Date
    @Query('toDate') toDate?: string, // Filter: To Date
    @Query('city') city?: string, // Filter: City
    @Query('state') state?: string, // Filter: State
  ) {
    const filters = { fromDate, toDate, city, state };
    return await this.adminService.getSubscriptionCountByRole(filters);
  }
  @UseGuards(AdminGuard)
  @AdminPermissions(Modules.DASHBOARD)
  @ApiBearerAuth()
  @Get('/dashboard/count-by-role')
  @ApiQuery({
    name: 'city',
    type: String,
    required: false,
  })
  @ApiQuery({
    name: 'state',
    type: String,
    required: false,
  })
  @ApiQuery({
    name: 'fromDate',
    type: String,
    required: false,
  })
  @ApiQuery({
    name: 'toDate',
    type: String,
    required: false,
  })
  async getUsersCountAndRole(
    @Query('fromDate') fromDate?: string, // Filter: From Date
    @Query('toDate') toDate?: string, // Filter: To Date
    @Query('city') city?: string, // Filter: City
    @Query('state') state?: string, // Filter: State
  ) {
    const filters = { fromDate, toDate, city, state };
    return await this.adminService.getUserCountByRole(filters);
  }

  @UseGuards(AdminGuard)
  @AdminPermissions(Modules.DASHBOARD)
  @ApiBearerAuth()
  @Get('/dashboard/registration-graph')
  @ApiQuery({
    name: 'fromDate',
    type: String,
    required: true,
  })
  @ApiQuery({
    name: 'toDate',
    type: String,
    required: true,
  })
  async getUserRegistrationGraph(
    @Query('fromDate') fromDate?: string,
    @Query('toDate') toDate?: string,
  ) {
    return await this.adminService.getUserRegistrationGraph({
      fromDate,
      toDate,
    });
  }
  @UseGuards(AdminGuard)
  @AdminPermissions(Modules.DASHBOARD)
  @ApiBearerAuth()
  @Get('/dashboard/total-revenue')
  @ApiQuery({ name: 'fromDate', type: String, required: false })
  @ApiQuery({ name: 'toDate', type: String, required: false })
  async getTotalRevenue(
    @Query('fromDate') fromDate: string,
    @Query('toDate') toDate: string,
  ) {
    return this.adminService.getTotalRevenue(fromDate, toDate);
  }
  @UseGuards(AdminGuard)
  @AdminPermissions(Modules.USER_MANAGEMENT)
  @ApiBearerAuth()
  @Get('/users-list')
  @ApiQuery({ name: 'role', type: String, required: false })
  @ApiQuery({ name: 'limit', type: String, required: false })
  @ApiQuery({ name: 'page', type: String, required: false })
  @ApiQuery({ name: 'search', type: String, required: false })
  @ApiQuery({ name: 'startDate', type: String, required: false })
  @ApiQuery({ name: 'endDate', type: String, required: false })
  async getUsersList(
    @Query('role') role?: string,
    @Query('limit') limit?: string,
    @Query('page') page?: string,
    @Query('search') search?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    const limitValue = limit ? parseInt(limit, 10) : 10;
    const offsetValue = page ? parseInt(page, 10) : 1;
    const filters = {
      search,
      role,
      startDate,
      endDate,
    };
    return this.adminService.listUsers(
      limitValue,
      (offsetValue - 1) * limitValue,
      filters,
    );
  }

  // @Get('/user-count')
  // @ApiOperation({ summary: 'Admin - Count of Role and total subscription Revenue' })
  // async calculateRevenue(
  //   @Query('startDate') startDateStr: string,
  //   @Query('endDate') endDateStr: string,
  //   @Query('month') month: number,
  //   @Query('year') year: number,
  // ) {
  //   try {
  //     let startDate: Date | null = null;
  //     let endDate: Date | null = null;

  //     if (startDateStr) {
  //       startDate = new Date(startDateStr);
  //       if (isNaN(startDate.getTime())) {
  //         throw new Error('Invalid start date format');
  //       }
  //     }
  //     if (endDateStr) {
  //       endDate = new Date(endDateStr);
  //       if (isNaN(endDate.getTime())) {
  //         throw new Error('Invalid end date format');
  //       }
  //     }
  //     const data = await this.adminService.getRegistration(startDate, endDate, month, year);
  //     return data;
  //   } catch (error) {
  //     console.error(error);
  //     throw new HttpException('Failed to retrieve data', HttpStatus.INTERNAL_SERVER_ERROR);
  //   }
  // }

  // @Get('/user-count/date')
  // @ApiOperation({ summary: 'Admin - Retrieve multiple data' })
  // async getRegistrationWeekAndMonth(
  //   @Query('startDate') startDateStr: string,
  //   @Query('endDate') endDateStr: string,
  //   @Query('month') month: number,
  //   @Query('year') year: number,
  // ) {
  //   try {
  //     let startDate: Date | null = null;
  //     let endDate: Date | null = null;

  //     if (startDateStr) {
  //       startDate = new Date(startDateStr);
  //       if (isNaN(startDate.getTime())) {
  //         throw new Error('Invalid start date format');
  //       }
  //     }
  //     if (endDateStr) {
  //       endDate = new Date(endDateStr);
  //       if (isNaN(endDate.getTime())) {
  //         throw new Error('Invalid end date format');
  //       }
  //     }
  //     const data = await this.adminService.getRegistrationWeekAndMonth(startDate, endDate, month, year);
  //     return data;
  //   } catch (error) {
  //     console.error(error);
  //     throw new HttpException('Failed to retrieve data', HttpStatus.INTERNAL_SERVER_ERROR);
  //   }
  // }

  // @Get('/subscription-list')
  // @ApiOperation({ summary: 'Admin - Count of Role and total subscription ' })
  // async allUserSubscriptionList(@Query('page') page = 1, @Query('limit') limit = 10) {
  //   try {
  //     const data = await this.adminService.allUserSubscriptionList(page, limit);
  //     return data;
  //   } catch (error) {
  //     console.error(error);
  //     throw new HttpException('Failed to retrieve data', HttpStatus.INTERNAL_SERVER_ERROR);
  //   }
  // }

  // @Get('/subscribed/search/data/')
  // @ApiOperation({ summary: 'Admin - search subscription Data' })
  // async searchAllUserSubscriptionList(@Query('name') name, @Query('fromDate') fromDate, @Query('toDate') toDate, @Query('role') role, @Query('page') page = 1, @Query('limit') limit = 10) {
  //   try {
  //     let newFromDate: Date | null = null;
  //     let newToDate: Date | null = null;
  //     if (fromDate) {
  //       newFromDate = new Date(fromDate);
  //       if (isNaN(newFromDate.getTime())) {
  //         throw new Error('Invalid date format');
  //       }
  //     }
  //     if (toDate) {
  //       newToDate = new Date(toDate);
  //       if (isNaN(newToDate.getTime())) {
  //         throw new Error('Invalid date format');
  //       }
  //     }
  //     const data = await this.adminService.searchAllUserSubscriptionList(name, role, newFromDate, newToDate, page, limit);
  //     return data;
  //   } catch (error) {
  //     console.error(error);
  //     throw new HttpException('Failed to retrieve data', HttpStatus.INTERNAL_SERVER_ERROR);
  //   }
  // }

  // @Get('/subscribed/export-user/')
  // async getUserSubscriptions(
  //   @Query('name') name: string,
  //   @Query('role') role: string,
  //   @Query('fromDate') fromDate: Date,
  //   @Query('toDate') toDate: Date,
  //   @Query('page') page: number = 1,
  //   @Query('limit') limit: number = 10,
  //   @Res() res: Response,
  // ) {
  //   try {
  //     let newFromDate: Date | null = null;
  //     let newToDate: Date | null = null;
  //     if (fromDate) {
  //       newFromDate = new Date(fromDate);
  //       if (isNaN(newFromDate.getTime())) {
  //         throw new Error('Invalid date format');
  //       }
  //     }
  //     if (toDate) {
  //       newToDate = new Date(toDate);
  //       if (isNaN(newToDate.getTime())) {
  //         throw new Error('Invalid date format');
  //       }
  //     }
  //     const userData = await this.adminService.searchAllUserSubscriptionList(name, role, newFromDate, newToDate, page, limit);
  //     if (userData.data.length > 0) {
  //       const pdfBuffer = await this.PDFservice.generateUserSubscriptionPDF(userData.data);
  //       res.setHeader('Content-Disposition', 'attachment; filename=user_subscriptions.pdf');
  //       res.setHeader('Content-Type', 'application/pdf');
  //       res.send(pdfBuffer);
  //     } else {
  //       res.status(200).json(userData);
  //     }
  //   } catch (error) {
  //     console.error('Error retrieving data:', error);
  //     res.status(500).json({ error: 'Failed to retrieve data' });
  //   }
  // }
}
