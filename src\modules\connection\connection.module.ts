import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { ConnectionService } from './connection.service';
import { Connection } from './schemas/connection.schema';
import { ConnectionController } from './connection.controller';
import { User } from '../admin/authentication/schemas/user.schema';
import { UserRole } from '../roles/schemas/usersRole.schema';
import { Role } from '../roles/schemas/role.schema';
import { SendgridModule } from '../sendgrid/sendgrid.module';
import { NotificationModule } from '../notification/notification.module';

@Module({
  imports: [
    SequelizeModule.forFeature([Connection, User, UserRole, Role]),
    SendgridModule,
    NotificationModule,
  ],
  providers: [ConnectionService],
  controllers: [ConnectionController],
  // exports: [ConnectionService],
})
export class ConnectionModule {}
