type TemplateProps = {
  title: string;
  userName: string;
  children: string;
};
const getTemplate = (props: TemplateProps) => {
  return `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Welcome Email</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f7f7f7;
    }
    .email-container {
      max-width: 600px;
      margin: 0 auto;
      background: #ffffff;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      overflow: hidden;
    }
    .email-header {
      background: #4CAF50;
      color: white;
      padding: 20px;
      text-align: center;
      font-size: 24px;
    }
    .email-body {
      padding: 20px;
      color: #333333;
    }
    .email-footer {
      background: #f1f1f1;
      padding: 10px;
      text-align: center;
      font-size: 14px;
      color: #777777;
    }
       .otp {
        display: block;
        text-align: center;
        font-size: 24px;
        font-weight: bold;
        color: #4CAF50;
        margin: 20px 0;
      }
    .button {
      display: inline-block;
      background: #4CAF50;
      color: white;
      padding: 10px 20px;
      border-radius: 4px;
      text-decoration: none;
      font-size: 16px;
      margin-top: 20px;
    }
    .button:hover {
      background: #45a049;
    }
  </style>
</head>
<body>
  <div class="email-container">
    <div class="email-header">
    ${props?.title}
    </div>
    <div class="email-body">
      <p>Dear ${props?.userName},</p>
      ${props?.children}
      <p>Best regards,<br>The Apna Builder Team</p>
    </div>
    <div class="email-footer">
      &copy; 2025 Apna Builder. All rights reserved.
    </div>
  </div>
</body>
</html>
`;
};

export const Email = {
  getTemplate,
};
