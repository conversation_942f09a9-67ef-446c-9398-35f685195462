import { Injectable, OnModuleInit } from '@nestjs/common';
import { Category } from './schemas/category.schema';
import { categoriesData } from './utils/options';

@Injectable()
export class CategorySeedService implements OnModuleInit {
  async onModuleInit() {
    // const count = await Category.count();
    // if (count === 0) {
    //   await this.seedCategoriesAndSubcategories();
    // }
  }
  seedCategories = async () => {
    const topLevelCategories = categoriesData.map((category) => ({
      name: category.name,
      roleId: category.roleId,
    }));

    const insertedCategories = await Category.bulkCreate(topLevelCategories, {
      returning: true,
    });

    return insertedCategories;
  };

  seedSubcategories = async (insertedCategories) => {
    const subcategories = [];

    insertedCategories.forEach((category, index) => {
      const subcats = categoriesData[index].subCategory || [];
      subcats.forEach((subcat) => {
        subcategories.push({
          name: subcat.name,
          roleId: category.roleId,
          parentId: category.id,
        });
      });
    });

    await Category.bulkCreate(subcategories);
  };
  seedCategoriesAndSubcategories = async () => {
    try {
      const insertedCategories = await this.seedCategories();

      await this.seedSubcategories(insertedCategories);

      console.log(
        'Categories and subcategories have been seeded successfully.',
      );
    } catch (error) {
      console.error('Error seeding categories:', error);
    }
  };
}
