import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { JobsService } from './job.service';
import { JobsController } from './job.controllers';
import { Job } from './schemas/job.schema';
import { User } from '../admin/authentication/schemas/user.schema';
import { BiddingDetail } from './schemas/bidding-details.schema';
import { Role } from '../roles/schemas/role.schema';
import { ScheduleModule } from '@nestjs/schedule';
import { SendgridModule } from '../sendgrid/sendgrid.module';
import { NotificationModule } from '../notification/notification.module';
import { NotificationPreference } from '../user-management/schemas/notification-preferences.schema';
import { Notification } from '../notification/schema/notification.schema';

@Module({
  imports: [
    SequelizeModule.forFeature([
      Job,
      User,
      BiddingDetail,
      Role,
      NotificationPreference,
      Notification,
    ]),
    ScheduleModule.forRoot(),
    SendgridModule,
    NotificationModule,
  ],
  providers: [JobsService],
  controllers: [JobsController],
  exports: [JobsService],
})
export class JobsModule {}
