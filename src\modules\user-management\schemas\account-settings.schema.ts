import {
  Column,
  Model,
  Table,
  ForeignKey,
  BelongsTo,
  CreatedAt,
  UpdatedAt,
} from 'sequelize-typescript';
import { User } from 'src/modules/admin/authentication/schemas/user.schema';

@Table
export class AccountSettings extends Model<AccountSettings> {
  @ForeignKey(() => User)
  @Column
  userId: number;

  @BelongsTo(() => User)
  user: User;

  @Column({ allowNull: true })
  appLanguage: string;

  @Column({ allowNull: true })
  communicationLanguage: string;

  @Column({ allowNull: true })
  profileVisibility: string;

  @CreatedAt
  createdAt: Date;

  @UpdatedAt
  updatedAt: Date;
}
