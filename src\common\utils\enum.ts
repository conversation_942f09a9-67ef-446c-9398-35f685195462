export enum ReviewStatus {
  UNPUBLISHED = 'unpublished',
  PUBLISHED = 'published',
  BLOCK = 'block',
  UN_BLOCK = 'unblock',
  REPORT = 'reported',
}

export enum Actions {
  VIEW = 'view',
  EDIT = 'edit',
  ADD = 'add',
}

export enum Modules {
  ADMINS = 'admins',
  DASHBOARD = 'dashboard',
  USER_MANAGEMENT = 'user_management',
  ADS_MANAGER = 'ads_manager',
  SUBSCRIPTION_MANAGEMENT = 'subscription_management',
}
export enum JobStatus {
  PUBLISHED = 'published',
  UNPUBLISHED = 'unpublished',
  DELETED = 'deleted',
  CLOSED = 'closed',
}
export enum RequestedQuotationStatus {
  REQUESTED = 'requested',
  RECEIVED = 'received',
  ACCEPTED = 'accepted',
  REJECTED = 'rejected',
}
export enum UrgencyLevel {
  IMMEDIATE = 'immediate',
  FLEXIBLE = 'flexible',
  WITHIN_A_MONTH = 'within_a_month',
}

export enum serviceType {
  NEW_CONSTRUCTIION = 'new_construction',
  RENOVATION_SERVICES = 'renovation_services',
  REPAIR_AND_MAINTENANCE = 'repair_and_maintenance',
  WASTE_MANAGEMENT = 'waste_management',
  TRANSPORTATION_AND_LOGISTICS = 'transportation_and_logistics',
  QUALITY_CONTROL_AND_INSPECTION = 'quality_control and_inspection',
  INTERIOR_OR_EXTERIOR_SERVICES = 'interior_or_exterior_services',
  OTHERS = 'others',
}

export enum SubscriptionStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  EXPIRED = 'EXPIRED',
  CANCELLED = 'CANCELLED',
}

export enum PaymentMethod {
  VISA = 'VISA',
  MASTERCARD = 'MASTERCARD',
  UPI = 'UPI',
  PAYPAL = 'PAYPAL',
}
