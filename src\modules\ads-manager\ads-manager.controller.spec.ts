import { Test, TestingModule } from '@nestjs/testing';
import { AdsManagerController } from './ads-manager.controller';
import { AdsManagerService } from './ads-manager.service';

describe('AdsManagerController', () => {
  let controller: AdsManagerController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AdsManagerController],
      providers: [
        {
          provide: AdsManagerService,
          useValue: {},
        },
      ],
    }).compile();

    controller = module.get<AdsManagerController>(AdsManagerController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
