import {
  Column,
  DataType,
  ForeignKey,
  Model,
  Table,
  BelongsTo,
  HasOne,
  CreatedAt,
  UpdatedAt,
} from 'sequelize-typescript';
import { User } from 'src/modules/admin/authentication/schemas/user.schema';
import { Job } from 'src/modules/job/schemas/job.schema';
import { RequestQuotation } from 'src/modules/service/schemas/quotation-request.schema';

@Table
export class ReviewRating extends Model<ReviewRating> {
  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  review: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  rating: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  status: string;

  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  reviewerId: number;

  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  revieweeId: number;

  @Column({ defaultValue: false })
  isVerified: boolean;

  @BelongsTo(() => User, 'reviewerId')
  reviewer: User;

  @BelongsTo(() => User, 'revieweeId')
  reviewee: User;

  @ForeignKey(() => ReviewRating)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  parentId: number;

  @BelongsTo(() => ReviewRating, 'parentId')
  parentReview: ReviewRating;

  @HasOne(() => ReviewRating, {
    foreignKey: 'parentId',
    as: 'reply',
  })
  reply: ReviewRating;
  @ForeignKey(() => Job)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  jobId: number;

  @BelongsTo(() => Job, 'jobId')
  job: Job;

  @ForeignKey(() => RequestQuotation)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  quotationId: number;

  @BelongsTo(() => RequestQuotation, 'quotationId')
  quotation: RequestQuotation;

  @CreatedAt
  @Column
  createdAt: Date;

  @UpdatedAt
  @Column
  updatedAt: Date;
}
