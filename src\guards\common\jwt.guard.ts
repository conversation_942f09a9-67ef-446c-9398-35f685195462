import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { CommonService } from 'src/common/common.service';
import { User } from 'src/modules/admin/authentication/schemas/user.schema';
import { Role } from 'src/modules/roles/schemas/role.schema';
import { AccountSettings } from 'src/modules/user-management/schemas/account-settings.schema';
import { NotificationPreference } from 'src/modules/user-management/schemas/notification-preferences.schema';
// import { Permission } from 'src/modules/sub-admin/permission.schema';

@Injectable()
export class JWTGuard implements CanActivate {
  constructor(
    private readonly authService: CommonService,
    @InjectModel(User)
    private userRepository: typeof User,
  ) {}

  canActivate = async (context: ExecutionContext): Promise<any> => {
    try {
      const request = context.switchToHttp().getRequest();

      const token = this.extractTokenFromHeader(request);
      const verify = await this.authService.verifyToken(token);
      if (verify) {
        const result: User = await this.userRepository.findOne({
          where: {
            id: verify._id,
            isActive: true,
            isDeleted: false,
          },
          include: [
            {
              model: Role,
              as: 'roles',
              attributes: ['roleName'],
              through: { attributes: [] },
            },
            {
              model: NotificationPreference,
              as: 'notificationPreference',
              required: false,
            },
            {
              model: AccountSettings,
              as: 'accountSettings',
              required: false,
            },
            // {
            //   model: UserSubscription,
            //   as: 'subscription',
            //   // required : false,
            //   where: {
            //     status:SubscriptionStatus.ACTIVE
            //   },
            //   include: [
            //     {
            //       model: Package,
            //       as: 'package',
            //       include: [
            //         {
            //           model: PackageDetails,
            //           as: 'packageDetails',
            //         },
            //       ],
            //     },
            //   ],
            // },
          ],
        });
        if (!result) {
          throw new UnauthorizedException();
        }
        request.user = result;
        return true;
      } else {
        throw new UnauthorizedException();
      }
    } catch (err) {
      throw err;
    }
  };
  private extractTokenFromHeader(request: Request): string | undefined {
    const [type, token] = request.headers['authorization']?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
