import { IsArray, IsEnum, IsNotEmpty, IsN<PERSON>ber } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { SubscriptionType } from '../schemas/user-subscription.schema';

export class CreateOrderDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  packageId: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsEnum(SubscriptionType)
  subscriptionType: SubscriptionType;

  // @ApiProperty()
  // @IsNotEmpty()
  // @IsNumber()
  // totalAmount: number;

  // @ApiProperty()
  // @IsNotEmpty()
  // @IsNumber()
  // discountAmount: number;
}
