import {
  Table,
  Column,
  Model,
  DataType,
  BelongsToMany,
  HasMany,
  HasOne,
} from 'sequelize-typescript';
import { Order } from 'src/modules/user-subscription/schemas/order.schema';
import { UserSubscription } from 'src/modules/user-subscription/schemas/user-subscription.schema';
import { PackageDetails } from './packagePermission.schema';
export enum PackageType {
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  YEARLY = 'yearly',
  free = 'free',
  default = 'default',
}
export enum PackageStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

@Table
export class Package extends Model {
  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  title: string;

  @Column({
    type: DataType.ENUM,
    values: Object.values(PackageType),
    allowNull: false,
  })
  packageType: PackageType;

  @Column({
    type: DataType.FLOAT,
    allowNull: false,
  })
  price: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  role: string;

  @Column({
    type: DataType.ENUM,
    values: ['active', 'inactive'],
    allowNull: false,
    defaultValue: 'active',
  })
  status: string;

  @Column({
    type: DataType.FLOAT,
    allowNull: true,
    defaultValue: 0,
  })
  gst: number;

  @Column({
    type: DataType.FLOAT,
    allowNull: true,
    defaultValue: 0,
  })
  discountPercentage: number;

  @HasMany(() => Order)
  orders: Order[];

  @HasMany(() => UserSubscription)
  userSubscriptions: UserSubscription[];

  @HasOne(() => PackageDetails)
  packageDetails: PackageDetails;
}
