import { Injectable } from '@nestjs/common';
import { UserJobs } from './schemas/user-jobs.schema';
import { InjectModel } from '@nestjs/sequelize';
import { UserJobStatus } from './utils/enum';
import { Op } from 'sequelize';
import { JobsService } from '../job/job.service';
import { JobStatus } from 'src/common/utils/enum';
import { Connection } from '../connection/schemas/connection.schema';

@Injectable()
export class UserJobsService {
  constructor(
    @InjectModel(UserJobs)
    private userJobsModel: typeof UserJobs,
    @InjectModel(Connection)
    private connectionModel: typeof Connection,
    private jobService: JobsService,
  ) {}

  async bulkCreate(createUserJobsDto: any[]): Promise<UserJobs[]> {
    return this.userJobsModel.bulkCreate(createUserJobsDto);
  }
  async create(createUserJobsDto: any): Promise<UserJobs> {
    const result = await this.userJobsModel.create(createUserJobsDto);
    return result;
  }

  async getAllWithCount(
    query: any,
  ): Promise<{ userJobs: UserJobs[]; total: number }> {
    const { count, rows } = await this.userJobsModel.findAndCountAll(query);

    return { userJobs: rows, total: count };
  }
  async getOne(query: any): Promise<UserJobs> {
    return this.userJobsModel.findOne(query);
  }
  async getAll(query: any): Promise<UserJobs[]> {
    return this.userJobsModel.findAll(query);
  }
  async count(options: any): Promise<any> {
    return this.userJobsModel.count(options);
  }
  async getAllWithStatus(query: any): Promise<any[]> {
    return this.userJobsModel.findAll(query);
  }
  async deleteOne(query: any) {
    return this.userJobsModel.destroy(query);
  }
  async patchUpdateStatus(
    existingData: any,
    status: string,
    userId?: number,
  ): Promise<UserJobs> {
    console.log('existingData::::::', existingData.job);
    existingData.status = status;
    existingData.acceptedById = userId;
    if (status === UserJobStatus.ACCEPTED) {
      const connection = await this.connectionModel.findOne({
        where: {
          [Op.or]: [
            {
              requester_id: existingData.userId,
              receiver_id: existingData.job.userId,
            },
            {
              requester_id: existingData.job.userId,
              receiver_id: existingData.userId,
            },
          ],
        },
      });
      console.log('connection::::::', connection);
      if (connection) {
        if (connection.status === 'pending') {
          await this.connectionModel.update(
            { status: 'accepted' },
            {
              where: {
                id: connection.id,
              },
            },
          );
        }
      } else {
        await this.connectionModel.create({
          requester_id: existingData.userId,
          receiver_id: existingData.job.userId,
          status: 'accepted',
        });
      }
      await this.userJobsModel.update(
        {
          status: UserJobStatus.CLOSED,
        },
        {
          where: {
            jobId: existingData.jobId,
            status: [UserJobStatus.INTERESTED, UserJobStatus.REQUESTED],
            [Op.not]: {
              id: existingData.id,
            },
          },
        },
      );
      await this.jobService.updateJobStatus(
        existingData.jobId,
        JobStatus.CLOSED,
      );
    }
    return existingData.save();
  }
}
