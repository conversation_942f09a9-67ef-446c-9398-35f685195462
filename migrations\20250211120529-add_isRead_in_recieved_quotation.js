'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
   await queryInterface.addColumn('RecievedQuotations', 'isRead', {
     type: Sequelize.BOOLEAN,
     allowNull: false,
     defaultValue: false,
   });
   await queryInterface.addColumn('RequestQuotations', 'isRead', {
     type: Sequelize.BOOLEAN,
     allowNull: false,
     defaultValue: false,
   });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('RecievedQuotations', 'isRead');
    await queryInterface.removeColumn('RequestQuotations', 'isRead');
  
  }
};
