import { ApiProperty } from '@nestjs/swagger';
import {
  IsEmail,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';
import { UserJobBidType } from '../utills/enum';

export class BidPlaceDto {
  @ApiProperty({ type: String, description: 'enter type', required: true })
  @IsString()
  @IsNotEmpty()
  @IsEnum(UserJobBidType)
  type: string;

  @ApiProperty({ type: Number, description: 'enter job id', required: true })
  @IsInt()
  jobId: number;

  // @ApiProperty({ type: Number, description: 'enter job id', required: false })
  // @IsInt()
  // userJobBidId: number;

  @ApiProperty({ type: String })
  @IsOptional()
  firstName: string;

  @ApiProperty({ type: String })
  @IsOptional()
  lastName: string;

  @ApiProperty({ type: String })
  @IsOptional()
  contactNumber: string;

  @ApiProperty({ type: String })
  @IsOptional()
  @IsEmail()
  email: string;

  @ApiProperty({ type: Number })
  @IsOptional()
  @IsNumber()
  amount: number;

  @ApiProperty({ type: String })
  @IsOptional()
  workCompletionDurationInDays: string;

  @ApiProperty({ type: String })
  @IsOptional()
  description: string;
}
