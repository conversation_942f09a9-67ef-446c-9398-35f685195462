import {
  Table,
  Column,
  Model,
  ForeignKey,
  CreatedAt,
  UpdatedAt,
  BelongsTo,
} from 'sequelize-typescript';
import { User } from 'src/modules/admin/authentication/schemas/user.schema';

@Table({ tableName: 'connections' })
export class Connection extends Model<Connection> {
  @ForeignKey(() => User)
  @Column
  requester_id: number;

  @ForeignKey(() => User)
  @Column
  receiver_id: number;

  @Column
  status: string;

  @Column({ defaultValue: false })
  isVerified: boolean;

  @CreatedAt
  created_at: Date;

  @UpdatedAt
  updated_at: Date;
  @BelongsTo(() => User, 'requester_id')
  requester: User;

  @BelongsTo(() => User, 'receiver_id')
  receiver: User;
}
