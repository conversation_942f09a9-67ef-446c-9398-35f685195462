import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsIn,
} from 'class-validator';
import { AdsStatus } from '../schemas/ads-manager.schema';

export class AdsManagerDto {
  @ApiProperty()
  @IsOptional()
  @IsString()
  title?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Slot value, can only be one of the strings: 1, 2, 3, 4, 5',
    enum: ['1', '2', '3', '4', '5'],
  })
  @IsOptional()
  @IsString()
  @IsIn(['1', '2', '3', '4', '5'], {
    message:
      'Slot must be one of the following values: "1", "2", "3", "4", "5".',
  })
  slot?: string;

  @ApiProperty()
  @IsOptional()
  fromDate?: Date;

  @ApiProperty()
  @IsOptional()
  toDate?: Date;

  @ApiProperty()
  @IsOptional()
  @IsString()
  endTime?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  startTime?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  ctaExternalLink?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  criteria?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  targetLocation?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  file_url?: string;
}

export class UpdateAdsStatusDto {
  @ApiProperty({
    description: 'The new status for the AdsManager',
    enum: AdsStatus,
    example: AdsStatus.PLAY,
  })
  @IsOptional()
  @IsEnum(AdsStatus)
  status?: AdsStatus;
}

export class TrackImpressionDto {
  @ApiProperty()
  @IsInt()
  @IsNotEmpty()
  adsId: number;
}
