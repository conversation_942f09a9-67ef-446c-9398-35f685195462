import { ConflictException, Injectable } from '@nestjs/common';
import * as sgMail from '@sendgrid/mail';
import { ConfigService } from '@nestjs/config';
import { Constants } from 'src/common/constants';

@Injectable()
export class SendgridService {
  constructor(private readonly configService: ConfigService) {
    const sendgridApiKey = this.configService.get<string>('SENDGRID_API_KEY');
    if (!sendgridApiKey) {
      throw new Error(
        'SENDGRID_API_KEY is not defined in environment variables',
      );
    }
    sgMail.setApiKey(sendgridApiKey);
  }

  async sendEmail(to: string, subject: string, content: string) {
    const fromEmail = this.configService.get<string>('SENDGRID_FROM_EMAIL');
    if (!fromEmail) {
      throw new Error(
        'SENDGRID_FROM_EMAIL is not defined in environment variables',
      );
    }

    const msg = {
      to,
      from: fromEmail, // Use your verified SendGrid sender email
      subject,
      text: content,
      html: `<p>${content}</p>`,
    };

    try {
      const nodeEnv = this.configService.get<string>('NODE_ENV');
      if (nodeEnv && [Constants.NodeEnv.MAIN].includes(nodeEnv)) {
        await sgMail.send(msg);
        console.log('Email sent successfully');
      }
    } catch (error) {
      console.error(
        'Error sending email:',
        error?.response?.body?.errors?.[0]?.message || error.message,
      );
      throw new ConflictException(
        error?.response?.body?.errors?.[0]?.message || 'Failed to send email',
      );
    }
  }
}
