import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import { Op } from 'sequelize';
import { User } from '../admin/authentication/schemas/user.schema';
import { NotificationPreference } from './schemas/notification-preferences.schema';
import { Category } from '../ category/schemas/category.schema';
import { SendgridService } from '../sendgrid/sendgrid.service';
import { Email } from 'src/common/email-template';
import { Constants } from 'src/common/constants';
import {
  notificationTitle,
  notificationMessage,
  notificationType,
} from 'src/common/messages';
import { NotificationService } from '../notification/notification.service';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';

@Injectable()
export class ProfileCompletionCron {
  constructor(
    @InjectModel(User)
    private readonly userRepository: typeof User,
    private readonly sendgridService: SendgridService,
    private readonly notificationService: NotificationService,
  ) {}

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async checkIncompleteProfiles() {
    console.log(`Running profile completion check cron job ${new Date()}`);
    console.log('Note: Excluding <EMAIL> from notifications');
    console.log(
      `<<<<<<---------------------------------------------------------->>>>>>`,
    );

    // Get the current time
    const currentTime = new Date();

    // Calculate the time exactly 24 hours ago
    const twentyFourHoursAgo = new Date(
      currentTime.getTime() - 24 * 60 * 60 * 1000,
    );

    console.log('twentyFourHoursAgo....', twentyFourHoursAgo);
    console.log('currentTime....', currentTime);

    // Find users who registered more than 24 hours ago and haven't received a notification yet
    // <NAME_EMAIL> from notifications
    const recentUsers = await this.userRepository.findAll({
      // attributes: ['id', 'email', 'phoneNumber', 'profileCompletionNotificationSent'],
      // raw: true,
      where: {
        isDeleted: false,
        profileCompletionNotificationSent: false,
        // Handle both null emails and non-admin emails
        [Op.or]: [
          { email: null }, // Include users with null email
          { email: { [Op.ne]: '<EMAIL>' } }, // Exclude <EMAIL>
        ],
        createdAt: {
          [Op.lte]: twentyFourHoursAgo, // Users who registered more than 24 hours ago
        },
      },
      include: [
        {
          model: NotificationPreference,
          as: 'notificationPreference',
          required: false,
        },
        {
          model: Category,
          through: {
            attributes: [],
          },
          as: 'categories',
          required: false,
        },
      ],
    });

    console.log(
      `Found ${recentUsers.length} users registered more than 24 hours ago who haven't been notified yet`,
    );

    for (const user of recentUsers) {
      // Check if profile is incomplete based on criteria
      const isProfileIncomplete = await this.isProfileIncomplete(user);
      console.log('isProfileIncomplete', isProfileIncomplete);

      if (isProfileIncomplete) {
        // Determine onboarding method (email or phone)
        const onboardedViaEmail = user.email && !user.phoneNumber;

        // Get profile completion status
        const hasBothEmailAndPhone = user.email && user.phoneNumber;
        const hasCategory = user.categories && user.categories.length > 0;

        // Log user details for debugging
        console.log(
          `<<<----------------------------------------------------------------->>>`,
        );
        console.log(
          `Processing user: ID=${user.id}, Name=${user.firstName} ${user.lastName}`,
        );
        console.log(
          `  Email: ${user.email || 'Not provided'}, Phone: ${user.phoneNumber || 'Not provided'}`,
        );
        console.log(
          `  Categories: ${user.categories?.length || 0}, Onboarded via Email: ${onboardedViaEmail}`,
        );
        console.log(
          `  Profile incomplete reason: Email+Phone=${!hasBothEmailAndPhone}, Categories=${!hasCategory}`,
        );
        console.log(
          `  Notification preferences - InApp: ${!!user.notificationPreference?.inAppNotifications}, Email: ${!!user.notificationPreference?.email}`,
        );
        console.log(`  User created at: ${user.createdAt}`);

        // Send notification based on onboarding method
        if (onboardedViaEmail) {
          // Check if user has device tokens for potential push notifications
          const deviceTokens = await this.notificationService.getAll({
            userId: user.id,
          });

          console.log(`  Sending EMAIL notification to: ${user.email}`);
          console.log(
            `  User has ${deviceTokens.length} device token(s) (for future push notifications)`,
          );

          // Send email notification if onboarded via email
          try {
            await this.sendgridService.sendEmail(
              user.email,
              'Complete Your Profile',
              Email.getTemplate({
                title: Constants.Titles.CompleteYourProfile,
                userName: `${user.firstName} ${user.lastName}`,
                children: `
                  <p>We noticed you haven't completed your profile. Complete it now to unlock all features.</p>
                `,
              }),
            );
            // Mark notification as sent
            await user.update({ profileCompletionNotificationSent: true });
            console.log(
              `  ✅ Email notification sent successfully and flag updated`,
            );
          } catch (error) {
            console.log('  ❌ Error sending email notification:', error);
          }
        } else if (!onboardedViaEmail) {
          // Check if user has device tokens for push notifications
          const deviceTokens = await this.notificationService.getAll({
            userId: user.id,
          });

          console.log(
            `  Sending IN-APP and PUSH notification to user with phone: ${user.phoneNumber}`,
          );
          console.log(
            `  User has ${deviceTokens.length} device token(s) for push notifications`,
          );

          // Send in-app and push notification if onboarded via phone number
          // The NotificationService will handle both in-app and push notifications for UPDATE_PROFILE type
          const data = {
            title: notificationTitle.UPDATE_PROFILE,
            message: notificationMessage.UPDATE_PROFILE,
            type: notificationType.UPDATE_PROFILE,
            receiverId: user.id,
            prefrence: 'inAppNotifications',
          };

          try {
            await this.notificationService.sendNotification(data);
            // Mark notification as sent
            await user.update({ profileCompletionNotificationSent: true });
            console.log(
              `  ✅ In-app and push notification sent successfully and flag updated`,
            );

            if (deviceTokens.length === 0) {
              console.log(
                `  ⚠️ Note: User has no device tokens, so push notification may not be delivered`,
              );
            }
          } catch (error) {
            console.log('  ❌ Error sending notification:', error);
          }
        } else {
          console.log(
            `  ⚠️ No notification sent - User doesn't meet notification preference criteria`,
          );
          console.log(
            `  Email notifications enabled: ${!!user.notificationPreference?.email}`,
          );
          console.log(
            `  In-app notifications enabled: ${!!user.notificationPreference?.inAppNotifications}`,
          );
        }
      }
    }
  }

  /**
   * Check if a user's profile is incomplete based on the criteria
   * @param user The user to check
   * @returns boolean indicating if the profile is incomplete
   */
  private async isProfileIncomplete(user: User): Promise<boolean> {
    // Check if user has both email and phone number
    const hasBothEmailAndPhone = user.email && user.phoneNumber;

    // Check if user has selected at least one category
    const hasCategory = user.categories && user.categories.length > 0;

    // Profile is incomplete if:
    // 1. User doesn't have both email and phone number, OR
    // 2. User doesn't have at least one category selected
    return !hasBothEmailAndPhone || !hasCategory;
  }
}
