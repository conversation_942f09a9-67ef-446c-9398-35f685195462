/* eslint-disable prettier/prettier */
import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Put,
  Query,
  Request,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiQuery,
  <PERSON>piT<PERSON>s,
  ApiParam,
} from '@nestjs/swagger';
import { SubscriptionService } from '../subscription/subscription.service';
import { UserSubscriptionService } from './user-subscription.service';
import { CreateOrderDto } from './dto/order.dto';
import { PaymentDto } from './dto/payment.dto';
import { JWTGuard } from 'src/guards/common/jwt.guard';
import { AdminGuard } from 'src/guards/admin/admin.guard';
import { AdminPermissions } from 'src/common/decorators/permissions.decorator';
import { Modules } from 'src/common/utils/enum';

@ApiTags('User-Subscription')
@Controller('User-Subscription')
export class UserSubscriptionController {
  constructor(
    private readonly userSubscriptionService: UserSubscriptionService,
  ) {}

  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @Get('/by-user')
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'limit', required: false, type: String })
  @ApiQuery({ name: 'page', required: false, type: String })
  async getSubscriptionByUserId(
    @Request() req,
    @Query('page') page = 1,
    @Query('limit') limit = 10,
  ) {
    const userId = req.user.id;
    return this.userSubscriptionService.getSubscriptionByUserId(
      userId,
      page,
      limit,
    );
  }

  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @Get()
  async getAllSubscriptions(@Request() req) {
    return this.userSubscriptionService.getAllSubscriptions(req.user.id);
  }

  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @Get(':id')
  async getOneSubscription(@Request() req, @Param('id') id: number) {
    return this.userSubscriptionService.getOneSubscription(req.user.id, id);
  }

  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @Post('createSubscription')
  async processPayment(@Request() req, @Body() paymentDto: CreateOrderDto) {
    // const dto = paymentDto;
    return await this.userSubscriptionService.createSubscription(
      req.user,
      paymentDto,
    );
  }

  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @Patch('/:id/cancel')
  async cancelSubscription(
    @Request() req,
    @Param('id', ParseIntPipe) id: number,
  ) {
    // const dto = paymentDto;
    console.log('id:::', id);
    const result = this.userSubscriptionService.cancelSubscription(
      req.user.id,
      id,
    );
    return result;
  }
}

@ApiTags('transactions')
@Controller('transactions')
export class TransactionController {
  constructor(private readonly transactionService: UserSubscriptionService) {}

  @UseGuards(AdminGuard)
  @AdminPermissions(Modules.SUBSCRIPTION_MANAGEMENT)
  @ApiBearerAuth()
  @Get()
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'limit', required: false, type: String })
  @ApiQuery({ name: 'page', required: false, type: String })
  async getAllTransactions(
    @Request() req,
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Query('search') search,
  ) {
    return this.transactionService.getAllTransactions(page, limit, search);
  }

  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @Get(':id')
  async getOneTransaction(@Request() req, @Param('id') id: number) {
    return this.transactionService.getOneTransaction(req.user.id, id);
  }
}

@ApiTags('orders')
@Controller('orders')
export class OrderController {
  constructor(private readonly orderService: UserSubscriptionService) {}

  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @Get()
  async getAllOrders(@Request() req) {
    return this.orderService.getAllOrders(req.user.id);
  }

  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @Get(':id')
  async getOneOrder(@Request() req, @Param('id') id: number) {
    return this.orderService.getOneOrder(req.user.id, id);
  }

  // @Post(':userId/create-order')
  // async createOrder(
  //   @Param('userId') userId: number,
  //   @Body() payload: CreateOrderDto,
  // ) {
  //   return this.orderService.createOrder(userId, payload);
  // }
}
