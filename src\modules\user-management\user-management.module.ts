import { Module } from '@nestjs/common';
import { UserManagementService } from './user-management.service';
import { UserManagementController } from './user-management.controller';
import { SequelizeModule } from '@nestjs/sequelize';
import { User } from '../admin/authentication/schemas/user.schema';
import { Connection } from '../connection/schemas/connection.schema';
import { UserRole } from '../roles/schemas/usersRole.schema';
import { Role } from '../roles/schemas/role.schema';
import { UserSkills } from './schemas/user-skills.schema';
import { Skill } from '../skill/schemas/skill.schema';
import { SkillService } from '../skill/skill.service';
import { UserJobs } from 'src/modules/user-jobs/schemas/user-jobs.schema';
import { Job } from '../job/schemas/job.schema';
import { JobsService } from '../job/job.service';
import { BiddingDetail } from '../job/schemas/bidding-details.schema';
import { UserSubCategories } from './schemas/user-categories.schema';
import { UserCategories } from './schemas/user-subcategories.schema';
import { AccountSettings } from './schemas/account-settings.schema';
import { NotificationPreference } from './schemas/notification-preferences.schema';
import { UserBlock } from './schemas/user-blocks.schema';
import { UserBlockController } from './user-block.controller';
import { UserBlockService } from './user-block.service';
import { NotificationModule } from '../notification/notification.module';
import { FirebaseModule } from '../firebase/firbase.module';
import { Service } from '../service/schemas/service.schema';
// import sequelize from 'sequelize';
import { UserSubscriptionModule } from '../user-subscription/user-subscription.module';
import { Package } from '../subscription/schemas/package.schema';
import { UserSubscription } from '../user-subscription/schemas/user-subscription.schema';
import { SendgridModule } from '../sendgrid/sendgrid.module';
import { SmsModule } from '../sms/sms.module';
import { UserTags } from './schemas/user-tags.schema';
import { ProfileCompletionCron } from './profile-completion-cron';

@Module({
  imports: [
    SequelizeModule.forFeature([
      User,
      Connection,
      UserRole,
      Role,
      UserSkills,
      Skill,
      Job,
      Service,
      UserJobs,
      UserTags,
      BiddingDetail,
      UserCategories,
      UserSubCategories,
      AccountSettings,
      NotificationPreference,
      UserBlock,
      Package,
      UserSubscription,
    ]),
    SendgridModule,
    NotificationModule,
    FirebaseModule,
    UserSubscriptionModule,
    SmsModule,
  ],
  providers: [
    UserManagementService,
    SkillService,
    JobsService,
    SendgridModule,
    UserBlockService,
    ProfileCompletionCron,
  ],
  controllers: [UserManagementController, UserBlockController],
  exports: [UserManagementService, UserBlockService],
})
export class UserManagementModule {}
