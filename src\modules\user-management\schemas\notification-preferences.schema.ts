import {
  Column,
  Model,
  Table,
  ForeignKey,
  BelongsTo,
  CreatedAt,
  UpdatedAt,
} from 'sequelize-typescript';
import { User } from 'src/modules/admin/authentication/schemas/user.schema';

@Table
export class NotificationPreference extends Model<NotificationPreference> {
  @ForeignKey(() => User)
  @Column
  userId: number;

  @BelongsTo(() => User)
  user: User;

  @Column({ defaultValue: true })
  jobAlerts: boolean;

  @Column({ defaultValue: true })
  newQuotation: boolean;

  @Column({ defaultValue: true })
  newBidRequest: boolean;

  @Column({ defaultValue: true })
  inAppNotifications: boolean;

  @Column({ defaultValue: true })
  sms: boolean;

  @Column({ defaultValue: true })
  email: boolean;

  @CreatedAt
  createdAt: Date;

  @UpdatedAt
  updatedAt: Date;
}
