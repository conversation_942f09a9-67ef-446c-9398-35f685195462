import {
  <PERSON>NotEmpty,
  <PERSON><PERSON>tring,
  <PERSON><PERSON><PERSON>ber,
  <PERSON><PERSON><PERSON>al,
  IsBoolean,
} from 'class-validator';
import { PackageType } from '../schemas/package.schema';
import { ApiProperty } from '@nestjs/swagger';

export class CreateSubscriptionDto {
  @ApiProperty()
  @IsOptional()
  @IsString()
  title?: string;

  @ApiProperty()
  @IsOptional()
  packageType?: PackageType;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  price?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  discount?: number;

  @ApiProperty()
  @IsOptional()
  roles?: string;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  status?: boolean = true;
}
