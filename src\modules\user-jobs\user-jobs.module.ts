import { Modu<PERSON> } from '@nestjs/common';

import { SequelizeModule } from '@nestjs/sequelize';
import { UserJobs } from './schemas/user-jobs.schema';
import { UserJobController } from './user-jobs.controller';
import { UserJobsService } from './user-jobs.service';
import { User } from 'src/modules/admin/authentication/schemas/user.schema';
import { BiddingDetail } from '../job/schemas/bidding-details.schema';
import { Job } from '../job/schemas/job.schema';
import { Role } from '../roles/schemas/role.schema';
import { JobsService } from '../job/job.service';
import { SendgridModule } from '../sendgrid/sendgrid.module';
import { UserJobBids } from '../user-job-bids/schemas/user-job-bids.schema';
import { Connection } from '../connection/schemas/connection.schema';
import { NotificationModule } from '../notification/notification.module';
import { Notification } from '../notification/schema/notification.schema';

@Module({
  imports: [
    SequelizeModule.forFeature([
      UserJobs,
      UserJobBids,
      User,
      Job,
      BiddingDetail,
      Role,
      Connection,
      Notification,
    ]),
    NotificationModule,
    SendgridModule,
  ],
  providers: [UserJobsService, JobsService],
  controllers: [UserJobController],
})
export class UserJobsModule {}
