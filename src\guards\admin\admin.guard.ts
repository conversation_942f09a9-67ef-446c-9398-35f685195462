import {
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { CommonService } from 'src/common/common.service';
import { User } from 'src/modules/admin/authentication/schemas/user.schema';
import { Reflector } from '@nestjs/core';
import { AdminRole } from 'src/modules/admin/schemas/admin-role.schemas';
import { Modules } from 'src/common/utils/enum';
import { Role } from 'src/modules/roles/schemas/role.schema';

@Injectable()
export class AdminGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private readonly authService: CommonService,
    @InjectModel(User)
    private userRepository: typeof User,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    try {
      const request = context.switchToHttp().getRequest();

      const token = this.extractTokenFromHeader(request);
      const verify = await this.authService.verifyToken(token);
      if (verify) {
        const result: User = await this.userRepository.findByPk(verify._id, {
          include: [
            {
              model: AdminRole,
              as: 'adminRole',
              attributes: [
                'admins',
                'user_management',
                'dashboard',
                'content_management',
                'ads_manager',
                'subscription_management',
              ],
            },
            {
              model: Role,
              as: 'roles',
            },
          ],
        }); //{ include: [Permission] }   if wanted
        if (!result) throw new UnauthorizedException('Access denied!');
        const isSuperAdmin = result.roles.some(
          (role) => role.roleName === 'super_admin',
        );

        if (isSuperAdmin) {
          request.user = result;
          return true;
        }
        const isAdmin = result.roles.some((role) => role.roleName === 'admin');
        if (isAdmin) {
          const requiredPermissions = this.reflector.get<Modules[]>(
            'permissions',
            context.getHandler(),
          );

          // Check if the admin role has the required permissions
          const hasPermission = requiredPermissions.every((permission) => {
            switch (permission) {
              case Modules.ADMINS:
                return result.adminRole.admins;
              case Modules.USER_MANAGEMENT:
                return result.adminRole.user_management;
              case Modules.DASHBOARD:
                return result.adminRole.dashboard;
              // case Modules.CONTENT_MANAGEMENT:
              //   return result.adminRole.content_management;
              case Modules.ADS_MANAGER:
                return result.adminRole.ads_manager;
              case Modules.SUBSCRIPTION_MANAGEMENT:
                return result.adminRole.subscription_management;
              default:
                return false;
            }
          });

          console.log('hasPermission...', hasPermission);

          if (hasPermission) {
            request.user = result; // Attach user information to the request object
            return true; // Grant access
          } else {
            throw new ForbiddenException(
              `Access denied to ${requiredPermissions}`,
            );
          }
        }
        // If the user role is not recognized, deny access
        throw new ForbiddenException('Access denied!');
      }
    } catch (err) {
      throw err;
    }
  }
  private extractTokenFromHeader(request: Request): string | undefined {
    const [type, token] = request.headers['authorization']?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
