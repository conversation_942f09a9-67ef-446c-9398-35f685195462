import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  InternalServerErrorException,
  BadRequestException,
  ConflictException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Op } from 'sequelize';
// import { User } from '../admin/authentication/schemas/user.schema';
import { CreatePackageDto, UpdatePackageStatusDto } from './dto/package.dto';
import { Package, PackageType } from './schemas/package.schema';
import { Role } from '../roles/schemas/role.schema';
import { PackageDetails } from './schemas/packagePermission.schema';
import { rolePermissions } from 'src/common/constants';
// import { Subscription } from './schemas/subscription.schema';

@Injectable()
export class SubscriptionService {
  constructor(
    @InjectModel(Package)
    private readonly packageRepository: typeof Package,
    @InjectModel(PackageDetails)
    private readonly packageDetailsModel: typeof PackageDetails,
    @InjectModel(Role)
    private readonly roleRepository: typeof Role,
  ) {}

  // async createSubscription(createSubscriptionData: CreateSubscriptionDto) {
  //   try {
  //     const subPackage = await this.subscriptionRepository.findOne({
  //       where:{
  //         title: createSubscriptionData.title
  //       }
  //     });
  //     if (!subPackage) {
  //       throw new BadRequestException('Invalid User ID');
  //     }

  //     const subscription = await this.subscriptionRepository.create({
  //       ...createSubscriptionData,
  //      // Assuming you have a foreign key field in Subscription
  //     });

  //     return {
  //       message: 'Subscription successfully created.',
  //     };
  //   } catch (error) {
  //     throw new InternalServerErrorException(`Failed to add subscription data: ${error.message}`);
  //   }
  // }

  // async findAllSubscriptions(page: number, limit: number) {
  //   const offset = (page - 1) * limit;
  //   try {
  //     return await this.subscriptionRepository.findAll({
  //       offset,
  //       limit,
  //     });
  //   } catch (error) {
  //     throw new InternalServerErrorException(`Failed to retrieve subscriptions: ${error.message}`);
  //   }
  // }

  // async findOneSubscription(id: number) {
  //   try {
  //     const subscription = await this.subscriptionRepository.findByPk(id);
  //     if (!subscription) {
  //       throw new NotFoundException(`Subscription with ID ${id} not found`);
  //     }
  //     return subscription;
  //   } catch (error) {
  //     throw new InternalServerErrorException(`Failed to retrieve subscription: ${error.message}`);
  //   }
  // }

  // async updateSubscription(id: number, updateSubscriptionData: CreateSubscriptionDto) {
  //   try {
  //     const [affectedCount] = await this.subscriptionRepository.update(updateSubscriptionData, {
  //       where: { id },
  //       returning: true,
  //     });

  //     if (affectedCount === 0) {
  //       throw new NotFoundException(`Subscription with ID ${id} not found`);
  //     }

  //     return {
  //       message: 'Subscription successfully updated.',
  //     };
  //   } catch (error) {
  //     throw new InternalServerErrorException(`Failed to update subscription: ${error.message}`);
  //   }
  // }

  // async removeSubscription(id: number) {
  //   try {
  //     const affectedCount = await this.subscriptionRepository.destroy({
  //       where: { id },
  //     });

  //     if (affectedCount === 0) {
  //       throw new NotFoundException(`Subscription with ID ${id} not found`);
  //     }

  //     return {
  //       message: 'Subscription successfully deleted.',
  //     };
  //   } catch (error) {
  //     throw new InternalServerErrorException(`Failed to delete subscription: ${error.message}`);
  //   }
  // }

  // async updateSubscriptionStatus(id: number, status: boolean) {
  //   try {
  //     const [affectedCount] = await this.subscriptionRepository.update({ status }, {
  //       where: { id },
  //       returning: true,
  //     });

  //     if (affectedCount === 0) {
  //       throw new NotFoundException(`Subscription with ID ${id} not found`);
  //     }

  //     return {
  //       message: 'Subscription status successfully updated.',
  //     };
  //   } catch (error) {
  //     throw new InternalServerErrorException(`Failed to update subscription status: ${error.message}`);
  //   }
  // }

  // async subscriptionSearch(title: string, limit: number, page: number) {
  //   const offset = (page - 1) * limit;
  //   try {
  //     return await this.subscriptionRepository.findAll({
  //       where: {
  //         title: {
  //           [Op.iLike]: `${title}%`, // Case-insensitive search
  //         },
  //       },
  //       limit,
  //       offset,
  //     });
  //   } catch (error) {
  //     if (error instanceof NotFoundException || error instanceof BadRequestException) {
  //       throw error;
  //     }
  //     throw new InternalServerErrorException('An unexpected error occurred.');
  //   }
  // }

  /////////////////////    package    /////////////////

  async createPackage(createData: CreatePackageDto) {
    try {
      const roleExist = await this.roleRepository.findOne({
        where: { roleName: createData.role },
      });
      if (!roleExist) {
        throw new ConflictException(`Role "${createData.role}" does not exist`);
      }

      const existingPackage = await this.packageRepository.findOne({
        where: {
          title: createData.title,
          packageType: createData.packageType,
          role: createData.role,
        },
      });

      if (existingPackage) {
        throw new ConflictException(
          'Package with these roles and type already exists.',
        );
      }

      // Calculate price if required
      let price = createData.price;
      if (createData.packageType !== PackageType.YEARLY) {
        const yearlyPackage = await this.packageRepository.findOne({
          where: {
            title: createData.title,
            role: createData.role,
            packageType: PackageType.YEARLY,
          },
        });

        if (!yearlyPackage) {
          throw new BadRequestException(
            'An yearly package must exist before creating monthly or quarterly packages.',
          );
        }

        price =
          createData.packageType === PackageType.MONTHLY
            ? Math.round((yearlyPackage.price / 12) * 1.5 * 100) / 100
            : Math.round(((yearlyPackage.price * 1.3) / 4) * 100) / 100;
      }

      const gst = Math.round(price * 0.18 * 100) / 100;

      let discountPercentage = 0;
      if (createData.packageType === PackageType.YEARLY) {
        discountPercentage = 33;
      } else if (createData.packageType === PackageType.QUARTERLY) {
        discountPercentage = 13;
      }

      const newPackage = await this.packageRepository.create({
        title: createData.title,
        packageType: createData.packageType,
        role: createData.role,
        price,
        gst,
        discountPercentage,
      });

      if (
        ['default', 'free', 'basic', 'premium'].includes(
          createData.title.toLowerCase(),
        ) &&
        ['buyer', 'architect', 'contractor', 'seller', 'labour'].includes(
          createData.role.toLowerCase(),
        )
      ) {
        const permissions =
          rolePermissions[createData.title.toLowerCase()][
            createData.role.toLowerCase()
          ];

        await this.packageDetailsModel.create({
          packageId: newPackage.id,
          ...permissions,
        });
      }
      return newPackage;
    } catch (error) {
      throw new InternalServerErrorException(error.message);
    }
  }

  async findAllPackages(page: number, limit: number, filters?: any) {
    const offset = (page - 1) * limit;
    try {
      const where: any = {};

      where.title = {
        [Op.notIn]: ['free', 'default'],
      };
      if (filters?.type) {
        where.packageType = {
          [Op.eq]: filters.type,
        };
      }
      if (filters?.role) {
        where.role = {
          [Op.iLike]: `%${filters.role}%`,
        };
      }
      if (filters?.search) {
        where.title = {
          ...where.title, // Combine the existing title condition with the search filter
          [Op.iLike]: `%${filters.search}%`,
        };
      }

      return await this.packageRepository.findAndCountAll({
        where,
        include: [
          {
            model: PackageDetails,
            as: 'packageDetails',
          },
        ],
        offset,
        limit,
        order: [['createdAt', 'DESC']],
      });
    } catch (error) {
      throw new InternalServerErrorException(
        `Failed to retrieve subscriptions: ${error.message}`,
      );
    }
  }

  async findOnePackage(id: number) {
    try {
      const subscriptionPackage = await this.packageRepository.findByPk(id);
      if (!subscriptionPackage) {
        throw new NotFoundException(`Subscription with ID ${id} not found`);
      }
      return subscriptionPackage;
    } catch (error) {
      throw new InternalServerErrorException(
        `Failed to retrieve subscription: ${error.message}`,
      );
    }
  }

  async removePackage(id: number) {
    try {
      const affectedCount = await this.packageRepository.destroy({
        where: { id },
      });

      if (affectedCount === 0) {
        throw new NotFoundException(`Subscription with ID ${id} not found`);
      }

      return {
        message: 'Subscription successfully deleted.',
      };
    } catch (error) {
      throw new InternalServerErrorException(
        `Failed to delete subscription: ${error.message}`,
      );
    }
  }

  async UpdatePackageById(id: number, updateData: CreatePackageDto) {
    try {
      const roleExist = await this.roleRepository.findOne({
        where: {
          roleName: updateData.role,
        },
      });
      if (!roleExist) {
        throw new ConflictException('Role does not exist');
      }

      let price = updateData.price;

      // For monthly and quarterly packages, ensure that a yearly package exists
      if (updateData.packageType !== PackageType.YEARLY) {
        const yearlyPackage = await this.packageRepository.findOne({
          where: {
            title: updateData.title,
            role: updateData.role,
            packageType: PackageType.YEARLY,
          },
        });

        if (!yearlyPackage) {
          throw new BadRequestException(
            'Yearly package must exist before creating monthly or quarterly packages.',
          );
        }

        // Apply discount if available
        let discountedYearlyPrice = yearlyPackage.price;
        // if (yearlyPackage.discount) {
        //   discountedYearlyPrice =
        //     yearlyPackage.price -
        //     (yearlyPackage.price * yearlyPackage.discount) / 100;
        // }

        // Calculate price based on the discounted yearly package price
        if (updateData.packageType === PackageType.MONTHLY) {
          price = (discountedYearlyPrice / 12) * 1.5; // 50% more than the discounted yearly price divided by 12
        } else if (updateData.packageType === PackageType.QUARTERLY) {
          const yearlyPriceWithIncrease = discountedYearlyPrice * 1.3; // 30% more than the discounted yearly price
          price = yearlyPriceWithIncrease / 4; // Divide the increased yearly price by 4 for the quarterly price
        }
      }

      // Check if a package with the same title, role, and package type already exists, excluding the current package's ID
      const duplicatePackage = await this.packageRepository.findOne({
        where: {
          title: updateData.title,
          role: updateData.role,
          packageType: updateData.packageType,
          id: { [Op.ne]: id }, // Exclude the current package ID
        },
      });

      if (duplicatePackage) {
        throw new ConflictException(
          'Another package with the same title, role, or package type already exists.',
        );
      }

      // Find the existing package by ID
      const existingPackage = await this.packageRepository.findByPk(id);

      if (!existingPackage) {
        throw new NotFoundException('Package not found.');
      }

      // Update the existing package
      existingPackage.title = updateData.title;
      existingPackage.packageType = updateData.packageType;
      existingPackage.role = updateData.role;
      existingPackage.price = price;

      await existingPackage.save();

      return existingPackage;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  async updateStatus(
    id: number,
    updateStatusDto: UpdatePackageStatusDto,
  ): Promise<Package> {
    const packages = await this.packageRepository.findOne({
      where: { id },
    });

    if (!packages) {
      throw new NotFoundException(`package not found`);
    }
    packages.status = updateStatusDto.status; // Assuming `status` is the property name in your DTO

    // Save the updated package
    await packages.save();
    return packages;
  }
}
