import { Controller, Post, Body, Get, Query } from '@nestjs/common';

import {
  ApiBearerAuth,
  ApiOperation,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { SkillService } from './skill.service';
import { CreateSkillDto } from './dto/create-skill.dto';
import { Skill } from './schemas/skill.schema';
import { Op } from 'sequelize';

@ApiTags('Skills')
@Controller('skill')
export class SkillController {
  constructor(private readonly skillService: SkillService) {}

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a skill' })
  @ApiResponse({ status: 201, description: 'Skill created successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @Post('')
  async createSkill(@Body() createSkillDto: CreateSkillDto): Promise<Skill> {
    return this.skillService.createSkill(createSkillDto);
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Fetch skill list' })
  @ApiResponse({ status: 200, description: 'Skills fetched successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'limit', required: false, type: String })
  @ApiQuery({ name: 'page', required: false, type: String })
  @Get()
  async getSkills(
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Query('search') search = null,
  ): Promise<{ skills: Skill[]; total: number }> {
    console.log('search..', search);
    const query = {
      ...(search && {
        name: { [Op.iLike]: `%${search}%` },
      }),
    };
    return this.skillService.getSkillsWithCount(query, page, limit);
  }
}
