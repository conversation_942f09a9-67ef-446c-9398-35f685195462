import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { DiscussionService } from './discussion.service';
import { DiscussionController } from './discussion.controller';
import { Discussion } from './schemas/discussion.schema';
import { User } from '../admin/authentication/schemas/user.schema';
import { RequestQuotation } from '../service/schemas/quotation-request.schema';
import { UserJobBidService } from '../user-job-bids/user-job-bids.servics';
import { UserJobBids } from '../user-job-bids/schemas/user-job-bids.schema';
import { JobsService } from '../job/job.service';
import { Job } from '../job/schemas/job.schema';
import { Role } from '../roles/schemas/role.schema';
import { BiddingDetail } from '../job/schemas/bidding-details.schema';
import { NotificationModule } from '../notification/notification.module';
import { Connection } from '../connection/schemas/connection.schema';
import { SendgridModule } from '../sendgrid/sendgrid.module';

@Module({
  imports: [
    SequelizeModule.forFeature([
      Discussion,
      User,
      RequestQuotation,
      UserJobBids,
      BiddingDetail,
      Job,
      Role,
      Connection,
    ]),
    NotificationModule,
    SendgridModule,
  ],
  providers: [DiscussionService, UserJobBidService, JobsService],
  controllers: [DiscussionController],
})
export class DiscussionModule {}
