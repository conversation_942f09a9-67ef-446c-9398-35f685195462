import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { I18nService } from 'nestjs-i18n';

@Injectable()
export class SmsService {
  private readonly smsApiUrl: string;
  private readonly smsAuthKey: string; // Replace with your MSG91 AuthKey

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly i18n: I18nService,
  ) {
    this.smsApiUrl = this.configService.get<string>('SMS_API_URL');
    this.smsAuthKey = this.configService.get<string>('SMS_AUTH_KEY');
  }
  async sendTemplateSms(data: any): Promise<any> {
    try {
      const headers = {
        'Content-Type': 'application/json',
        authkey: this.smsAuthKey,
      };
      const senderId = 'PARMIP';
      const payload = {
        sender: senderId,
        flow_id: data.templateId,
        mobiles: `91${data.phone}`,
        ...data.variables, // Spread template variables directly into the payload
      };
      console.log('payload::::', payload);
      console.log('headers::::', headers);
      console.log('smsApiUrl::::', `${process.env.MSG_91_API}/flow`);
      const response = await axios.post(
        `${process.env.MSG_91_API}/flow`,
        payload,
        { headers },
      );
      if (response.data.type === 'success') {
        console.log('sms response::::', response);
        return response.data.message;
      } else {
        throw new HttpException(
          response.data.message || 'Failed to send SMS',
          HttpStatus.BAD_REQUEST,
        );
      }
    } catch (error) {
      console.error('Error sending SMS:', error.message);
      throw new HttpException(
        error.response?.data?.message || 'SMS sending failed',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  private generateOTPOptions(payload: any) {
    const data = {
      template_id: `${payload.templateId}`,
      sender: 'PARMIP',
      mobiles: `91${payload.phone}`,
      var1: payload.var1,
    };
    return {
      method: 'POST',
      url: `${this.smsApiUrl}/flow`,
      headers: {
        authkey: this.smsAuthKey,
        'Content-Type': 'application/json',
        accept: 'application/json',
      },
      data: JSON.stringify(data),
    };
  }

  /**
   * Generate options for sending general SMS
   */
  private generateSMSOptions(payload: any) {
    console.log('payload:::::', payload);
    const data = {
      template_id: `${payload.templateId}`, // Add template_id here
      sender: 'PARMIP', // Add sender ID here
      mobiles: `91${payload.phone}`,
      var1: payload.var1,
      var2: payload.var2,
    };

    if (payload.var2) {
      data['var2'] = payload.var2;
    }

    return {
      method: 'POST',
      url: `${this.smsApiUrl}/flow`,
      headers: {
        authkey: this.smsAuthKey,
        'Content-Type': 'application/json',
        accept: 'application/json',
      },
      data: JSON.stringify(data),
    };
  }

  /**
   * Send OTP or general SMS based on the flag `isOTP`
   */
  async triggerSMS(payload: any, isOTP: boolean): Promise<any> {
    console.log('sms payload', payload);
    const options = isOTP
      ? this.generateOTPOptions(payload)
      : this.generateSMSOptions(payload);

    try {
      const response: any = await firstValueFrom(
        this.httpService.request(options),
      );

      const responseOK =
        response && (response.status === 200 || response.status === 400);

      if (responseOK) {
        console.log('SMS sent successfully:', response.data);
        return response.data;
      } else {
        throw new HttpException('Failed to send SMS', HttpStatus.BAD_REQUEST);
      }
    } catch (error) {
      throw new HttpException(
        `SMS API Error: ${error.message || 'Request Failed'}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
