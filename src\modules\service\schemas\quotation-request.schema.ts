import {
  Table,
  Column,
  Model,
  DataType,
  BelongsTo,
  ForeignKey,
  HasOne,
  HasMany,
} from 'sequelize-typescript';
import {
  RequestedQuotationStatus,
  serviceType,
  UrgencyLevel,
} from 'src/common/utils/enum';
import { User } from 'src/modules/admin/authentication/schemas/user.schema';
import { Service } from './service.schema';
import { RecievedQuotation } from './quotation-recieved.schema';
import { Category } from 'src/modules/ category/schemas/category.schema';
import { Discussion } from 'src/modules/discussion/schemas/discussion.schema';
import { ReviewRating } from 'src/modules/review-rating/schemas/review-rating.schema';

@Table
export class RequestQuotation extends Model<RequestQuotation> {
  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  title: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  budget: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  type: string;

  @Column({
    type: DataType.ENUM,
    values: Object.values(RequestedQuotationStatus),
    defaultValue: RequestedQuotationStatus.REQUESTED,
    allowNull: false,
  })
  status: RequestedQuotationStatus;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  size: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  address1: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  address2: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  city: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  state: string;
  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  pincode: string;
  @Column({
    type: DataType.ENUM,
    values: Object.values(serviceType),
    allowNull: true,
  })
  services: serviceType;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  material: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  quantity: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  preferredBrands: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  fileUrl: string;
  @Column({
    type: DataType.ENUM,
    values: Object.values(UrgencyLevel),
    allowNull: true,
  })
  urgencyLevel: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  instructions: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  fullName: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  email: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  phoneNumber: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  contactMethod: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
  })
  negotiation: boolean;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  quoteValid: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  validTill: number;

  @Column({
    defaultValue: false,
    type: DataType.BOOLEAN,
  })
  isRead: boolean;

  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  userId: number;

  @BelongsTo(() => User, 'userId')
  user: User;

  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  requestedTo: number;

  @BelongsTo(() => User, 'requestedTo')
  requested: User;

  @ForeignKey(() => Service)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  serviceId: number;

  @BelongsTo(() => Service, 'serviceId')
  service: Service;

  @HasOne(() => RecievedQuotation, 'requestQuotationId')
  receivedQuotation: RecievedQuotation;

  @HasMany(() => Discussion)
  discussions: Discussion[];

  @ForeignKey(() => Category)
  @Column
  categoryId: number;

  @ForeignKey(() => Category)
  @Column
  subCategoryId: number;

  @BelongsTo(() => Category, { foreignKey: 'categoryId', as: 'category' })
  category: Category;

  @BelongsTo(() => Category, { foreignKey: 'subCategoryId', as: 'subCategory' })
  subCategory: Category;

  @HasOne(() => ReviewRating, {
    foreignKey: 'quotationId',
    as: 'reviewRating',
  })
  reviewRating: ReviewRating;
}
