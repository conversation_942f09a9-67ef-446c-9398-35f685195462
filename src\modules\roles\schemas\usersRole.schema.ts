import {
  Table,
  Column,
  Model,
  ForeignKey,
  BelongsTo,
  DataType,
} from 'sequelize-typescript';
import { User } from 'src/modules/admin/authentication/schemas/user.schema';
import { Role } from './role.schema';

@Table
export class UserRole extends Model {
  @Column({
    type: DataType.BIGINT,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;
  @ForeignKey(() => User)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  userId: string;

  @ForeignKey(() => Role)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  roleId: string;

  @BelongsTo(() => Role)
  role: Role;

  @BelongsTo(() => User)
  user: User;

  @Column({
    type: DataType.STRING,
    defaultValue: 'Unblock',
  })
  status: string;
}
