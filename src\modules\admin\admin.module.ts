import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { AdminController } from './admin.controller';
import { AdminService } from './admin.service';
import { AuthenticationModule } from './authentication/authentication.module';
import { User } from './authentication/schemas/user.schema';
import { AdminRole } from './schemas/admin-role.schemas';
import { UserRole } from '../roles/schemas/usersRole.schema';
import { Role } from '../roles/schemas/role.schema';
import { UserSubscription } from '../user-subscription/schemas/user-subscription.schema';
import { Transaction } from '../user-subscription/schemas/transaction.schema';

@Module({
  imports: [
    SequelizeModule.forFeature([
      User,
      AdminRole,
      UserRole,
      Role,
      UserSubscription,
      Transaction,
    ]),
    AuthenticationModule,
  ],
  providers: [AuthenticationModule, AdminService],
  controllers: [AdminController],
  exports: [AdminService],
  // imports: [AuthenticationModule],
})
export class AdminModule {}
