import {
  Column,
  Model,
  Table,
  ForeignKey,
  DataType,
  BelongsTo,
  CreatedAt,
  UpdatedAt,
  HasMany,
} from 'sequelize-typescript';
import { User } from 'src/modules/admin/authentication/schemas/user.schema';
import { Job } from 'src/modules/job/schemas/job.schema';
import { UserJobBidStatus, UserJobBidType } from '../utills/enum';
import { Bids } from './bids.schema';

@Table({ timestamps: true })
export class UserJobBids extends Model<UserJobBids> {
  @Column({
    type: DataType.ENUM,
    values: Object.values(UserJobBidType),
  })
  type: string;

  @ForeignKey(() => User)
  @Column
  userId: number;

  @ForeignKey(() => User)
  @Column
  jobOwnerId: number;

  @ForeignKey(() => Job)
  @Column
  jobId: number;

  @Column({
    type: DataType.ENUM,
    values: Object.values(UserJobBidStatus),
  })
  status: string;

  @Column({ defaultValue: false })
  isPaid: boolean;

  @BelongsTo(() => User)
  user: User;

  @BelongsTo(() => User, { foreignKey: 'jobOwnerId', as: 'jobOwner' })
  jobOwner: User;

  @BelongsTo(() => Job)
  job: Job;

  @HasMany(() => Bids, { foreignKey: 'userJobBidId', as: 'bids' })
  bids: Bids[];

  @CreatedAt
  @Column
  createdAt: Date;

  @UpdatedAt
  @Column
  updatedAt: Date;
}
