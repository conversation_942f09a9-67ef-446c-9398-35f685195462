import { HttpStatus, Injectable, UnauthorizedException } from '@nestjs/common';
import { SuperAdminCreateDTO } from './dto/superAdmin.dto';
import * as bcrypt from 'bcryptjs';
import { User } from './schemas/user.schema';
import { Constants } from 'src/common/constants';
import { InjectModel } from '@nestjs/sequelize';
import { LoginDTO } from './dto/login.dto';
import { CustomException } from 'src/common/custom.exception';
import { ERROR_MESSAGES } from 'src/common/messages';
import { CommonService } from 'src/common/common.service';
import { AdminRole } from '../schemas/admin-role.schemas';
import { Role } from 'src/modules/roles/schemas/role.schema';
import { UserRole } from 'src/modules/roles/schemas/usersRole.schema';

@Injectable()
export class AuthenticationService {
  constructor(
    @InjectModel(User)
    private userRepository: typeof User,
    @InjectModel(UserRole)
    private userRoleRepository: typeof UserRole,
    @InjectModel(Role)
    private roleRepository: typeof Role,
    @InjectModel(AdminRole)
    private adminRoleRepository: typeof AdminRole,
    private readonly authService: CommonService,
  ) {}

  getAll = async () => {
    return this.userRepository.findAll({
      attributes: [
        'id',
        'firstName',
        'lastName',
        'role',
        'email',
        'status',
        'profilePicture',
        'bio',
        'phoneNumber',
        'address1',
        'address2',
        'city',
      ],
    });
  };

  // CreateUserAdmin
  createSuperAdminUser = async (dto: SuperAdminCreateDTO) => {
    const { email, password } = dto;
    const exisintgSuperAdmin = await this.userRepository.findOne({
      where: {
        role: Constants.Roles.SUPER_ADMIN,
      },
    });
    if (exisintgSuperAdmin) {
      throw new CustomException(
        ERROR_MESSAGES.SUPER_ADMIN_ALREADY_EXISTS,
        HttpStatus.UNAUTHORIZED,
      );
    }
    const hashPassword = await bcrypt.hash(password, 10);
    const dataToSave = {
      firstName: 'Super',
      lastName: 'Admin',
      email: email,
      password: hashPassword,
      role: Constants.Roles.SUPER_ADMIN,
    };

    const user = await this.userRepository.create(dataToSave);
    const role = await this.roleRepository.findOne({
      where: {
        roleName: 'super_admin',
      },
    });
    await this.userRoleRepository.create({
      userId: user.id,
      roleId: role.id,
    });
    return user;
  };

  login = async (loginDto: LoginDTO) => {
    const { email, password } = loginDto;

    try {
      const userExists = await this.userRepository.findOne({
        where: {
          email: email,
        },
        include: [
          {
            model: Role,
          },
          {
            model: AdminRole,
            as: 'adminRole',
          },
        ],
      });
      if (!userExists) {
        throw new CustomException(
          ERROR_MESSAGES.EMAIL_NOT_FOUND,
          HttpStatus.BAD_REQUEST,
        );
      }
      if (!userExists.isActive) {
        throw new CustomException(
          ERROR_MESSAGES.ACCOUNT_DEACTIVATED,
          HttpStatus.UNAUTHORIZED,
        );
      }
      const isPassword = await bcrypt.compare(password, userExists.password);
      if (!isPassword) {
        throw new UnauthorizedException('incorrect password');
      }
      const token = await this.authService.generateToken(userExists);
      userExists.lastLogin = new Date();
      userExists.save();

      const user = {
        id: userExists.id,
        firstName: userExists.firstName,
        lastName: userExists.lastName,
        role: userExists.role,
        email: userExists.email,
        phoneNumber: userExists.phoneNumber,
        isActive: userExists.isActive,
        adminRole: userExists.adminRole,
        roles: userExists.roles,
      };
      return { token: token, user };
    } catch (err) {
      throw err;
    }
  };

  verifyToken = async (token: string): Promise<boolean> => {
    try {
      this.authService.verifyToken(token);
      return true;
    } catch (err) {
      return false;
    }
  };
}
