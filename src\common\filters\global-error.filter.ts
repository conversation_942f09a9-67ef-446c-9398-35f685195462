import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpException,
  HttpStatus,
} from '@nestjs/common';

@Catch()
export class GlobalErrorFilter implements ExceptionFilter {
  catch(exception: any, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse();
    const status =
      exception instanceof HttpException
        ? exception.getStatus()
        : HttpStatus.INTERNAL_SERVER_ERROR;

    let message: any;

    if (exception instanceof HttpException && exception.getResponse()) {
      // If the exception is an instance of HttpException and has a response
      const responseBody = exception.getResponse();
      if (
        typeof responseBody === 'object' &&
        responseBody.hasOwnProperty('message')
      ) {
        // If the response body is an object and has a 'message' property, use it
        message = responseBody['message'];
      } else {
        // If not, use the response body itself
        message = responseBody;
      }
    } else {
      // For other exceptions, use the generic error message
      message =
        exception instanceof Error
          ? exception.message
          : 'Internal server error';
    }

    response.status(status).json({
      statusCode: status,
      message: message,
    });
  }
}
