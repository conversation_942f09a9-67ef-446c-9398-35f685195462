import { Injectable, OnModuleInit } from '@nestjs/common';
import * as admin from 'firebase-admin';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class FirebaseService {
  constructor(private configService: ConfigService) {}
  onModuleInit() {
    if (!admin.apps.length) {
      // Check if Firebase app is already initialized
      const serviceAccount = JSON.parse(
        this.configService.get<string>('FIREBASE_SERVICE_ACCOUNT'),
      );
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
      });
      console.log('Firebase app initialized');
    }
  }

  async sendNotificationToDevice(
    token: string,
    notification: { title: string; body: string },
    data: any,
  ): Promise<void> {
    const message = {
      token: token,
      notification,
      data,
    };

    try {
      const responses = await admin.messaging().send(message);
      console.log('push notification response:::: ', responses);
      console.log(`Successfully sent notification to token: ${token}`);
    } catch (error) {
      console.error(
        `Error sending push notification to token: ${token}`,
        error,
      );
    }
  }

  async sendPushNotification(
    tokens: string[],
    notification: { title: string; body: string },
    data?: Record<string, string>,
  ): Promise<void> {
    if (!tokens || tokens.length === 0) {
      console.log('No tokens to send notifications to.');
      return;
    }

    const message = {
      notification,
      data: data || {},
      tokens,
    };

    try {
      // Send the notification to multiple devices
      const response = await admin.messaging().sendEachForMulticast(message);

      // Log success and failure counts
      console.log(`${response.successCount} messages were sent successfully.`);
      console.log(`${response.failureCount} messages failed.`);

      // Handle failed tokens
      if (response.failureCount > 0) {
        const failedTokens = response.responses
          .map((resp, idx) => (!resp.success ? tokens[idx] : null))
          .filter((token) => token !== null);

        console.log('Failed tokens:', failedTokens);
      }
    } catch (error) {
      console.error('Error sending push notifications:', error);
    }
  }
}
