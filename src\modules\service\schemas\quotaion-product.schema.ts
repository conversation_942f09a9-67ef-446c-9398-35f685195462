import {
  BelongsTo,
  Column,
  DataType,
  ForeignKey,
  Model,
  Table,
} from 'sequelize-typescript';
import { Service } from './service.schema';
import { IsNotEmpty, IsNumber } from 'class-validator';
import { RecievedQuotation } from './quotation-recieved.schema';

@Table
export class ProductDetails extends Model {
  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  title: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  description: string;

  @IsNumber()
  @IsNotEmpty()
  @Column({
    type: DataType.FLOAT,
    allowNull: true,
  })
  unitCost: number;

  @IsNumber()
  @IsNotEmpty()
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  quantity: number;

  @IsNumber()
  @IsNotEmpty()
  @Column({
    type: DataType.FLOAT,
    allowNull: true,
  })
  amount: number;

  @ForeignKey(() => Service)
  @Column({
    // unique: true, // Ensure one product can have only one ProductDetails
  })
  productId: number;

  @BelongsTo(() => Service)
  product: Service;

  @ForeignKey(() => RecievedQuotation)
  @Column
  receivedQuotationId: number;

  @BelongsTo(() => RecievedQuotation)
  recievedQuotation: RecievedQuotation;
}
