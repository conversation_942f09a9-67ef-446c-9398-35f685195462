import { Column, Model, Table, ForeignKey } from 'sequelize-typescript';
import { User } from 'src/modules/admin/authentication/schemas/user.schema';
import { Tags } from 'src/modules/service/schemas/tags.schema';

@Table
export class UserTags extends Model<UserTags> {
  @ForeignKey(() => User)
  @Column({
    allowNull: false,
  })
  userId: number;

  @ForeignKey(() => Tags)
  @Column({
    allowNull: false,
  })
  tagId: number;
}
