import {
  BadRequestException,
  ConflictException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { CommonService } from 'src/common/common.service';
import { FindOptions, Op, Sequelize } from 'sequelize';
import { User } from './authentication/schemas/user.schema';
import { AdminRole } from './schemas/admin-role.schemas';
import { AdminRoleDto } from './dto/admin-role.dto';
import { SubAdminDto } from './dto/sub-admin.dto';
// import { Role } from '../auth/dto/user.dto';
import { Role } from '../roles/schemas/role.schema';
import * as bcrypt from 'bcryptjs';
import { CustomException } from 'src/common/custom.exception';
import { ERROR_MESSAGES } from 'src/common/messages';
import { UserRole } from '../roles/schemas/usersRole.schema';
import { UserSubscription } from '../user-subscription/schemas/user-subscription.schema';
import { SubscriptionStatus } from 'src/common/utils/enum';
import {
  Transaction,
  TransactionStatus,
} from '../user-subscription/schemas/transaction.schema';
import sequelize from 'sequelize';
import { from } from 'rxjs';
import moment from 'moment';

@Injectable()
export class AdminService {
  constructor(
    @InjectModel(User)
    private userRepository: typeof User,
    @InjectModel(AdminRole)
    private adminRoleRepository: typeof AdminRole,
    @InjectModel(Role)
    private roleRepository: typeof Role,
    @InjectModel(UserRole)
    private userRoleRepository: typeof UserRole,
    @InjectModel(UserSubscription)
    private userSubscriptionRepository: typeof UserSubscription,
    @InjectModel(Transaction)
    private transactionRepository: typeof Transaction,
    private readonly authService: CommonService,
  ) {}
  async createRole(adminRoleData: AdminRoleDto) {
    // try {
    const {
      roleName,
      admins,
      user_management,
      dashboard,
      content_management,
      ads_manager,
      subscription_management,
    } = adminRoleData;

    const existingRoleByName = await this.adminRoleRepository.findOne({
      where: { role_name: roleName },
    });

    if (existingRoleByName) {
      throw new BadRequestException(
        'An Admin Role with the same title already exists.',
      );
    }

    // Check for duplicate role permissions
    const existingRoleByPermissions = await this.adminRoleRepository.findOne({
      where: {
        admins,
        user_management,
        dashboard,
        content_management,
        ads_manager,
        subscription_management,
      },
    });

    if (existingRoleByPermissions) {
      throw new BadRequestException(
        'An Admin Role with the same permissions already exists.',
      );
    }

    const adminRole = await this.adminRoleRepository.create({
      role_name: adminRoleData.roleName,
      description: adminRoleData.description,
      admins: adminRoleData.admins,
      user_management: adminRoleData.user_management,
      dashboard: adminRoleData.dashboard,
      content_management: adminRoleData.content_management,
      ads_manager: adminRoleData.ads_manager,
      subscription_management: adminRoleData.subscription_management,
    });
    return adminRole;
  }

  async findAllRole(page: number, limit: number, roleName: string) {
    try {
      const options: any = {
        include: [
          {
            model: User,
            as: 'users',
          },
        ],
        limit,
        offset: (page - 1) * limit,
        order: [['createdAt', 'DESC']],
      };

      if (roleName) {
        options.where = {
          [Op.or]: [
            { role_name: { [Op.iLike]: `%${roleName}%` } }, // Match role_name
            { description: { [Op.iLike]: `%${roleName}%` } }, // Match description
          ],
        };
      }

      return await this.adminRoleRepository.findAndCountAll(options);
    } catch (error) {
      throw new InternalServerErrorException('An unexpected error occurred.');
    }
  }

  async findOneRole(id: number) {
    try {
      const adminRole = await this.adminRoleRepository.findOne({
        where: { id },
      });
      if (!adminRole) {
        throw new NotFoundException(`Role with ID ${id} not found`);
      }
      return adminRole;
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new InternalServerErrorException('An unexpected error occurred.');
    }
  }
  async updateRole(id: number, updateRoleData: AdminRoleDto) {
    try {
      const {
        roleName,
        admins,
        user_management,
        dashboard,
        content_management,
        ads_manager,
        subscription_management,
      } = updateRoleData;
      const adminRole = await this.adminRoleRepository.findOne({
        where: { id },
      });
      if (!adminRole) {
        throw new NotFoundException(`Role with ID ${id} not found`);
      }

      const existingRoleByName = await this.adminRoleRepository.findOne({
        where: {
          role_name: roleName,
          id: { [Op.ne]: id }, // Ensure the current role is excluded from this check
        },
      });

      if (existingRoleByName) {
        throw new BadRequestException(
          'An Admin Role with the same title already exists.',
        );
      }

      // Check if a different role with the same permissions exists
      const existingRoleByPermissions = await this.adminRoleRepository.findOne({
        where: {
          admins,
          user_management,
          dashboard,
          content_management,
          ads_manager,
          subscription_management,
          id: { [Op.ne]: id }, // Ensure the current role is excluded from this check
        },
      });

      if (existingRoleByPermissions) {
        throw new BadRequestException(
          'An Admin Role with the same permissions already exists.',
        );
      }

      // Update the role with new data
      await adminRole.update({
        role_name: updateRoleData.roleName,
        description: updateRoleData.description,
        admins: updateRoleData.admins,
        user_management: updateRoleData.user_management,
        dashboard: updateRoleData.dashboard,
        content_management: updateRoleData.content_management,
        ads_manager: updateRoleData.ads_manager,
        subscription_management: updateRoleData.subscription_management,
      });

      return adminRole;
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new InternalServerErrorException('An unexpected error occurred.');
    }
  }
  async removeRole(id: number) {
    const adminRole = await this.adminRoleRepository.findOne({
      where: { id },
      include: [
        {
          model: User,
          as: 'users',
        },
      ],
    });
    console.log(adminRole.users);
    if (!adminRole) {
      throw new NotFoundException(`Role with ID ${id} not found`);
    }

    if (adminRole.users && adminRole.users.length > 0) {
      throw new BadRequestException(
        'Please remove sub-admin before deleting this role',
      );
    }

    await adminRole.destroy();
    return { message: `Role with ID ${id} has been removed` };
  }

  async createSubAdmin(subAdminData: SubAdminDto) {
    const admin = await this.userRepository.findOne({
      where: {
        email: subAdminData.email,
        phoneNumber: subAdminData.phoneNumber,
      },
    });
    if (admin) {
      throw new CustomException(
        ERROR_MESSAGES.ADMIN_ALREADY_EXISTS,
        HttpStatus.UNAUTHORIZED,
      );
    }
    if (subAdminData.email) {
      const existingByEmail = await this.userRepository.findOne({
        where: {
          email: subAdminData.email,
        },
      });
      if (existingByEmail) {
        throw new BadRequestException('Email already in use');
      }
    }

    // Check for duplicate phone number
    if (subAdminData.phoneNumber) {
      const existingByPhone = await this.userRepository.findOne({
        where: {
          phoneNumber: subAdminData.phoneNumber,
        },
      });
      if (existingByPhone) {
        throw new BadRequestException('Phone number already in use');
      }
    }
    const hashPassword = await bcrypt.hash(subAdminData.password, 10);
    const role = await this.roleRepository.findOne({
      where: {
        roleName: 'admin',
      },
    });

    if (!role) {
      throw new CustomException(
        ERROR_MESSAGES.ROLE_NOT_FOUND,
        HttpStatus.NOT_FOUND,
      );
    }

    const subAdmin = await this.userRepository.create({
      ...subAdminData,
      password: hashPassword,
    });

    await this.userRoleRepository.create({
      userId: subAdmin.id,
      roleId: role.id,
    });

    return { message: ' admin created successfully', admin: subAdmin };
  }

  async findAllSubAdmin(page: number, limit: number, name: string) {
    try {
      const options: FindOptions = {
        limit,
        offset: (page - 1) * limit,
        order: [['createdAt', 'DESC']],
        include: [
          {
            model: Role,
            where: {
              roleName: 'admin',
            },
          },
          {
            model: AdminRole,
            as: 'adminRole',
          },
        ],
        attributes: { exclude: ['password', 'otp', 'verifyOTP'] },
      };
      if (name) {
        // Split the fullName into firstName and lastName
        const [firstName, lastName] = name.split(' ', 2); // Split into two parts

        // Construct the where clause for the search
        options.where = {
          ...options.where,
          [Op.or]: [
            // Case where fullName matches in a single field (either firstName or lastName)
            {
              [Op.or]: [
                {
                  [Op.and]: [
                    { firstName: { [Op.iLike]: `%${firstName || ''}%` } }, // First part of the full name
                    { lastName: { [Op.iLike]: `%${lastName || ''}%` } }, // Second part of the full name (if provided)
                  ],
                },
                { firstName: { [Op.iLike]: `%${name}%` } }, // Full name in firstName
                { lastName: { [Op.iLike]: `%${name}%` } }, // Full name in lastName
              ],
            },
            // Filter by phoneNumber
            { phoneNumber: { [Op.iLike]: `%${name}%` } },
            // Filter by email
            { email: { [Op.iLike]: `%${name}%` } },
          ],
        };
      }

      const subAdmins = await this.userRepository.findAndCountAll(options);
      return subAdmins;
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new InternalServerErrorException('An unexpected error occurred.');
    }
  }

  async findOneSubAdmin(id: number) {
    try {
      const subAdmin = await this.userRepository.findByPk(id);
      if (!subAdmin) {
        throw new NotFoundException('SubAdmin not found');
      }
      return subAdmin;
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new InternalServerErrorException('An unexpected error occurred.');
    }
  }

  async updateSubAdmin(id: number, updateSubAdminData: SubAdminDto) {
    try {
      const subAdmin = await this.findOneSubAdmin(id);
      if (!subAdmin) {
        throw new NotFoundException('Admin not found');
      }

      // Check for duplicate email
      if (updateSubAdminData.email) {
        const existingByEmail = await this.userRepository.findOne({
          where: {
            email: updateSubAdminData.email,
            id: { [Op.ne]: id }, // Ensure it's not the same user being updated
          },
        });
        if (existingByEmail) {
          throw new BadRequestException('Email already in use');
        }
      }

      // Check for duplicate phone number
      if (updateSubAdminData.phoneNumber) {
        const existingByPhone = await this.userRepository.findOne({
          where: {
            phoneNumber: updateSubAdminData.phoneNumber,
            id: { [Op.ne]: id }, // Ensure it's not the same user being updated
          },
        });
        if (existingByPhone) {
          throw new BadRequestException('Phone number already in use');
        }
      }
      const hashPassword = await bcrypt.hash(updateSubAdminData.password, 10);
      updateSubAdminData.password = hashPassword;

      await subAdmin.update(updateSubAdminData);
      return subAdmin;
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new InternalServerErrorException('An unexpected error occurred.');
    }
  }

  async removeSubAdmin(id: number) {
    try {
      const subAdmin = await this.findOneSubAdmin(id);
      if (!subAdmin) {
        throw new NotFoundException('SubAdmin not found');
      }
      if (subAdmin.role === 'super_admin') {
        throw new NotFoundException('SuperAdmin cannot be deleted');
      }
      await subAdmin.destroy();
      return { message: 'Admin deleted successfully' };
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new InternalServerErrorException('An unexpected error occurred.');
    }
  }

  async blockOrUnblockUser(adminId: number): Promise<{ message: string }> {
    const user = await this.userRepository.findOne({
      where: {
        id: adminId,
      },
      include: [
        {
          model: Role,
          where: {
            roleName: 'admin',
          },
        },
      ],
    });
    if (!user) throw new NotFoundException(ERROR_MESSAGES.USER_NOT_FOUND);
    user.isActive = !user.isActive;
    await user.save();
    return {
      message: `User ${user.isActive ? 'Unblocked' : 'Blocked'} successfully`,
    };
  }

  async getUserCountByRole(filters: {
    fromDate?: string;
    toDate?: string;
    city?: string;
    state?: string;
  }) {
    const { fromDate, toDate, city, state } = filters;

    const userWhere: any = {
      isActive: true,
      isDeleted: false,
    };
    if (fromDate && toDate) {
      try {
        const startOfDay = new Date(fromDate);
        startOfDay.setHours(0, 0, 0, 0); // Set to 00:00:00

        const endOfDay = new Date(toDate);
        endOfDay.setHours(23, 59, 59, 999); // Set to 23:59:59.999

        userWhere.createdAt = { [Op.between]: [startOfDay, endOfDay] };
      } catch (error) {
        throw new Error(
          'Invalid date format for fromDate or toDate. Please use a valid date format.',
        );
      }
    }
    if (city) userWhere.city = city;
    if (state) userWhere.state = state;

    // Query to count users grouped by role
    return this.userRoleRepository.findAll({
      attributes: [
        [Sequelize.col('role.roleName'), 'roleName'],
        [Sequelize.fn('COUNT', Sequelize.col('user.id')), 'userCount'],
      ],
      include: [
        {
          model: Role,
          as: 'role',
          attributes: [],
        },
        {
          model: User,
          as: 'user',
          where: userWhere,
          attributes: [],
        },
      ],
      group: ['role.id'],
      order: [[Sequelize.fn('COUNT', Sequelize.col('user.id')), 'DESC']],
    });
  }
  // private getStartOfCurrentMonth(): Date {
  //   const date = new Date();
  //   date.setDate(1); // Set the date to the 1st day of the current month
  //   date.setHours(0, 0, 0, 0); // Set the time to midnight
  //   return date;
  // }
  // private getEndOfCurrentMonth(): Date {
  //   const date = new Date();
  //   date.setMonth(date.getMonth() + 1); // Go to the next month
  //   date.setDate(0); // Set to the last day of the current month
  //   date.setHours(23, 59, 59, 999); // Set the time to the last millisecond of the last day
  //   return date;
  // }
  async getSubscriptionCountByRole(filters: {
    fromDate?: string;
    toDate?: string;
    city?: string;
    state?: string;
  }) {
    const { fromDate, toDate, city, state } = filters;
    const userWhere: any = {
      isActive: true,
    };
    const subsWhere: any = {
      status: SubscriptionStatus.ACTIVE,
    };
    if (fromDate && toDate) {
      const startOfDay = new Date(fromDate);
      startOfDay.setHours(0, 0, 0, 0); // Set to 00:00:00

      const endOfDay = new Date(toDate);
      endOfDay.setHours(23, 59, 59, 999); // Set to 23:59:59.999

      subsWhere.createdAt = { [Op.between]: [startOfDay, endOfDay] };
    }
    if (city) userWhere.city = city;
    if (state) userWhere.state = state;

    // Query to count users grouped by role
    const data = await this.userSubscriptionRepository.findAll({
      where: subsWhere,
      include: [
        {
          model: User,
          as: 'user',
          where: userWhere,
          attributes: [],
        },
      ],
      attributes: [
        [Sequelize.fn('COUNT', Sequelize.col('UserSubscription.id')), 'count'],
      ],
      group: ['UserSubscription.id'],
      raw: true,
    });
    const totalSubscriptionCount = data.reduce(
      (total, row: any) => total + parseInt(row.count),
      0,
    );
    return { UserSubscriptionCount: totalSubscriptionCount };
  }

  async getUserRegistrationGraph(filters: {
    fromDate: string;
    toDate: string;
  }) {
    const { fromDate, toDate } = filters;

    const startDate = new Date(fromDate);
    const endDate = new Date(toDate);

    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      throw new Error('Invalid date format');
    }

    if (startDate > endDate) {
      throw new Error('fromDate cannot be after toDate');
    }

    // Fetch user counts grouped by date
    const userCounts = await this.userRepository.findAll({
      where: {
        createdAt: {
          [Op.between]: [startDate, endDate],
        },
        isActive: true, // Active users only
        isDeleted: false,
      },
      attributes: [
        [Sequelize.fn('DATE', Sequelize.col('createdAt')), 'date'],
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'userCount'],
      ],
      group: ['date'],
      order: [['date', 'ASC']],
      raw: true,
    });

    // Fill in missing dates
    return this.fillMissingDates(startDate, endDate, userCounts);
  }

  private fillMissingDates(startDate: Date, endDate: Date, userCounts: any[]) {
    const result = [];
    const dateMap = new Map(
      userCounts.map((entry) => [entry.date, parseInt(entry.userCount, 10)]),
    );

    const currentDate = new Date(startDate);
    while (currentDate <= endDate) {
      const formattedDate = currentDate.toISOString().split('T')[0];
      result.push({
        date: formattedDate,
        userCount: dateMap.get(formattedDate) || 0, // Default to 0 if date is missing
      });
      currentDate.setDate(currentDate.getDate() + 1); // Increment to the next day
    }

    return result;
  }

  async getTotalRevenue(fromDate: string, toDate: string) {
    try {
      const where: any = {
        status: TransactionStatus.SUCCESS,
      };

      if (fromDate && toDate) {
        const startOfDay = new Date(fromDate);
        startOfDay.setHours(0, 0, 0, 0); // Set to 00:00:00

        const endOfDay = new Date(toDate);
        endOfDay.setHours(23, 59, 59, 999); // Set to 23:59:59.999

        where.createdAt = { [Op.between]: [startOfDay, endOfDay] };
      }
      const result = await this.transactionRepository.findAll({
        attributes: ['totalAmount'],
        where: where,
        logging: true,
      });

      console.log('result::::', result);
      const totalRevenue = result.reduce((sum, record) => {
        return sum + +record.totalAmount;
      }, 0);

      return { totalRevenueAmount: totalRevenue };
    } catch (error) {
      console.error('Error fetching total revenue:', error);
      throw error;
    }
  }

  async listUsers(limit: number, offset: number, filters?: any): Promise<any> {
    const rolesToInclude = [
      'labour',
      'seller',
      'buyer',
      'contractor',
      'architect',
    ];
    const where: any = {
      isDeleted: false,
    };
    if (filters?.search) {
      where[Op.or] = [
        Sequelize.literal(
          `concat("User"."firstName", ' ', "User"."lastName") ILIKE '%${filters?.search}%'`,
        ),
      ];
    }
    const roleWhere: any = {};
    if (filters?.role) {
      roleWhere.roleName = {
        [Op.iLike]: `%${filters.role}%`,
      };
    } else {
      roleWhere.roleName = {
        [Op.in]: rolesToInclude,
      };
    }
    const dateCondition = [];
    if (filters?.startDate && filters?.endDate) {
      dateCondition.push(
        sequelize.where(sequelize.fn('date', sequelize.col('User.createdAt')), {
          [Op.gte]: filters?.startDate,
          [Op.lte]: filters?.endDate,
        }),
      );
    } else if (filters?.startDate) {
      dateCondition.push(
        sequelize.where(
          sequelize.fn('date', sequelize.col('User.createdAt')),
          '>=',
          filters?.startDate,
        ),
      );
    } else if (filters?.endDate) {
      dateCondition.push(
        sequelize.where(
          sequelize.fn('date', sequelize.col('User.createdAt')),
          '<=',
          filters?.endDate,
        ),
      );
    }
    if (dateCondition.length > 0) {
      where[Op.and] = dateCondition;
    }
    const result = await this.userRepository.findAndCountAll({
      where,
      include: [
        {
          model: Role,
          as: 'roles',
          where: roleWhere,
          through: { attributes: [] },
        },
        {
          model: UserSubscription,
          as: 'subscription',
          where: { status: 'active' },
          required: false, // Include users without subscriptions
        },
      ],
      attributes: {
        exclude: [
          'password',
          'address1',
          'address2',
          'otp',
          'verifyOTP',
          'updatedAt',
        ],
      },
      limit,
      offset,
      order: [['createdAt', 'DESC']],
      distinct: true,
      logging: true,
    });

    const { rows, count } = result;
    return { data: rows, totalCount: count };
  }
}

// async getRegistration(startDate: Date | null, endDate: Date | null, month: number | null, year: number | null) {
//   console.log(month, 'month', year, 'year');
//   let validatedStartDate: Date;
//   let validatedEndDate: Date;

//   if (startDate && endDate) {
//     const { startDate: validatedStartDateTemp, endDate: validatedEndDateTemp } = validateAndSetDates(startDate, endDate);
//     validatedStartDate = validatedStartDateTemp;
//     validatedEndDate = validatedEndDateTemp;
//   } else if (month && year) {
//     const { firstDate, lastDate } = getFirstAndLastDatesOfMonth(month, year);
//     validatedStartDate = firstDate;
//     validatedEndDate = lastDate;
//   } else {
//     const currentDate = new Date();
//     validatedEndDate = currentDate;
//     validatedStartDate = new Date(currentDate.getTime() - 7 * 24 * 60 * 60 * 1000);
//   }

//   try {
//     const users = await this.userRoleRepository.findAll({
//       include: ['role', 'user'],
//     });

//     const roleCounts = users.reduce((acc: any, userRole: any) => {
//       const roleName = userRole.role.roleName;
//       if (!acc[roleName]) {
//         acc[roleName] = 0;
//       }
//       acc[roleName]++;
//       return acc;
//     }, {});

//     const result: any = Object.keys(roleCounts).reduce((acc: any, roleName: string) => {
//       acc[roleName] = Number(roleCounts[roleName]);
//       return acc;
//     }, {});

//     const totalSubscriptions = await this.userSubscriptionRepository.count({
//       where: {
//         status: true,
//       },
//     });

//     const subscriptions = await this.userSubscriptionRepository.findAll({
//       where: {
//         created_at: {
//           [Op.between]: [validatedStartDate, validatedEndDate],
//         },
//         status: true,
//       },
//     });

//     let totalRevenue = 0;
//     subscriptions.forEach((subscription: any) => {
//       const price = subscription.price;
//       const discount = subscription.discount || 0;
//       const revenue = price - (price * (discount / 100));
//       totalRevenue += revenue;
//     });

//     result['totalSubscriptions'] = totalSubscriptions;
//     result['totalRevenue'] = totalRevenue;

//     return result;
//   } catch (error) {
//     console.error(error);
//     throw new Error('Failed to retrieve registration data');
//   }
// }

// async getRegistrationWeekAndMonth(startDate: Date | null, endDate: Date | null, month: number | null, year: number | null) {
//   let validatedStartDate: Date;
//   let validatedEndDate: Date;

//   if (startDate && endDate) {
//     const { startDate: validatedStartDateTemp, endDate: validatedEndDateTemp } = validateAndSetDates(startDate, endDate);
//     validatedStartDate = validatedStartDateTemp;
//     validatedEndDate = validatedEndDateTemp;
//   } else if (month && year) {
//     const { firstDate, lastDate } = getFirstAndLastDatesOfMonth(month, year);
//     validatedStartDate = firstDate;
//     validatedEndDate = lastDate;
//   } else {
//     const currentDate = new Date();
//     validatedEndDate = currentDate;
//     validatedStartDate = new Date(currentDate.getTime() - 7 * 24 * 60 * 60 * 1000);
//   }

//   const dailyCounts: { [key: string]: number } = {};
//   let currentDate = new Date(validatedStartDate);
//   while (currentDate <= validatedEndDate) {
//     const nextDay = new Date(currentDate);
//     nextDay.setDate(nextDay.getDate() + 1);
//     const count = await this.userRoleRepository.count({
//       where: {
//         createdAt: {
//           [Op.between]: [currentDate, nextDay],
//         },
//       },
//     });
//     const formattedDate = moment(currentDate).format('DD-MM-YYYY');
//     dailyCounts[formattedDate] = count;
//     currentDate = nextDay;
//   }
//   const totalCount = Object.values(dailyCounts).reduce((acc, count) => acc + count, 0);
//   return {
//     records: dailyCounts,
//     total: totalCount,
//   };
// }

// async allUserSubscriptionList(page: number, limit: number) {
//   const offset = (page - 1) * limit;
//   try {
//     const users = await this.userRolesRepo.findAll({
//       include: ['user', 'role'],
//       offset,
//       limit,
//     });

//     const subscriptions = await this.subscriptionRepository.findAll({
//       include: ['user'],
//     });

//     const userData = users.map((user: any) => {
//       return {
//         id: user.id,
//         role: {
//           roleName: user.role.roleName,
//         },
//         user: {
//           id: user.user.id,
//           name: user.user.name,
//           email: user.user.email,
//           phoneNumber: user.user.phoneNumber,
//           isActive: user.user.isActive,
//           state: user.user.state,
//           city: user.user.city,
//         },
//         subscriptionStatus: getSubscriptionStatus(subscriptions, user.user.id),
//       };
//     });
//     return { data: userData, totalCount: userData.length };

//   } catch (error) {
//     console.error(error);
//     throw new Error('Failed to retrieve user subscription list');
//   }
// }

// async searchAllUserSubscriptionList(name?: string, role?: string, fromDate?: Date, toDate?: Date, page: number = 1, limit: number = 10) {
//   try {
//     const options: any = {
//       include: ['user', 'role'],
//       offset: (page - 1) * limit,
//       limit,
//     };

//     if (name) {
//       options.where = {
//         ...options.where,
//         name: {
//           [Op.iLike]: `%${name}%`,
//         },
//       };
//     }
//     if (role) {
//       options.where = {
//         ...options.where,
//         roleName: {
//           [Op.iLike]: `%${role}%`,
//         },
//       };
//     }
//     if (fromDate && toDate) {
//       options.where = {
//         ...options.where,
//         createdAt: {
//           [Op.between]: [
//             new Date(fromDate.getFullYear(), fromDate.getMonth(), fromDate.getDate(), 0, 0, 0),
//             new Date(toDate.getFullYear(), toDate.getMonth(), toDate.getDate(), 23, 59, 59),
//           ],
//         },
//       };
//     }
//     if (fromDate && !toDate) {
//       options.where = {
//         ...options.where,
//         createdAt: {
//           [Op.between]: [
//             new Date(fromDate.getFullYear(), fromDate.getMonth(), fromDate.getDate(), 0, 0, 0),
//             new Date(fromDate.getFullYear(), fromDate.getMonth(), fromDate.getDate(), 23, 59, 59),
//           ],
//         },
//       };
//     }
//     if (!fromDate && toDate) {
//       options.where = {
//         ...options.where,
//         createdAt: {
//           [Op.between]: [
//             new Date(toDate.getFullYear(), toDate.getMonth(), toDate.getDate(), 0, 0, 0),
//             new Date(toDate.getFullYear(), toDate.getMonth(), toDate.getDate(), 23, 59, 59),
//           ],
//         },
//       };
//     }

//     const users = await this.userRolesRepo.findAll(options);
//     const subscriptions = await this.subscriptionRepository.findAll({
//       include: ['user'],
//     });

//     const userData = users.map((user: any) => {
//       return {
//         id: user.id,
//         role: {
//           roleName: user.role.roleName,
//         },
//         user: {
//           id: user.user.id,
//           name: user.user.name,
//           email: user.user.email,
//           phoneNumber: user.user.phoneNumber,
//           isActive: user.user.isActive,
//           state: user.user.state,
//           city: user.user.city,
//         },
//         subscriptionStatus: getSubscriptionStatus(subscriptions, user.user.id),
//       };
//     });

//     return { data: userData, totalCount: userData.length };
//   } catch (error) {
//     console.error(error);
//     throw new Error('Failed to search user subscription list');
//   }
// }
// }
