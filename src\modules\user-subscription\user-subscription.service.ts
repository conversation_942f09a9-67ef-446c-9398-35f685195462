/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable prettier/prettier */
import {
  Injectable,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { User } from '../admin/authentication/schemas/user.schema';
import { Package, PackageType } from '../subscription/schemas/package.schema';
import { CreateOrderDto } from './dto/order.dto';
import { Role } from '../roles/schemas/role.schema';
import { UserSubscription } from './schemas/user-subscription.schema';
import { Order, OrderStatus, PaymentStatus } from './schemas/order.schema';
import { Transaction, TransactionStatus } from './schemas/transaction.schema';
import { PaymentService } from '../payment/payment.service';
import { SubscriptionStatus } from 'src/common/utils/enum';
import { Op, Sequelize } from 'sequelize';
import { SendgridService } from '../sendgrid/sendgrid.service';
import { Email } from 'src/common/email-template';
import { Constants, TEMPLATE_IDS } from 'src/common/constants';
import { Cron, CronExpression } from '@nestjs/schedule';
import { SmsService } from '../sms/sms.service';
import { NotificationPreference } from '../user-management/schemas/notification-preferences.schema';
const crypto = require('crypto');

@Injectable()
export class UserSubscriptionService {
  constructor(
    private paymentService: PaymentService,
    @InjectModel(User) private userRepository: typeof User,
    @InjectModel(Package) private PackageRepository: typeof Package,
    @InjectModel(UserSubscription)
    private UserSubscriptionRepository: typeof UserSubscription,
    @InjectModel(Order) private orderRepository: typeof Order,
    @InjectModel(Transaction) private transactionRepository: typeof Transaction,
    private readonly sendgridService: SendgridService,
    private readonly smsService: SmsService,
  ) {}

  // @Cron(CronExpression.EVERY_DAY_AT_10AM) // Runs daily at 10AM
  // async handleSubscriptionExpiryCheck() {
  //   const today = new Date();

  //   const subscriptions = await this.UserSubscriptionRepository.findAll({
  //     where: {
  //       status: 'active',
  //       validTill: {
  //         [Op.gte]: today, // Valid till today or later
  //       },
  //     },
  //   });

  //   if (subscriptions.length > 0) {
  //     for (const subscription of subscriptions) {
  //       const user = await this.userRepository.findByPk(subscription.userId, {
  //         include: [
  //           {
  //             model: NotificationPreference,
  //             as: 'notificationPreference',
  //           },
  //         ],
  //       });

  //       if (!user) {
  //         continue;
  //       }

  //       const validTillDate = new Date(subscription.validTill);
  //       const diffTime = validTillDate.getTime() - today.getTime();
  //       const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)); // Difference in days
  //       const userNotificationPreference = user.notificationPreference;
  //       // If exactly 7 days left, send EMAIL
  //       if (diffDays === 7 && user.email && userNotificationPreference?.email) {
  //         await this.sendgridService.sendEmail(
  //           user.email,
  //           Constants.Titles.SubscriptionRenewalDue,
  //           Email.getTemplate({
  //             title: 'Renewal Reminder',
  //             userName: `${user?.firstName} ${user?.lastName}`.trim(),
  //             children: `
  //             <p>
  //             Your subscription is about to expire in 7 days. Renew now to continue enjoying premium features.
  //             </p>
  //           `,
  //           }),
  //         );
  //       }

  //       // If exactly 1 day left, send SMS
  //       if (diffDays === 1 && user.phoneNumber) {
  //         const templateId = TEMPLATE_IDS.Subscription_Renewal_Due;
  //         const payload = {
  //           phone: user.phoneNumber,
  //           templateId,
  //           var1: `${user.firstName} ${user.lastName}`.trim(),
  //         };
  //         await this.smsService.triggerSMS(payload, false);
  //       }
  //     }
  //   }
  // }

  @Cron(CronExpression.EVERY_DAY_AT_10AM) // Runs daily at 10AM
  async handleSubscriptionExpiryCheck() {
    const today = new Date();

    const subscriptions = await this.UserSubscriptionRepository.findAll({
      where: {
        status: 'active',
        validTill: {
          [Op.gte]: today, // Valid till today or later
        },
      },
    });

    if (subscriptions.length > 0) {
      for (const subscription of subscriptions) {
        const user = await this.userRepository.findByPk(subscription.userId, {
          include: [
            {
              model: NotificationPreference,
              as: 'notificationPreference',
            },
          ],
        });

        if (!user) {
          continue;
        }

        const validTillDate = new Date(subscription.validTill);
        const diffTime = validTillDate.getTime() - today.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)); // Difference in days
        const userNotificationPreference = user.notificationPreference;

        // If exactly 7 days left and notification not sent yet, send EMAIL
        if (
          diffDays === 7 &&
          user.email &&
          userNotificationPreference?.email &&
          !subscription.sevenDayReminderSent
        ) {
          await this.sendgridService.sendEmail(
            user.email,
            Constants.Titles.SubscriptionRenewalDue,
            Email.getTemplate({
              title: 'Renewal Reminder',
              userName: `${user?.firstName} ${user?.lastName}`.trim(),
              children: `
              <p>
              Your subscription is about to expire in 7 days. Renew now to continue enjoying premium features.
              </p>
            `,
            }),
          );

          // Mark that 7-day reminder has been sent
          await subscription.update({ sevenDayReminderSent: true });
        }

        // If exactly 1 day left and notification not sent yet, send SMS
        if (
          diffDays === 1 &&
          user.phoneNumber &&
          !subscription.oneDayReminderSent
        ) {
          const templateId = TEMPLATE_IDS.Subscription_Renewal_Due;
          const payload = {
            phone: user.phoneNumber,
            templateId,
            var1: `${user.firstName} ${user.lastName}`.trim(),
          };
          await this.smsService.triggerSMS(payload, false);

          // Mark that 1-day reminder has been sent
          await subscription.update({ oneDayReminderSent: true });
        }
      }
    }
  }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async handleSubscriptionExpiry() {
    const today = new Date();
    const next7Days = new Date(today);
    next7Days.setDate(today.getDate() + 7);

    const subscriptions = await this.UserSubscriptionRepository.findAll({
      where: {
        status: [SubscriptionStatus.ACTIVE],
        validTill: {
          [Op.lt]: today,
        },
        packageType: {
          [Op.ne]: 'default',
        },
      },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'firstName', 'lastName', 'email', 'phoneNumber'],
          include: [
            {
              model: Role,
              as: 'roles',
              attributes: ['roleName'],
              where: {
                roleName: { [Op.notIn]: ['admin', 'super_admin'] },
              },
              through: { attributes: [] },
            },
          ],
        },
      ],
    });
    console.log('subscriptions', subscriptions);
    if (subscriptions.length > 0) {
      for (const subscription of subscriptions) {
        subscription.status = SubscriptionStatus.EXPIRED;
        subscription.save();
        const userRole = subscription.user.roles.map((role) => role.roleName);
        console.log('userRole', userRole);
        const defaultPackage = await this.PackageRepository.findOne({
          where: {
            packageType: 'default',
            role: userRole,
          },
        });
        const defaultSubscription =
          await this.UserSubscriptionRepository.create({
            subscriptionType: 'single',
            packageType: 'default',
            userId: subscription.user.id,
            packageId: defaultPackage.id,
          });
      }
    }
  }

  ////////////////////      ORDER      /////////////////////

  async getAllOrders(userId: number): Promise<Order[]> {
    return this.orderRepository.findAll({
      where: {
        userId,
      },
      include: [
        {
          model: Package,
          as: 'package',
        },
      ],
    });
  }

  async getOneOrder(userId: number, id: number): Promise<Order> {
    const order = await this.orderRepository.findOne({
      where: {
        id,
        userId,
      },
      include: [
        {
          model: Package,
          as: 'package',
        },
      ],
    });
    if (!order) {
      throw new Error('Order not found');
    }
    return order;
  }

  async createOrder(
    userId: number,
    payload: CreateOrderDto,
    subscription: any,
  ) {
    try {
      const user = await this.userRepository.findByPk(userId, {
        include: [
          {
            model: Role,
            as: 'roles',
            attributes: ['roleName'],
            through: { attributes: [] },
          },
          {
            model: UserSubscription,
            as: 'subscription',
          },
        ],
      });

      if (!user) {
        throw new NotFoundException('User not found');
      }
      // Extract user's role(s)
      const userRoles = user.roles.map((role) => role.roleName);
      // Handle dual subscription case
      let totalAmount = 0;
      const discountAmount = 0;
      // Single subscription: get the package for the user's role
      const userRole = userRoles[0];
      const singlePackage = await this.PackageRepository.findOne({
        where: {
          role: userRole,
          id: payload.packageId,
        },
      });
      // console.log('singlePackage:::::', singlePackage);
      if (!singlePackage) {
        throw new NotFoundException('Package for the user role must exist.');
      }
      if (
        subscription &&
        subscription.packageType &&
        subscription.packageType !== 'default' &&
        subscription.packageType !== 'free'
      ) {
        const validFrom = new Date(subscription.validFrom);
        const validTill = new Date(subscription.validTill);
        const today = new Date();

        const totalDays = Math.ceil(
          (validTill.getTime() - validFrom.getTime()) / (1000 * 60 * 60 * 24),
        );

        const dailyPrice = parseFloat(
          (subscription.package.price / totalDays).toFixed(2),
        );

        const remainingDays = Math.max(
          Math.ceil(
            (validTill.getTime() - today.getTime()) / (1000 * 60 * 60 * 24),
          ),
          0,
        );

        const remainingValue = parseFloat(
          (dailyPrice * remainingDays).toFixed(2),
        );

        // Calculate the new total amount
        totalAmount = singlePackage.price - remainingValue;
      } else {
        totalAmount = singlePackage.price;
      }
      console.log('totalAmount::::::::::::', totalAmount);
      const uniqueId =
        crypto.randomBytes(5).toString('hex') +
        Date.now().toString().toUpperCase();

      // Create the order
      const orderData = {
        uniqueId,
        userId: userId,
        packageType: singlePackage.packageType,
        subscriptionType: payload.subscriptionType,
        totalAmount: totalAmount + singlePackage.gst,
        discountAmount,
        status: OrderStatus.PENDING,
        PaymentStatus: PaymentStatus.UNPAID,
        packageId: singlePackage.id,
      };
      // console.log('order:::::::', orderData);

      const order = await this.orderRepository.create(orderData);

      // console.log('order created::::::', order);

      // await order.$set('packages', packageIds);

      return {
        success: true,
        data: order,
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  ///////////////////////////       Subscription      ///////////////////////

  async createFreeSubscription(userId: number, payload: CreateOrderDto) {
    try {
      const user = await this.userRepository.findByPk(userId, {
        include: [
          {
            model: Role,
            as: 'roles',
            attributes: ['roleName'],
            through: { attributes: [] },
          },
          {
            model: UserSubscription,
            as: 'subscription',
          },
        ],
      });

      if (!user) {
        throw new NotFoundException('User not found');
      }
      console.log('User:::::', user);

      // Extract user's role(s)
      const userRoles = user.roles.map((role) => role.roleName);

      console.log('userRoles::::', userRoles);
      const userRole = userRoles[0];

      return {
        success: true,
        // data: order,
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }
  async getAllSubscriptions(userId: number): Promise<UserSubscription[]> {
    return this.UserSubscriptionRepository.findAll({
      where: {
        userId,
      },
      include: [
        {
          model: Transaction,
          include: [
            {
              model: Order,
            },
          ],
        },
        {
          model: Package,
          as: 'package',
        },
        {
          model: Transaction,
          as: 'transaction',
          include: [
            {
              model: Order,
              as: 'order',
            },
          ],
        },
        {
          model: User,
          as: 'user',
          include: [
            {
              model: Role,
              as: 'roles',
              attributes: ['id', 'roleName'],
              through: { attributes: [] },
            },
          ],
          attributes: ['id', 'firstName', 'lastName', 'email', 'phoneNumber'],
        },
      ],
      order: [['createdAt', 'DESC']],
    });
  }

  async getOneSubscription(
    userId: number,
    id: number,
  ): Promise<UserSubscription> {
    const subscription = await this.UserSubscriptionRepository.findByPk(id, {
      include: [
        {
          model: Package,
          as: 'package',
        },
        {
          model: Transaction,
          as: 'transaction',
          include: [
            {
              model: Order,
              as: 'order',
            },
          ],
        },
        {
          model: User,
          as: 'user',
          where: {
            id: userId,
          },
          include: [
            {
              model: Role,
              as: 'roles',
              through: { attributes: [] },
            },
          ],
          attributes: ['id', 'firstName', 'lastName', 'email', 'phoneNumber'],
        },
      ],
    });
    if (!subscription) {
      throw new Error('Subscription not found');
    }
    return subscription;
  }

  async getSubscriptionByUserId(userId: number, page: number, limit: number) {
    const offset = (page - 1) * limit;
    console.log('offset', offset, page, limit);
    console.log('offset');
    const subscription = await this.UserSubscriptionRepository.findAll({
      include: [
        {
          model: Package,
        },
        {
          model: User,
          as: 'user',
          where: {
            id: userId,
          },
          attributes: ['id'],
        },
      ],
      offset,
      limit,
      order: [['createdAt', 'DESC']],
      // logging: true,
    });
    if (!subscription) {
      throw new Error('Subscription not found');
    }
    return subscription;
  }

  ///////////////////////////       transaction      ////////////////////////////

  async getAllTransactions(page: number, limit: number, search?: string) {
    try {
      const offset = (page - 1) * limit;
      const whereCondition = search
        ? Sequelize.literal(`
      (
          "Transaction"."trackingId" = '${search}' 
          OR "subscription"."packageType" ILIKE '%${search}%'
      )
  `)
        : {};

      const { count, rows } = await Transaction.findAndCountAll({
        where: whereCondition,
        include: [
          {
            model: UserSubscription,
            as: 'subscription',
            required: false,
            include: [
              {
                model: Package,
                as: 'package',
              },
            ],
          },
          {
            model: Order,
            as: 'order',
            where: { status: 'completed' },
          },
        ],
        order: [['createdAt', 'DESC']],
        limit,
        offset,
      });

      return {
        totalCount: count,
        transactions: rows,
      };
    } catch (error) {
      console.error('Error fetching transactions:', error);
      throw new Error('Unable to fetch transactions. Please try again later.');
    }
  }

  async getOneTransaction(userId: number, id: number): Promise<Transaction> {
    const transaction = await this.transactionRepository.findByPk(id);
    if (!transaction) {
      throw new Error('Transaction not found');
    }
    return transaction;
  }

  ////////////////////////      payment process       //////////////////////

  async createSubscription(user: User, paymentDto: CreateOrderDto) {
    try {
      console.log('user:::', user.id);
      const userSubscription = await this.UserSubscriptionRepository.findOne({
        where: {
          userId: user.id,
          status: SubscriptionStatus.ACTIVE,
          // packageId: paymentDto.packageId,
        },
        include: [
          {
            model: Package,
            as: 'package',
          },
        ],
        order: [['validTill', 'DESC']],
        limit: 1,
      });
      const packages = await this.PackageRepository.findByPk(
        paymentDto.packageId,
      );
      if (userSubscription) {
        if (userSubscription.isCanceled) {
          throw new Error(
            'You can’t change plans until your canceled subscription expires.',
          );
        }
        const titlePriority = { free: 1, basic: 2, premium: 3 };
        const typePriority = { monthly: 1, quarterly: 2, yearly: 3 };
        if (
          titlePriority[packages.title] ===
          titlePriority[userSubscription.package.title]
        ) {
          if (
            typePriority[packages.packageType] <=
            typePriority[userSubscription.package.packageType]
          ) {
            throw new Error(
              `Cannot downgrade or stay on the same package type (${userSubscription.package.packageType}) for "${userSubscription.package.title}". Please upgrade.`,
            );
          }
        }

        // Allow upgrades to higher titles (e.g., free → basic or basic → premium)
        if (
          titlePriority[packages.title] >
          titlePriority[userSubscription.package.title]
        ) {
          console.log(
            `Upgrading title from ${userSubscription.package.title} to ${packages.title}.`,
          );
        }
        if (userSubscription) {
          // Check if packageId matches and subscription is active
          if (
            (userSubscription.packageId === paymentDto.packageId &&
              userSubscription.status === 'active',
            userSubscription.isCanceled === true)
          ) {
            throw new BadRequestException(
              'Subscription already exists for this package, it is cancled but active',
            );
          }
        }
      }
      // const amount = packages.price - userSubscription.totalAmount;

      const order = await this.createOrder(
        user.id,
        paymentDto,
        userSubscription,
      );
      const newOrder = order.data;
      const paymentdata = {
        orderId: newOrder.id,
        uniqueId: newOrder.uniqueId,
        amount: newOrder.totalAmount,
        name: `${user.firstName} ${user.lastName}`,
        userLanguage: user.accountSettings.appLanguage,
      };
      // console.log('paymentdata::::::', paymentdata);
      const payment = await this.paymentService.initiatePayment(paymentdata);
      return payment;
    } catch (error) {
      // console.log('paymentdata::::::error', error);
      throw new BadRequestException(error.message);
    }
  }

  // Helper method to calculate the subscription end date
  calculateValidTillDate(packageType: string): Date {
    const date = new Date();
    switch (packageType) {
      case 'yearly':
        date.setFullYear(date.getFullYear() + 1);
        break;
      case 'monthly':
        date.setMonth(date.getMonth() + 1);
        break;
      case 'quarterly':
        date.setMonth(date.getMonth() + 3);
        break;
      default:
        throw new Error('Invalid package type');
    }
    return date;
  }

  async cancelSubscription(userId: number, id: number) {
    const subscription = await this.UserSubscriptionRepository.findOne({
      where: { id },
    });
    if (!subscription) {
      throw new Error('Subscription not found');
    }
    if (subscription.userId !== userId) {
      throw new Error('You are not authorized to cancel this subscription');
    }
    subscription.isCanceled = true;
    await subscription.save();
    return subscription;
  }
}
