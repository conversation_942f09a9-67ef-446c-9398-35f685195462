import {
  Table,
  Column,
  Model,
  ForeignKey,
  BelongsTo,
  DataType,
  HasOne,
} from 'sequelize-typescript';
import { User } from 'src/modules/admin/authentication/schemas/user.schema';
import { Transaction } from './transaction.schema';
import { Package } from 'src/modules/subscription/schemas/package.schema';

export enum OrderStatus {
  COMPLETED = 'completed',
  PENDING = 'pending',
  CANCELLED = 'cancelled',
}
export enum PaymentStatus {
  PAID = 'paid',
  UNPAID = 'unpaid',
}

@Table
export class Order extends Model {
  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  uniqueId: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  orderType: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  packageType: string;

  @Column({
    type: DataType.ENUM,
    values: Object.values(OrderStatus),
  })
  status: OrderStatus;

  @Column({
    type: DataType.ENUM,
    values: Object.values(PaymentStatus),
  })
  paymentStatus: PaymentStatus;

  @Column({
    type: DataType.DECIMAL(10, 2),
  })
  totalAmount: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
  })
  discountAmount: number;

  @ForeignKey(() => User)
  @Column
  userId: number;

  @BelongsTo(() => User)
  user: User;

  @ForeignKey(() => Package)
  @Column({
    type: DataType.INTEGER,
    allowNull: false, // Ensure every order has a package
  })
  packageId: number;

  @BelongsTo(() => Package)
  package: Package;

  // Transaction association
  @HasOne(() => Transaction)
  transaction: Transaction;
}
