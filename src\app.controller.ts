import { Controller, Get, Res } from '@nestjs/common';
import { Response } from 'express';
import { AppService } from './app.service';
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}
  @Get()
  getHealth(@Res() res: Response) {
    res.sendStatus(this.appService.getStatus());
  }
  @Get('lang')
  async getHello() {
    return await this.appService.getHello();
  }
}
