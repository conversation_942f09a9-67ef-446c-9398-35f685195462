// error.messages.ts

export const ERROR_MESSAGES = {
  SUPER_ADMIN_ALREADY_EXISTS: 'The super admin already exists',
  ADMIN_ALREADY_EXISTS: 'Sub-Admin already exists',
  ROLE_ALREADY_EXISTS: 'Role already exists',
  ROLE_NOT_FOUND: 'Role not found',
  NOT_AUTHORIZED: 'You are not authorized to perform this action',
  USERNAME_ALREADY_EXISTS:
    'The username you are trying to use already exists. Please choose a different one.',
  USER_ALREADY_EXISTS: 'The user already exists.',
  EMAIL_ALREADY_EXISTS:
    'The email you are trying to use already exists. Please choose a different one.',
  PHONE_NO_ALREADY_EXISTS:
    'The phone number you are trying to use already exists. Please choose a different one.',
  REG_NO_ALREADY_EXISTS:
    'The registration number you are trying to use already exists. Please choose a different one.',
  GSTIN_NO_ALREADY_EXISTS: 'The GSTIN number already exists.',
  EMAIL_NOT_FOUND:
    "We couldn't find any account associated with this email address. Please ensure you've entered it correctly.",
  INVALID_CREDENTIALS:
    'Invalid credentials. Please double-check your username and password and try again.',
  PASSWORD_RESET_NOT_INITIATED:
    "Oops! It seems you haven't requested a password reset.",
  INVALID_OTP:
    'Invalid One-Time Password (OTP). Please double-check and try again',
  OTP_NOT_VERIFIED:
    'Oops! It seems the One-Time Password (OTP) has not been verified yet. Please try again later.',
  INVALID_TOKEN:
    'The provided token is invalid. Please make sure you have the correct token.',
  TOKEN_ALREADY_USED: 'Sorry but this registration has already been utilized.',
  PHONE_NO_NOT_REGISTERED:
    'Sorry, this number is not registered with us. Please check and try again or sign up to create an account.',

  INVALID_SPECIALIZATION_ID:
    'The specialization ID you provided is invalid. Please review and try again.',
  INVALID_USER_ID:
    'The user ID you provided is invalid. Please review and try again.',
  INVALID_APPOINTMENT_ID:
    'The appointment ID you provided is invalid. Please review and try again.',
  INVALID_CONNECTION_ID:
    'The connection ID you provided is invalid. Please review and try again.',
  INVALID_POST_ID:
    'The post ID you provided is invalid. Please review and try again.',
  INVALID_COMMENT_ID:
    'The comment ID you provided is invalid. Please review and try again.',
  INVALID_REPLY_ID:
    'The reply ID you provided is invalid. Please review and try again.',
  INVALID_ARTICLE_ID:
    'The article ID you provided is invalid. Please review and try again.',

  SPECIALIZATION_ALREADY_EXISTS:
    'The Specialization you are trying to use already exists. Please choose a different one.',
  KEY_NOT_FOUND: 'The requested file was not found.',
  ALREADY_FOLLOWING: 'You are already following this doctor.',
  NOT_FOLLOWING: 'You are not currently following this doctor.',
  NOT_LOGGED_IN: 'You must log in first before verifying OTP',
  SLOT_ALREADY_BOOKED: 'Sorry this time slot has already been booked',
  APPOINTMENT_ALREADY_EXISTS:
    'Sorry, you already have an appointment at this time.',
  CAN_NOT_REPORT_YOUR_OWN_POST: "Sorry, you can't report your own post.",
  POST_ALREADY_REPORTED: 'You have already reported this post.',
  CAN_NOT_DELETE_POST: "Sorry, you can't delete post that you didn't create.",
  CAN_NOT_EDIT_POST: "Sorry, you can't edit post that you didn't create.",
  CAN_NOT_DELETE_COMMENT:
    "Sorry, you can't delete comment that you didn't added.",
  CAN_NOT_DELETE_REPLY: "Sorry, you can't delete reply that you didn't added.",
  CAN_NOT_EDIT_ARTICLE: "Sorry, you can't edit article that you didn't create.",
  CAN_NOT_ADD_ARTICLE: 'Sorry, you are not allowed to perform this action.',
  CAN_NOT_REPORT_ARTICLE: "Sorry, you can't report your own article.",
  ARTICLE_ALREADY_REPORTED: 'You have already reported this article.',
  ALREADY_RATED: "You've already rated this appointment.",
  TAG_ALREADY_EXISTS: 'Tag with this title already exists',
  USE_CASE_ALREADY_EXISTS: 'Use Case with this title already exists',
  REVIEW_ALREADY_ADDED: 'Product review already added',
  REVIEW_NOT_FOUND: 'Review Not found',
  NOT_ALLOWED_TO_ADD_REVIEW: 'You are not allowed to add review',
  PRODUCT_NOT_FOUND: 'Product not found',
  ACCOUNT_DEACTIVATED: 'Your account has been blocked',
  REQUEST_EXISTS: 'Deletion request submitted already',
  USER_NOT_FOUND: 'User not found',
  UPDATE_LOCATION: 'Update your geo location',
};

export const SUCCESS_MESSAGES = {
  SUCCESSFULLY_LOGGED_IN: 'You have been successfully logged in.',
  LOGOUT:
    "You have been successfully logged out. Remember, you're always welcome back!",
  OTP_SENT: 'An OTP has been sent to your email address.',
  OTP_VERIFIED: 'The OTP has been successfully verified.',
  PASSWORD_CHANGED:
    'Your password has been changed successfully. You can now log in with your new password.',
  USER_REG_INITIATED:
    'User registration process initiated. Kindly verify the OTP to finalize your registration.',
  PATIENT_REG_INITIATED:
    'Patient registration process initiated. Kindly verify the OTP to finalize your registration.',
  OTP_SENT_TO_MOBILE: 'An OTP has been sent to your phone number.',

  DATA_RETRIEVED: 'Data has been retrieved successfully.',
  USER_UPDATED: 'User account has been updated.',
  USER_BLOCKED: 'The user has been blocked successfully.',
  USER_UNBLOCKED: 'The user has been unblocked successfully.',
  PROFILE_PIC_UPDATED: 'Your profile picture has been successfully updated.',

  // Specialization success messages
  SPECIALIZATION_ADDED: 'The Specialization has been added successfully.',
  SPECIALIZATION_UPDATED:
    'The Specialization details have been updated successfully.',
  SPECIALIZATION_DELETED: 'The Specialization has been deleted successfully.',
  CATEGORY_DELETED: 'The Category delete successfully.',
  SPECIALIZATION_BLOCKED: 'The Specialization has been blocked successfully.',
  SPECIALIZATION_UNBLOCKED:
    'The Specialization has been unblocked successfully.',

  // Specialization success messages
  DOCTOR_ADDED: 'The Doctor has been added successfully.',
  DOCTOR_DELETED: 'The Doctor has been deleted successfully.',
  DOCTOR_BLOCKED: 'The Doctor has been blocked successfully.',
  DOCTOR_UNBLOCKED: 'The Doctor has been unblocked successfully.',
  DOCTOR_VERIFIED: 'The Doctor has been verified successfully.',
  DOCTOR_UNVERIFIED: 'The Doctor has been unverified successfully',

  PATIENT_DELETED: 'The Patient has been deleted successfully.',

  INFLUENCER_ADDED: 'The Influencer has been added successfully.',
  INFLUENCER_UPDATED: 'The Influencer has been updated successfully.',
  INFLUENCER_DELETED: 'The Influencer has been deleted successfully.',
  INFLUENCER_BLOCKED: 'The Influencer has been blocked successfully.',
  INFLUENCER_UNBLOCKED: 'The Influencer has been unblocked successfully.',
  INFLUENCER_VERIFIED: 'The influencer has been verified successfully.',
  INFLUENCER_UNVERIFIED: 'The influencer has been unverified successfully',

  // USER_BLOCKED: "The user has been blocked successfully.",
  // USER_UNBLOCKED: "The user has been unblocked successfully.",

  FILE_UPLOADED: 'The file has been uploaded successfully.',

  SCHEDULE_APPOINTMENT_INITIATED:
    'Appointment booking has been initialized. Kindly verify the OTP to finalize your booking.',
  SCHEDULE_APPOINTMENT: 'The appointment has been scheduled successfully',
  RE_SCHEDULE_APPOINTMENT: 'The appointment has been rescheduled successfully',
  CANCEL_APPOINTMENT: 'The appointment has been cancelled successfully',
  FOLLOW_SUCCESSFUL: 'You are now following this user.',
  UNFOLLOW_SUCCESSFUL: 'You have successfully unfollowed this user.',

  USERNAMES_GENERATED: 'Usernames generated successfully.',
  USERNAME_AVAILABLE:
    'Username is available for use. You can proceed with this username.',
  MESSAGE_SENT: 'Message sent successfully.',
  OTP_VERIFIED_PHONE:
    'The OTP for phone number update has been successfully verified and the phone number has been updated.',

  EMAIL_OTP_VERIFIED:
    'The OTP for email update has been successfully verified and the email has been updated.',
  TIME_SLOTS_ADDED: 'Time slots added successfully.',

  PRESCRIPTIONS_ADDED: 'Prescriptions added successfully.',
  POST_ADDED: 'The Post has been added successfully.',
  POST_UPDATED: 'The Post has been updated successfully.',
  POST_DELETED: 'The Post has been deleted successfully.',
  POST_REPORTED: 'The Post has been reported successfully.',
  POST_LIKED: 'The Post has been liked successfully.',
  POST_DISLIKED: 'The Post has been disliked successfully.',
  POST_BLOCKED: 'The Post has been blocked successfully.',
  POST_UNBLOCKED: 'The Post has been unblocked successfully.',

  COMMENT_ADDED: 'The Comment has been added successfully.',
  COMMENT_UPDATED: 'The Comment have been updated successfully.',
  COMMENT_DELETED: 'The Comment has been deleted successfully.',
  COMMENT_LIKED: 'The Comment has been liked successfully.',
  COMMENT_DISLIKED: 'The Comment has been disliked successfully.',

  REPLY_ADDED: 'The reply has been added successfully.',
  REPLY_UPDATED: 'The reply have been updated successfully.',
  REPLY_DELETED: 'The reply has been deleted successfully.',
  REPLY_LIKED: 'The reply has been liked successfully.',
  REPLY_DISLIKED: 'The reply has been disliked successfully.',

  MESSAGES_CLEARED: 'The messages has been cleared successfully.',

  ARTICLE_ADDED: 'The Article has been added successfully.',
  ARTICLE_UPDATED: 'The Article has been updated successfully.',
  ARTICLE_DELETED: 'The Article has been deleted successfully.',
  ARTICLE_LIKED: 'The Article has been liked successfully.',
  ARTICLE_DISLIKED: 'The Article has been disliked successfully.',
  ARTICLE_BLOCKED: 'The Article has been blocked successfully.',
  ARTICLE_UNBLOCKED: 'The Article has been unblocked successfully.',
  ARTICLE_REPORTED: 'The Article has been reported successfully.',

  ROOM_CREATED: 'Room created successfully.',
  RATING_SUBMITTED: 'Rating submitted successfully.',
};

export const notificationType = {
  NEW_BID: 'new_bid',
  JOB_POST: 'job_post',
  JOB_ALERT: 'job_alert',
  BID_ACCEPTED: 'bid_accepted',
  QUOTATION_REQUEST: 'quotation_request',
  QUOTATION_ACCEPTED: 'quotation_accepted',
  QUOTATION_RECIEVED: 'quotation_Recieved',
  NEW_MESSAGE: 'new_message',
  QUOTATION_DISCUSSION: 'quotation_discussion',
  REGISTRATION_SUCCESSFUL: 'user_registered',
  UPDATE_NOTIFICATION_PREFERENCE: 'update_notification_preference',
  UPDATE_PROFILE_SETTINGS: 'update_profile_settings',
  POST_REVIEW: 'post_review',
  REVIEW_REMINDER: 'review_reminder',
  REVIEW_RECIEVED: 'review_recieved',
  REVIEW_REQUESTED: 'review_requested',
  RESPONSE_SUBMITTED: 'response_submitted',
  UPDATE_PROFILE: 'update_profile',
  SUBSCRIPTION_ACTIVATED: 'subscription_activated',
  CONNECTION_REQUEST: 'connection_request',
  CONNECTION_ACCEPTED: 'connection_accepted',
};

export const notificationTitle = {
  POST_NEW_JOB: 'Time to post',
  JOB_ALERT: 'Job Alert',
  NEW_BID: 'You Have a New Bid',
  BID_ACCEPTED: 'Bid Accepted',
  NEW_MESSAGE_RECIEVED: 'New Message',
  QUOTATION_REQUEST: 'Quotation Request',
  QUOTATION_ACCEPTED: 'Quotation Accepted',
  QUOTATION_RECIEVED: 'Quotation Recieved',
  REGISTRATION_SUCCESSFUL: 'Successful Registration',
  NOTIFICATION_PREFERENCE_UPDATED: 'Notification Preferences Updated',
  UPDATE_PROFILE_SETTINGS: 'Profile Settings Updated',
  POST_REVIEW: 'Review Your Experience',
  REVIEW_REMINDER: 'Dont forget to review',
  REVIEW_RECIEVED: 'New Review Received',
  REVIEW_REQUESTED: 'New Review Requested',
  RESPONSE_SUBMITTED: 'Response Submitted',
  UPDATE_PROFILE: 'Complete your Profile',
  SUBSCRIPTION_ACTIVATED: 'Subscription Activated',
  CONNECTION_REQUEST: 'Connection Request',
  CONNECTION_ACCEPTED: 'Connection Accepted',
};
export const notificationMessage = {
  REGISTRATION_SUCCESSFUL: `Your registration is successful. please update your profile`,
  POST_NEW_JOB:
    'It’s been a while since you posted a job. Post one now and get the best quotes.',
  JOB_ALERT:
    'A new job has been posted that matches your preferences. Check it out now.',
  NEW_BID: `A new bid has been placed on your job. Check it out now.`,
  NOTIFICATION_PREFERENCE_UPDATED:
    'Your notification preferences have been updated according to your choices.',

  UPDATE_PROFILE_SETTINGS:
    'Your profile settings have been successfully updated.',

  POST_NEW_REVIEW: 'How was your experience? Leave a review to help others.',
  REVIEW_REMINDER: 'Please take a moment to review your recent experience.',

  REVIEW_RECIEVED: 'You’ve Got a New Review',
  REVIEW_REQUESTED: 'You’ve Got a New Review Request',
  NEW_MESSAGE_RECIEVED: 'You’ve Got a New message',
  RESPONSE_SUBMITTED:
    ' Your response has been successfully submitted. Thank you for your feedback!',
  BID_ACCEPTED: `Your bid has been accepted. Check it out now.`,
  QUOTATION_REQUEST: `A new quotation has been requested. Check it out now.`,
  QUOTATION_ACCEPTED: `A quotation has been accepted . Check it out now.`,
  QUOTATION_RECIEVED: `You have recieved an Quotation for your project. Check it out now.`,
  UPDATE_PROFILE:
    'we noticed you have not completed your profile. please complete it now to unlock all features',
  SUBSCRIPTION_ACTIVATED: (subscriptionType: string) =>
    `Your ${subscriptionType} subscription is now active. Thank you for choosing our service!`,

  CONNECTION_REQUEST: 'You have a new connection request',
  CONNECTION_ACCEPTED: 'Your connection request has been accepted',
};
