import {
  BadRequestException,
  ConflictException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { AdsManagerDto, TrackImpressionDto } from './dto/ads-manager.dto';
import { AdsManager, AdsStatus } from './schemas/ads-manager.schema';
import { InjectModel } from '@nestjs/sequelize';
import { AdsDisplay } from './schemas/ads-display.schema';
import { AdsClick } from './schemas/ads-click.schema';
import { Sequelize } from 'sequelize';
import { Op } from 'sequelize';
import * as moment from 'moment-timezone';

@Injectable()
export class AdsManagerService {
  constructor(
    @InjectModel(AdsManager)
    private readonly adsManagerRepository: typeof AdsManager,
    @InjectModel(AdsDisplay)
    private readonly adsDisplayRepository: typeof AdsDisplay,
    @InjectModel(AdsClick)
    private readonly adsClickRepository: typeof AdsClick,
  ) {}

  // async findAll(page: number, limit: number, status?: AdsStatus) {
  //   try {
  //     const offset = (page - 1) * limit;
  //     const queryOptions: any = {
  //       offset,
  //       limit,
  //     };

  //     // Add status filter if provided
  //     if (status) {
  //       queryOptions.where = { status };
  //     }

  //     return await this.adsManagerRepository.findAndCountAll(queryOptions);
  //   } catch (error) {
  //     // Log error if necessary
  //     console.error(`Failed to find all ads: ${error.message}`);
  //     throw new InternalServerErrorException('An unexpected error occurred.');
  //   }
  // }

  // async findAll(
  //   page: number,
  //   limit: number,
  //   search?: string,
  //   status?: AdsStatus,
  // ) {
  //   try {
  //     const offset = (page - 1) * limit;
  //     const where: any = {};

  //     where[Op.and] = [
  //       {
  //         // Check if today's date is between from_date and to_date (inclusive)
  //         from_date: { [Op.lte]: new Date() }, // from_date is less than or equal to today
  //         to_date: { [Op.gte]: new Date() }, // to_date is greater than or equal to today
  //       },
  //     ];

  //     // If status is provided, add it to the where clause
  //     if (status) {
  //       where.status = status;
  //     }

  //     // If searchTerm is provided, add it to the where clause to filter by title
  //     if (search) {
  //       where.title = { [Op.iLike]: `%${search}%` }; // Case-insensitive search for title
  //     }

  //     const ads = await this.adsManagerRepository.findAll({
  //       offset,
  //       limit,
  //       include: [
  //         {
  //           model: AdsDisplay,
  //           as: 'adDisplays',
  //           attributes: [],
  //           // required: false,
  //         },
  //         {
  //           model: AdsClick,
  //           as: 'adClicks',
  //           attributes: [],
  //           // required: false,
  //         },
  //       ],
  //       attributes: [
  //         'id',
  //         'title',
  //         'description',
  //         'slot',
  //         'file_url',
  //         'cta_external_link',
  //         'target_location',
  //         'status',
  //         'end_time',
  //         'start_time',
  //         'from_date',
  //         'to_date',
  //         [
  //           Sequelize.literal(`(
  //           SELECT COUNT(*)
  //           FROM "AdsDisplays" AS adDisplays
  //           WHERE adDisplays."adsId" = "AdsManager"."id"
  //         )`),
  //           'impressions',
  //         ],
  //         [Sequelize.fn('COUNT', Sequelize.col('adClicks.id')), 'clicks'],
  //       ],
  //       group: ['AdsManager.id', 'adDisplays.id', 'adClicks.id'],
  //       subQuery: false,
  //       where,
  //       order: [['createdAt', 'DESC']],
  //     });

  //     // Separate query to count total number of ads
  //     const totalAdsCount = await this.adsManagerRepository.count({
  //       where,
  //     });
  //     console.log('total ads count:', totalAdsCount);

  //     // Calculate CTR and format the result
  //     const adsWithMetrics = ads.map((ad) => {
  //       const impressions = Number(ad.getDataValue('impressions'));
  //       const clicks = Number(ad.getDataValue('clicks'));
  //       const ctr = impressions > 0 ? (clicks / impressions) * 100 : 0;
  //       return {
  //         ...ad.toJSON(),
  //         impressions,
  //         clicks,
  //         ctr: parseFloat(ctr.toFixed(2)),
  //       };
  //     });
  //     console.log('ads with metrics::::::::', adsWithMetrics);

  //     return {
  //       ads: adsWithMetrics,
  //       total: totalAdsCount,
  //     };
  //     //
  //   } catch (error) {
  //     console.error(`Failed to find all ads: ${error.message}`);
  //     throw new InternalServerErrorException(
  //       `Failed to find all ads: ${error.message}`,
  //     );
  //   }
  // }
  async findAll(
    page: number,
    limit: number,
    search?: string,
    status?: AdsStatus,
    criteria?: string,
  ) {
    try {
      const offset = (page - 1) * limit;
      const where: any = {};

      if (criteria) {
        where.criteria = { [Op.iLike]: `%${criteria}%` };
      }

      if (status) {
        where.status = status;
      }

      if (search) {
        where.title = { [Op.iLike]: `%${search}%` };
      }
      // Query to get ads with impressions and clicks
      const ads = await this.adsManagerRepository.findAll({
        offset,
        limit,
        include: [
          {
            model: AdsDisplay,
            as: 'adDisplays',
            attributes: [],
          },
          {
            model: AdsClick,
            as: 'adClicks',
            attributes: [],
          },
        ],
        attributes: [
          'id',
          'title',
          'description',
          'slot',
          'file_url',
          'cta_external_link',
          'target_location',
          'status',
          'end_time',
          'start_time',
          'from_date',
          'to_date',
          'criteria',
          [
            Sequelize.literal(`(
            SELECT COUNT(*)
            FROM "AdsDisplays" AS adDisplays
            WHERE adDisplays."adsId" = "AdsManager"."id"
          )`),
            'impressions',
          ],
          [Sequelize.fn('COUNT', Sequelize.col('adClicks.id')), 'clicks'],
        ],
        group: ['AdsManager.id'],
        subQuery: false,
        where,
        order: [
          ['slot', 'ASC'],
          ['createdAt', 'DESC'],
        ],
        logging: true,
      });

      // Separate query to count total number of ads without grouping
      const totalAdsCount = await this.adsManagerRepository.count({
        where,
      });
      // Calculate CTR and format the result
      const adsWithMetrics = ads.map((ad) => {
        const impressions = Number(ad.getDataValue('impressions'));
        const clicks = Number(ad.getDataValue('clicks'));
        const ctr = impressions > 0 ? (clicks / impressions) * 100 : 0;
        return {
          ...ad.toJSON(),
          impressions,
          clicks,
          ctr: parseFloat(ctr.toFixed(2)),
        };
      });
      return {
        ads: adsWithMetrics,
        total: totalAdsCount,
      };
    } catch (error) {
      console.error(`Failed to find all ads: ${error.message}`);
      throw new InternalServerErrorException(
        `Failed to find all ads: ${error.message}`,
      );
    }
  }
  async findAllAds(
    page: number,
    limit: number,
    search?: string,
    status?: AdsStatus,
  ) {
    try {
      const offset = (page - 1) * limit;
      const where: any = {};

      // If status is provided, add it to the where clause
      if (status) {
        where.status = status;
      }

      // If search term is provided, add it to the where clause to filter by title
      if (search) {
        where.title = { [Op.iLike]: `%${search}%` }; // Case-insensitive search for title
      }

      // Query to get ads with impressions and clicks
      const ads = await this.adsManagerRepository.findAll({
        offset,
        limit,
        include: [
          {
            model: AdsDisplay,
            as: 'adDisplays',
            attributes: [],
          },
          {
            model: AdsClick,
            as: 'adClicks',
            attributes: [],
          },
        ],
        attributes: [
          'id',
          'title',
          'description',
          'slot',
          'file_url',
          'cta_external_link',
          'target_location',
          'status',
          'end_time',
          'start_time',
          'from_date',
          'to_date',
          'criteria',
          [
            Sequelize.literal(`(
            SELECT COUNT(*)
            FROM "AdsDisplays" AS adDisplays
            WHERE adDisplays."adsId" = "AdsManager"."id"
          )`),
            'impressions',
          ],
          [Sequelize.fn('COUNT', Sequelize.col('adClicks.id')), 'clicks'],
        ],
        group: ['AdsManager.id'],
        subQuery: false,
        where,
        order: [['createdAt', 'DESC']],
      });

      // Separate query to count total number of ads without grouping
      const totalAdsCount = await this.adsManagerRepository.count({
        where,
      });

      // Calculate CTR and format the result
      const adsWithMetrics = ads.map((ad) => {
        const impressions = Number(ad.getDataValue('impressions'));
        const clicks = Number(ad.getDataValue('clicks'));
        const ctr = impressions > 0 ? (clicks / impressions) * 100 : 0;
        return {
          ...ad.toJSON(),
          impressions,
          clicks,
          ctr: parseFloat(ctr.toFixed(2)),
        };
      });
      return {
        ads: adsWithMetrics,
        total: totalAdsCount,
      };
    } catch (error) {
      console.error(`Failed to find all ads: ${error.message}`);
      throw new InternalServerErrorException(
        `Failed to find all ads: ${error.message}`,
      );
    }
  }

  async findOne(id: number) {
    try {
      const ad = await this.adsManagerRepository.findOne({
        where: { id },
        include: [
          {
            model: AdsDisplay,
            as: 'adDisplays',
            attributes: [],
            // required: false,
          },
          {
            model: AdsClick,
            as: 'adClicks',
            attributes: [],
            // required: false,
          },
        ],
        attributes: [
          'id',
          'title',
          'description',
          'slot',
          'file_url',
          'cta_external_link',
          'target_location',
          'status',
          'end_time',
          'start_time',
          'from_date',
          'to_date',
          [
            Sequelize.literal(`(
            SELECT COUNT(*)
            FROM "AdsDisplays" AS adDisplays
            WHERE adDisplays."adsId" = "AdsManager"."id"
          )`),
            'impressions',
          ],
          [Sequelize.fn('COUNT', Sequelize.col('adClicks.id')), 'clicks'],
        ],
        group: ['AdsManager.id', 'adDisplays.id', 'adClicks.id'],
        subQuery: false,
        order: [['createdAt', 'DESC']],
      });
      if (!ad) {
        throw new NotFoundException(`AdsManager with id ${id} not found`);
      }

      const impressions = Number(ad.getDataValue('impressions'));
      const clicks = Number(ad.getDataValue('clicks'));
      const ctr = impressions > 0 ? (clicks / impressions) * 100 : 0;

      return {
        ...ad.toJSON(),
        impressions,
        clicks,
        ctr: parseFloat(ctr.toFixed(2)), // Format CTR to 2 decimal places
      };
    } catch (error) {
      // this.logger.error(`Failed to find ad with id ${id}: ${error.message}`);
      throw new InternalServerErrorException('An unexpected error occurred.');
    }
  }

  async createAdsManager(createAdsManagerDto: AdsManagerDto) {
    try {
      const fromDate = moment(createAdsManagerDto.fromDate);
      const toDate = moment(createAdsManagerDto.toDate);

      const fromDateStartOfDay = fromDate.startOf('day');
      const toDateEndOfDay = toDate.endOf('day');

      const existingAd = await this.adsManagerRepository.findOne({
        where: {
          criteria: createAdsManagerDto.criteria,
          slot: createAdsManagerDto.slot,
        },
      });

      if (existingAd) {
        throw new ConflictException(
          `An ad with criteria ${createAdsManagerDto.criteria} and slot ${createAdsManagerDto.slot} already exists.`,
        );
      }

      // Check if the total ads for the given criteria have reached the limit
      const existingAdsCount = await this.adsManagerRepository.count({
        where: {
          criteria: createAdsManagerDto.criteria,
        },
      });

      if (existingAdsCount >= 5) {
        throw new ConflictException(
          `The selected criteria ${createAdsManagerDto.criteria} already has the maximum of 5 ads.`,
        );
      }
      const adsManager = {
        title: createAdsManagerDto.title,
        description: createAdsManagerDto.description,
        slot: createAdsManagerDto.slot,
        from_date: createAdsManagerDto.fromDate,
        to_date: toDateEndOfDay,
        start_time: createAdsManagerDto.startTime,
        end_time: createAdsManagerDto.endTime,
        cta_external_link: createAdsManagerDto.ctaExternalLink,
        criteria: createAdsManagerDto.criteria,
        target_location: createAdsManagerDto.targetLocation,
        file_url: createAdsManagerDto.file_url,
      };

      const ads = await this.adsManagerRepository.create(adsManager);
      if (!ads) {
        throw new ConflictException(`ads not created`);
      }
      return ads;
    } catch (error) {
      throw new BadRequestException(`${error.message}`);
    }
  }

  async update(id: number, updateAdsManagerDto: AdsManagerDto) {
    try {
      const adsManager = await this.adsManagerRepository.findOne({
        where: { id },
      });
      if (!adsManager) {
        throw new NotFoundException(`AdsManager with id ${id} not found`);
      }

      adsManager.title = updateAdsManagerDto.title;
      adsManager.description = updateAdsManagerDto.description;
      adsManager.slot = updateAdsManagerDto.slot;
      adsManager.from_date = updateAdsManagerDto.fromDate;
      adsManager.to_date = updateAdsManagerDto.toDate;
      adsManager.start_time = updateAdsManagerDto.startTime;
      adsManager.end_time = updateAdsManagerDto.endTime;
      adsManager.cta_external_link = updateAdsManagerDto.ctaExternalLink;
      adsManager.criteria = updateAdsManagerDto.criteria;
      adsManager.target_location = updateAdsManagerDto.targetLocation;
      adsManager.file_url = updateAdsManagerDto.file_url;
      await adsManager.save();
      return adsManager;
    } catch (error) {
      console.log(`Failed to update ad with id ${id}: ${error.message}`);
      throw new InternalServerErrorException('An unexpected error occurred.');
    }
  }

  async remove(id: number) {
    try {
      const ad = await this.adsManagerRepository.findOne({ where: { id } });
      if (!ad) {
        throw new NotFoundException(`AdsManager with id ${id} not found`);
      }
      await ad.destroy();
      return { message: `Ad with id ${id} successfully deleted.` };
    } catch (error) {
      // this.logger.error(`Failed to delete ad with id ${id}: ${error.message}`);
      throw new InternalServerErrorException('An unexpected error occurred.');
    }
  }

  // async adsSearch(title: string, to_date: Date, from_date: Date, limit: number, page: number) {
  //   try {
  //     const offset = (page - 1) * limit;
  //     const whereClause: any = {};

  //     if (title) {
  //       whereClause.title = { [Like]: `${title}%` }; // Adjust if using Sequelize
  //     }
  //     if (from_date) {
  //       whereClause.from_date = from_date;
  //     }
  //     if (to_date) {
  //       whereClause.to_date = to_date;
  //     }

  //     return await this.adsManagerRepository.findAll({
  //       where: whereClause,
  //       offset: offset,
  //       limit: limit,
  //     });
  //   } catch (error) {
  //     // this.logger.error(`Failed to search ads: ${error.message}`);
  //     throw new InternalServerErrorException('An unexpected error occurred.');
  //   }
  // }

  async updateAdsStatus(id: number, updateStatusData: { status?: AdsStatus }) {
    try {
      const ad = await this.adsManagerRepository.findOne({ where: { id } });
      if (!ad) {
        throw new NotFoundException(`AdsManager with id ${id} not found`);
      }

      // Check if status is provided and valid
      if (updateStatusData.status) {
        // Validate the status value
        if (!Object.values(AdsStatus).includes(updateStatusData.status)) {
          throw new BadRequestException(
            `Invalid status value: ${updateStatusData.status}`,
          );
        }
        ad.status = updateStatusData.status;
      }

      await ad.save();
      return { message: 'Ads status successfully updated.' };
    } catch (error) {
      console.error(`Failed to update ad status with id ${id}:`, error);
      throw new InternalServerErrorException('An unexpected error occurred.');
    }
  }

  async trackImpression(
    trackImpressionDto: TrackImpressionDto,
    userId: number,
  ) {
    const adsDisplay = await this.adsDisplayRepository.create({
      adsId: trackImpressionDto.adsId,
      userId: userId,
    });
    return adsDisplay;
  }

  async trackClick(adsId: number, userId: number): Promise<AdsClick> {
    return this.adsClickRepository.create({ adsId, userId });
  }

  async getAdPerformance(adId: number): Promise<any> {
    const [impressions, clicks] = await Promise.all([
      this.adsDisplayRepository.count({ where: { adId } }),
      this.adsClickRepository.count({ where: { adId } }),
    ]);

    const ctr = impressions > 0 ? (clicks / impressions) * 100 : 0;

    return {
      impressions,
      clicks,
      ctr: parseFloat(ctr.toFixed(2)),
    };
  }
}
