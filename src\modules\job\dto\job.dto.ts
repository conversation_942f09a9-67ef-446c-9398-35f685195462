import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { JobStatus } from 'src/common/utils/enum';

import {
  IsString,
  IsNumber,
  IsArray,
  IsOptional,
  IsInt,
  ValidateNested,
  IsDate,
  Min,
  registerDecorator,
  ValidationOptions,
  ValidationArguments,
  IsEnum,
} from 'class-validator';

// Custom validator for comparing two number properties
function IsGreaterThan(
  property: string,
  validationOptions?: ValidationOptions,
) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      name: 'isGreaterThan',
      target: object.constructor,
      propertyName: propertyName,
      constraints: [property],
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          const [relatedPropertyName] = args.constraints;
          const relatedValue = (args.object as any)[relatedPropertyName];
          return (
            typeof value === 'number' &&
            typeof relatedValue === 'number' &&
            value > relatedValue
          );
        },
      },
    });
  };
}

// Custom validator for comparing two date properties
function IsAfter(property: string, validationOptions?: ValidationOptions) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      name: 'isAfter',
      target: object.constructor,
      propertyName: propertyName,
      constraints: [property],
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          const [relatedPropertyName] = args.constraints;
          const relatedValue = (args.object as any)[relatedPropertyName];
          return (
            value instanceof Date &&
            relatedValue instanceof Date &&
            value > relatedValue
          );
        },
      },
    });
  };
}

export class JobBiddingDetails {
  @ApiProperty({
    description: 'The minimum amount in the bidding range for the job, in INR.',
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  minRange: number;

  @ApiProperty({
    description: 'The maximum amount in the bidding range for the job, in INR.',
  })
  @IsNumber()
  @Min(0)
  @IsGreaterThan('minRange', {
    message: 'maxRange must be greater than minRange',
  })
  @IsOptional()
  maxRange: number;

  @ApiProperty({ description: 'The start date of the job.' })
  @Type(() => Date)
  @IsDate()
  startDate: Date;

  @ApiProperty({ description: 'The end date of the job.' })
  @Type(() => Date)
  @IsDate()
  @IsAfter('startDate', { message: 'endDate must be after startDate' })
  endDate: Date;
}

export class CreateJobDto {
  @ApiProperty({ description: 'The title or name of the job.' })
  @IsString()
  title: string;

  @ApiProperty({ description: 'A detailed description of the job.' })
  @IsString()
  description: string;

  @ApiProperty({
    description:
      'The radius within which the job is applicable, in kilometers.',
    required: false,
  })
  @IsNumber()
  radius: number;

  @ApiProperty({
    description: 'An optional array of image URLs related to the job.',
    type: [String],
    required: false,
  })
  @IsArray()
  @IsOptional()
  images?: string[];

  @ApiProperty({
    description: 'Specifies role',
  })
  @IsInt()
  roleId: number;

  @ApiProperty({
    description: 'The qualifications or criteria required for the job.',
    required: false,
  })
  @IsInt()
  @IsOptional()
  roleSubcategoryId?: number;

  @ApiProperty({
    description: 'The subcategory is required for the job.',
    required: false,
  })
  @IsInt()
  @IsOptional()
  subCategoryId?: number;

  @ApiProperty({
    description: 'enter geometryLocation',
    example: ['24.44444', '22.4444'],
    required: false,
  })
  @IsArray()
  geolocation: string[];

  @ApiProperty({ description: 'The city where the job is located.' })
  @IsString()
  city: string;

  @ApiProperty({ description: 'The state or region where the job is located.' })
  @IsString()
  state: string;

  @ApiProperty({ description: "The postal code for the job's location." })
  @IsString()
  pincode: string;

  @ApiProperty({
    description: 'Bidding details for the job',
    type: JobBiddingDetails,
  })
  @ValidateNested()
  @Type(() => JobBiddingDetails)
  biddingDetail: JobBiddingDetails;
}

export class UpdateJobStatus {
  @ApiProperty()
  @IsEnum(JobStatus)
  status: JobStatus;
}
