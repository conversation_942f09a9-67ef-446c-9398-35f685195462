'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('ReviewRatings', 'jobId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'jobs',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    await queryInterface.addColumn('ReviewRatings', 'quotationId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'RequestQuotations',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('ReviewRatings', 'jobId');
    await queryInterface.removeColumn('ReviewRatings', 'quotationId');
  },
};
