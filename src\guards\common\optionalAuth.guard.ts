import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { CommonService } from 'src/common/common.service';
import { User } from 'src/modules/admin/authentication/schemas/user.schema';

@Injectable()
export class OptinalAuthGuard implements CanActivate {
  constructor(
    private readonly authService: CommonService,
    @InjectModel(User)
    private userRepository: typeof User,
  ) {}

  canActivate = async (context: ExecutionContext): Promise<any> => {
    try {
      const request = context.switchToHttp().getRequest();
      const token = this.extractTokenFromHeader(request);
      if (!token) return true;
      const verify = await this.authService.verifyToken(token);
      if (verify) {
        const result: User = await this.userRepository.findOne({
          where: {
            id: verify._id,
            isActive: true,
          },
        });
        request.user = result;
        return true;
      }
    } catch (err) {
      throw err;
    }
  };
  private extractTokenFromHeader(request: Request): string | undefined {
    const [type, token] = request.headers['authorization']?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
