import { NestFactory } from '@nestjs/core';
import { Logger } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AppModule } from './app.module';
import { ConfigService } from '@nestjs/config';
import { ValidationPipe } from '@nestjs/common';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  app.enableCors();
  app.useGlobalPipes(new ValidationPipe());

  const configService = app.get(ConfigService);
  const config = new DocumentBuilder()
    .setTitle('Apna Builder')
    .setDescription('Apna Builder API Docs')
    .addBearerAuth()
    .setVersion('1.0')
    .addServer('http://localhost:3000', 'local server')
    .addServer('http://localhost:3001', 'local server')
    .addServer(configService.get('BACKEND_URL'), 'development server')
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('/api-docs', app, document);
  await app.listen(configService.get('PORT'));
  Logger.log(
    `🚀 Application is running in "${configService.get('NODE_ENV')}" mode on "${configService.get('BACKEND_URL')}"`,
  );
}
bootstrap();
