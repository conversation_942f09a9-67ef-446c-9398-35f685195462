'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    /**
     * Add sevenDayReminderSent and oneDayReminderSent columns to UserSubscriptions table
     * These flags track whether subscription expiration reminders have been sent to users
     */
    try {
      // Check if columns exist before adding them
      const tableInfo = await queryInterface.describeTable('UserSubscriptions');
      
      // Add sevenDayReminderSent column if it doesn't exist
      if (!tableInfo.sevenDayReminderSent) {
        await queryInterface.addColumn('UserSubscriptions', 'sevenDayReminderSent', {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: false,
        });
        console.log('Column sevenDayReminderSent added to UserSubscriptions table');
      } else {
        console.log('Column sevenDayReminderSent already exists in UserSubscriptions table, skipping...');
      }
      
      // Add oneDayReminderSent column if it doesn't exist
      if (!tableInfo.oneDayReminderSent) {
        await queryInterface.addColumn('UserSubscriptions', 'oneDayReminderSent', {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: false,
        });
        console.log('Column oneDayReminderSent added to UserSubscriptions table');
      } else {
        console.log('Column oneDayReminderSent already exists in UserSubscriptions table, skipping...');
      }
    } catch (error) {
      console.error('Error in migration:', error.message);
    }
  },

  async down(queryInterface, Sequelize) {
    /**
     * Remove the added columns from UserSubscriptions table
     */
    try {
      // Check if columns exist before removing them
      const tableInfo = await queryInterface.describeTable('UserSubscriptions');
      
      // Remove sevenDayReminderSent column if it exists
      if (tableInfo.sevenDayReminderSent) {
        await queryInterface.removeColumn('UserSubscriptions', 'sevenDayReminderSent');
        console.log('Column sevenDayReminderSent removed from UserSubscriptions table');
      } else {
        console.log('Column sevenDayReminderSent does not exist in UserSubscriptions table, skipping...');
      }
      
      // Remove oneDayReminderSent column if it exists
      if (tableInfo.oneDayReminderSent) {
        await queryInterface.removeColumn('UserSubscriptions', 'oneDayReminderSent');
        console.log('Column oneDayReminderSent removed from UserSubscriptions table');
      } else {
        console.log('Column oneDayReminderSent does not exist in UserSubscriptions table, skipping...');
      }
    } catch (error) {
      console.error('Error in migration rollback:', error.message);
    }
  }
};
