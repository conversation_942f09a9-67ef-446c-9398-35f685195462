import { UserSubCategories } from 'src/modules/user-management/schemas/user-categories.schema';

export const categoriesData = [
  { name: 'Commercial Property Owner', roleId: 3 }, // buyer
  { name: 'Home Owner', roleId: 3 },
  { name: 'Property Developer / Commercial Builder', roleId: 3 },
  {
    name: 'Road & Bridge Concessionaire',
    roleId: 3,
  },
  //
  {
    name: 'Architech', // architect
    roleId: 7,
  },
  {
    name: 'Vastu Consultant',
    roleId: 7,
  },
  {
    name: 'Cost / Planning Consultant',
    roleId: 7,
  },
  {
    name: 'Project Manager / Consultant',
    roleId: 7,
  },
  {
    name: 'Interior Designer',
    roleId: 7,
  },
  {
    name: 'Structure Engineer',
    roleId: 7,
  },
  {
    name: 'Safety / Code Inspector',
    roleId: 7,
  },
  {
    name: 'Quality Control Inspector',
    roleId: 7,
  },
  {
    name: 'AAC Blocks / Bricks', //seller
    roleId: 4,
  },
  {
    name: 'AC / Ventilation / Heating / HVAC',
    roleId: 4,
    subCategory: [
      { name: 'Centralised Air Condition' },
      { name: 'Cooler Evaporative' },
      { name: 'Ductless Mini Split System' },
      { name: 'Split System Air Condition' },
      { name: 'VRV / VRF' },
      { name: 'Window Air Condition' },
      { name: 'Air Purifiers' },
      { name: 'Attic Fans' },
      { name: 'Ceiling Fans' },
      { name: 'Chimney / Ventilation Hood' },
      { name: 'Dust Collector' },
      { name: 'Exhaust Fans' },
      { name: 'Fume Extractor' },
      { name: 'Industrial Exhausts' },
      { name: 'Boilers' },
      { name: 'Electric Heaters' },
      { name: 'Furnaces' },
      { name: 'Gas Heaters' },
      { name: 'Heat Pumps' },
      { name: 'Industrial Heaters' },
      { name: 'Radiant Heaters' },
    ],
  },
  {
    name: 'Anti Termite Solutions',
    roleId: 4,
  },
  {
    name: 'Asphalt',
    roleId: 4,
  },
  {
    name: 'AutoCAD / BricsCAD Design Software',
    roleId: 4,
  },
  {
    name: 'Brick / Kiln',
    roleId: 4,
  },
  {
    name: 'Burglar Alarms / Access Control',
    roleId: 4,
  },
  {
    name: 'Cables / Wires / Electrical Fittings',
    roleId: 4,
    subCategory: [
      { name: 'Cable Ties' },
      { name: 'Cat 6 Ethernet cables' },
      { name: 'Circuit Breaker / MCB' },
      { name: 'Conduit Connector' },
      { name: 'Electrical Panels' },
      { name: 'Electrical Wire' },
      { name: 'ETM Conduit' },
      { name: 'Fuse Box' },
      { name: 'Junction Box' },
      { name: 'Outlet Box' },
      { name: 'PVC Conduit' },
      { name: 'USB Outlets' },
      { name: 'Wall Sconces' },
      { name: 'Switches / Sockets' },
    ],
  },
  {
    name: 'Canopy',
    roleId: 4,
  },
  {
    name: 'CCTV Cameras',
    roleId: 4,
  },
  {
    name: 'Cement / Building Material',
    roleId: 4,
    subCategory: [
      { name: 'Cement PPC / OPC' },
      { name: 'White Cement' },
      { name: 'Premix Sand Cement Mortar' },
      { name: 'Putty / filler / Primer' },
      { name: 'Plaster of Paris (POP)' },
      { name: 'Gypsum Board / PVC Panels' },
      { name: 'Foam Board' },
      { name: 'Mineral Wool' },
      { name: 'Admixture' },
      { name: 'Block Adhesive' },
    ],
  },
  {
    name: 'Chandelier / Jhoomar',
    roleId: 4,
  },
  {
    name: 'Construction Chemicals',
    roleId: 4,
  },
  {
    name: 'Curtains / Shutter / Blinds',
    roleId: 4,
  },
  {
    name: 'Fiber-Reinforced Polymers (FRP)',
    roleId: 4,
    subCategory: [
      { name: 'Polypropylene Fiber' },
      { name: 'Polyester Fiber' },
      { name: 'Carbon Fiber Polymer' },
      { name: 'Steel Fiber' },
      { name: 'Glass Fiber' },
      { name: 'Synthetic Fiber' },
      { name: 'Basalt Fiber' },
    ],
  },
  {
    name: 'Fire Safety & Protection',
    roleId: 4,
  },
  {
    name: 'Flush Doors / Panel Doors',
    roleId: 4,
  },
  {
    name: 'Furniture / Wardrobes',
    roleId: 4,
  },
  {
    name: 'Garden Irrigation System / Grass Turf',
    roleId: 4,
  },

  {
    name: 'General Electrical',
    roleId: 4,
    subCategory: [
      { name: 'Coffee Makers' },
      { name: 'Dish Washer' },
      { name: 'Gaming Consoles' },
      { name: 'Hair Dryers' },
      { name: 'Microwaves' },
      { name: 'Refrigerators' },
      { name: 'Speakers / Home Theater' },
      { name: 'Stove / Hobs' },
      { name: 'Toasters' },
      { name: 'TV / LCD /LED / OLED' },
      { name: 'Vacuum Cleaner Machine' },
      { name: 'Washing Machines' },
    ],
  },
  {
    name: 'Generators',
    roleId: 4,
  },
  {
    name: 'Glass Bricks / Glass Blocks',
    roleId: 4,
  },
  {
    name: 'Glass / Tempered Glass / Glazed Glass',
    roleId: 4,
  },
  {
    name: 'Hardware',
    roleId: 4,
    subCategory: [
      { name: 'Accessories' },
      { name: 'Adhesive' },
      { name: 'Bungee Cords' },
      { name: 'Cable Cutter' },
      { name: 'Clamps' },
      { name: 'Drills' },
      { name: 'Fastners' },
      { name: 'First Aid Kits' },
      { name: 'Flooring Material' },
      { name: 'Hammer / Nails' },
      { name: 'Helmets / Gloves /Safety Shoes' },
      { name: 'Hinges' },
      { name: 'Insulation Material' },
      { name: 'Ladder' },
      { name: 'Levels' },
      { name: 'Measuring Tape' },
      { name: 'Nuts & Bolts' },
      { name: 'Revets' },
      { name: 'Saftey Equipments' },
      { name: 'Saw' },
      { name: 'Screws / Screwdriver' },
      { name: 'Sealents' },
      { name: 'Wrenches' },
      { name: 'Fencing Material / Barb Wire' },
    ],
  },
  {
    name: 'Home Decor / Sculpture / Artifacts / Paintings',
    roleId: 4,
  },
  {
    name: 'Home Insurance',
    roleId: 4,
  },
  {
    name: 'Home Loan',
    roleId: 4,
  },
  {
    name: 'Inverters / Batteries',
    roleId: 4,
  },
  {
    name: 'Kitchenware / Modular Kitchen',
    roleId: 4,
  },
  {
    name: 'Lift / Elevator / Escalator',
    roleId: 4,
  },
  {
    name: 'Lighting',
    roleId: 4,
  },
  {
    name: 'Liquefied Petroleum Gas (LPG) Plant',
    roleId: 4,
  },
  {
    name: 'Marble / Stone / Granite',
    roleId: 4,
    subCategory: [
      { name: 'Granite' },
      { name: 'Green Marble' },
      { name: 'Italian Marble' },
      { name: 'katni' },
      { name: 'Kota Stone' },
      { name: 'Lime Stone' },
      { name: 'Makrana Marble' },
      { name: 'Quartz' },
      { name: 'Quartzite' },
      { name: 'Rajnagar Marble' },
      { name: 'Sand Stone' },
      { name: 'Slate' },
      { name: 'Terrazzo' },
      { name: 'Onxy Marble' },
      { name: 'White Marble' },
      { name: 'Travertine Stone' },
    ],
  },
  {
    name: 'Mattress',
    roleId: 4,
  },
  {
    name: 'Metal / Steel Sheets',
    roleId: 4,
    subCategory: [
      { name: 'Aluminized Steel Sheets' },
      { name: 'Corrugated Steel Sheets' },
      { name: 'Electrogalvanized Steel Sheets' },
      { name: 'Galvanized Steel Sheets' },
      { name: 'Hot Rolled Steel Sheets' },
      { name: 'stainless Steel Sheets' },
    ],
  },
  {
    name: 'Nursery / Gardening / Planters',
    roleId: 4,
  },
  {
    name: 'Paint / Colour / Coating',
    roleId: 4,
  },
  {
    name: 'Plumbing Pipes / Sewerage',
    roleId: 4,
    subCategory: [
      { name: 'CPVC Pipes' },
      { name: 'GI Pipes' },
      { name: 'HDPE Pipes' },
      { name: 'Composite Pipes / Kitec' },
      { name: 'PEX' },
      { name: 'PPR Pipes' },
      { name: 'PVC Pipes' },
      { name: 'Stainless Steel Pipes' },
      { name: 'UPVC Pipes' },
      { name: 'GRP Sewer Pipes' },
      { name: 'Cast Iron Sewer Pipes' },
      { name: 'Concrete Sewer Pipes' },
      { name: 'Ductile Iron Sewer Pipes' },
    ],
  },
  {
    name: 'Power Tools',
    roleId: 4,
  },
  {
    name: 'Precast Walls / Precast Slab / Concrete Block',
    roleId: 4,
  },
  {
    name: 'Pump & Submersible',
    roleId: 4,
  },
  {
    name: 'Railing',
    roleId: 4,
  },
  {
    name: 'Ready Mix Concrete ( RMC )',
    roleId: 4,
  },
  {
    name: 'Roofing Panels / Shingle / Ceiling / Insulation',
    roleId: 4,
    subCategory: [
      { name: 'Acoustic Roofing Panels' },
      { name: 'Asphalt Shingles' },
      { name: 'Clay Roofing Tiles' },
      { name: 'Cellulose Eco Insulation' },
      { name: 'Concrete Roofing Tiles' },
      { name: 'Composite Shingles' },
      { name: 'Fiber Glass Insulation' },
      { name: 'Fiber Cement Roofing Panels' },
      { name: 'Grid Ceiling' },
      { name: 'Mineral Wool Insulation' },
      { name: 'Natural Fiber Insulation' },
      { name: 'Metal Shingles' },
      { name: 'Metal Roofing Panels' },
      { name: 'Wood Shingles' },
      { name: 'Rigid Foam Board Insulation' },
      { name: 'Slate Shingles' },
      { name: 'Tile Shingles' },
      { name: 'Spray Foam Insulation' },
    ],
  },
  {
    name: 'Safety Equipment',
    roleId: 4,
  },
  {
    name: 'Sand / Aggregate',
    roleId: 4,
    subCategory: [
      { name: 'Bio-Mining Road Material' },
      { name: 'Core Sand' },
      { name: 'Fine Sand' },
      { name: 'Dust' },
      { name: 'Crushed Stone / Gatka' },
      { name: 'Concrete / Bajri' },
      { name: 'Fly Ash' },
      { name: 'Slag' },
    ],
  },
  {
    name: 'Sanitaryware / Bath / Toilet',
    roleId: 4,
    subCategory: [
      { name: 'Bath Tubs' },
      { name: 'Conceal Body / Tanks' },
      { name: 'Diverters & Shower Valves' },
      { name: 'Faucets / Taps' },
      { name: 'Flushing System' },
      { name: 'Sanitaryware' },
      { name: 'Sauna' },
      { name: 'Shower Enclosure' },
      { name: 'Shower Panel' },
      { name: 'Showers' },
      { name: 'Spas / Jacuzzi / Oxypool' },
      { name: 'Steam Bath Solutions' },
      { name: 'Thermostatic Mixers' },
      { name: 'Water Heaters' },
    ],
  },
  {
    name: 'Scaffolding / Shuttering',
    roleId: 4,
  },
  {
    name: 'Security Services / Stationery Guards',
    roleId: 4,
  },
  {
    name: 'Service Providers',
    roleId: 4,
    subCategory: [
      { name: 'Air Conditioning Service' },
      { name: 'Sofa Dry Cleaning Service' },
      { name: 'Sewage Tank Cleaning Service' },
      { name: 'Liquefied Petroleum Gas (LPG)' },
      { name: 'Home Cleaning Service' },
      { name: 'House Maid Service' },
      { name: 'Internet / Dual Band / Networking Service' },
    ],
  },
  {
    name: 'Sewage Treatment',
    roleId: 4,
  },
  {
    name: 'Sign Board / Flex Board / Name Plate',
    roleId: 4,
  },
  {
    name: 'Soil Testing / Compaction Testing',
    roleId: 4,
  },
  {
    name: 'Solar Energy / Solar Panels',
    roleId: 4,
  },
  {
    name: 'Steel / TMT Bar',
    roleId: 4,
  },
  {
    name: 'Street Lamps / Poles',
    roleId: 4,
  },
  {
    name: 'Tiles / Wooden Flooring',
    roleId: 4,
    subCategory: [
      { name: 'Cement Tiles' },
      { name: 'Bamboo Flooring' },
      { name: 'Ceremic Tiles' },
      { name: 'Glass Tiles' },
      { name: 'Laminate Flooring' },
      { name: 'Mosaic Tiles' },
      { name: 'Parquet Flooring' },
      { name: 'Porcelain Tiles' },
      { name: 'Stone Tiles' },
      { name: 'Wooden Flooring' },
      { name: 'Vetrified Tiles' },
      { name: 'Vinyl Tiles' },
    ],
  },
  {
    name: 'Trucks / Machinery / Equipment Rental',
    roleId: 4,
    subCategory: [
      { name: 'Boom Loader' },
      { name: 'Concrete Mixer' },
      { name: 'Concrete Pump' },
      { name: 'Cranes' },
      { name: 'Excavator' },
      { name: 'Generators' },
      { name: 'JCB Backhoe Loader' },
      { name: 'Man Lift' },
      { name: 'Pavers' },
      { name: 'Pickup' },
      { name: 'Road Roller' },
      { name: 'Skid Steer Loader' },
      { name: 'Tipper Truck ( 600-1000 Ft. )' },
      { name: 'Tower Crane' },
      { name: 'Tractor Trolly' },
      { name: 'Trailer' },
      { name: 'Vibrator' },
      { name: 'Wheel Excavactor' },
      { name: 'Wheel Loader' },
      { name: 'Forklift' },
      { name: 'Portable Toilets' },
      { name: 'Temporary Site Offices' },
      { name: 'Arial Work Platform' },
    ],
  },
  {
    name: 'UPVC & Aluminium Windows / Doors',
    roleId: 4,
  },
  {
    name: 'Wallpaper',
    roleId: 4,
  },
  {
    name: 'Waste Collection / Waste Management',
    roleId: 4,
  },
  {
    name: 'Water Purifier / RO',
    roleId: 4,
  },
  {
    name: 'Water Tanker / 100 Ft. Trolley',
    roleId: 4,
  },
  {
    name: 'Waterproofing / Weatherproofing',
    roleId: 4,
  },
  {
    name: 'Wind Turbine Motionless / Bladeless',
    roleId: 4,
  },
  {
    name: 'Wood / Teakwood / Plywood / Sal',
    roleId: 4,
    subCategory: [
      { name: 'Hard Wood' },
      { name: 'Kail Wood' },
      { name: 'Pine Wood' },
      { name: 'Ghana Teak (Sagwan)' },
      { name: 'Ivory Cost Teak (Sagwan)' },
      { name: 'Myanmar Teak (Sagwan)' },
      { name: 'MP Teak (Sagwan)' },
      { name: 'Nigerian Teak (Sagwan)' },
      { name: 'Sudan Teak (Sagwan)' },
      { name: 'Plywood' },
      { name: 'Kapur Sal' },
      { name: 'Malaysian Sal (Truck body)' },
      { name: 'Red Sal' },
      { name: 'Veneer / Laminates' },
    ],
  },
  { name: 'Backfilling Contractor', roleId: 5 },
  { name: 'Boring / Pilling Contractor', roleId: 5 },
  { name: 'Civil Contractor / Thekedar', roleId: 5 },
  { name: 'Defence Contractor', roleId: 5 },
  { name: 'Demolition Contractor', roleId: 5 },
  { name: 'Electric Contractor', roleId: 5 },
  { name: 'Flooring Contractor', roleId: 5 },
  { name: 'Joinery / Wood Contractor', roleId: 5 },
  { name: 'Kitchen Contractor', roleId: 5 },
  { name: 'Landscaping Contractor', roleId: 5 },
  { name: 'Painting Contractor', roleId: 5 },
  { name: 'Plumbing / Sanitary Contractor', roleId: 5 },
  { name: 'Rain Water Harvesting Contractor', roleId: 5 },
  { name: 'Renovation Contractor', roleId: 5 },
  { name: 'Road Contractor', roleId: 5 },
  { name: 'Roofing / Ceiling Contractor', roleId: 5 },
  { name: 'Scaffolding / Shuttering Contractor', roleId: 5 },
  { name: 'Steel Binding Contractor', roleId: 5 },
  { name: 'Swimming Pool Contractor', roleId: 5 },
  { name: 'Waterproofing Contractor / Applicator', roleId: 5 },
  { name: 'Weatherproofing Contractor', roleId: 5 },
  {
    name: 'Skilled Labour',
    roleId: 6,
    subCategory: [
      { name: 'Carpenter' },
      { name: 'Electrician' },
      { name: 'Gardner' },
      { name: 'Mason (Mistry)' },
      { name: 'Painter' },
      { name: 'Plumber' },
      { name: 'Welder & Gas Cutter' },
      { name: 'Other (Mention)' },
    ],
  },
  { name: 'Unskilled Labour', roleId: 6, subCategory: [{ name: 'Helper' }] },
];
