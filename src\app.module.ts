import { Module } from '@nestjs/common';
import { App<PERSON>ontroller } from './app.controller';
import { AppService } from './app.service';
import { ConfigModule } from '@nestjs/config';
import { AdminModule } from './modules/admin/admin.module';
import { DatabaseModule } from './database/database.module';
import { GuardsModule } from './guards/guards.module';
import { AuthModule } from './modules/auth/auth.module';
import { UserManagementModule } from './modules/user-management/user-management.module';
import { ConnectionModule } from './modules/connection/connection.module';
import { AdsManagerModule } from './modules/ads-manager/ads-manager.module';
import { SubscriptionModule } from './modules/subscription/subscription.module';
import { UploadModule } from './modules/upload/upload.module';
import { JobsModule } from './modules/job/job.module';
import { ExportModule } from './modules/export/export.module';
import { RoleModule } from './modules/roles/roles.module';
import { ServiceModule } from './modules/service/service.module';
import { SkillModule } from './modules/skill/skill.module';
import { UserJobsModule } from './modules/user-jobs/user-jobs.module';
import { UserJobBidsModule } from './modules/user-job-bids/user-job-bids.module';
import { DiscussionModule } from './modules/discussion/discussion.module';
import { CategoryModule } from './modules/ category/category.module';
import { ReviewRatingModule } from './modules/review-rating/review-rating.module';
import { NotificationModule } from './modules/notification/notification.module';
import * as path from 'path';
import {
  I18nModule,
  AcceptLanguageResolver,
  QueryResolver,
  HeaderResolver,
} from 'nestjs-i18n';
import { FirebaseModule } from './modules/firebase/firbase.module';
import { UserSubscriptionModule } from './modules/user-subscription/user-subscription.module';
import { PaymentModule } from './modules/payment/payment.module';
import { SendgridModule } from './modules/sendgrid/sendgrid.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    I18nModule.forRootAsync({
      useFactory: () => ({
        fallbackLanguage: 'en',
        loaderOptions: {
          path: path.join(__dirname, '/i18n/'),
          watch: true,
        },
      }),
      resolvers: [
        { use: QueryResolver, options: ['lang'] },
        AcceptLanguageResolver,
        new HeaderResolver(['accept-language']),
      ],
    }),
    DatabaseModule,
    AdminModule,
    GuardsModule,
    AuthModule,
    UserManagementModule,
    ConnectionModule,
    AdsManagerModule,
    SubscriptionModule,
    JobsModule,
    UploadModule,
    ExportModule,
    ServiceModule,
    UserJobsModule,
    RoleModule,
    SkillModule,
    UserJobBidsModule,
    // SubAdminModule,
    DiscussionModule,
    CategoryModule,
    ReviewRatingModule,
    NotificationModule,
    FirebaseModule,
    UserSubscriptionModule,
    PaymentModule,
    SendgridModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
