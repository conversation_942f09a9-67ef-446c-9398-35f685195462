import {
  <PERSON>,
  Post,
  Patch,
  Get,
  Param,
  Query,
  Delete,
  ParseIntPipe,
  Body,
  UseGuards,
  Request,
} from '@nestjs/common';
import { ConnectionService } from './connection.service';
import { SendConnectionDto } from './dto/send-connection.dto';
import {
  ApiOperation,
  ApiTags,
  ApiQuery,
  ApiBody,
  ApiParam,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { Connection } from './schemas/connection.schema';
import { JWTGuard } from 'src/guards/common/jwt.guard';
@ApiTags('connection')
@Controller('connection')
export class ConnectionController {
  constructor(private readonly connectionService: ConnectionService) {}

  @Post('/send')
  @ApiOperation({ summary: 'Send a connection request' })
  async sendConnection(@Body() sendConnectionDto: SendConnectionDto) {
    return this.connectionService.sendConnection(
      sendConnectionDto.requesterId,
      sendConnectionDto.receiverId,
    );
  }

  @Patch(':requesterId/:receiverId/status')
  @ApiOperation({ summary: 'Update connection status' })
  @ApiParam({
    name: 'requesterId',
    required: true,
    description: 'Requester ID',
  })
  @ApiParam({ name: 'receiverId', required: true, description: 'Receiver ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', enum: ['accept', 'reject'] },
      },
    },
  })
  async updateConnectionStatus(
    @Param('requesterId') requesterId: number,
    @Param('receiverId') receiverId: number,
    @Body('status') status: 'accept' | 'reject',
  ): Promise<Connection> {
    return this.connectionService.updateConnectionStatus(
      requesterId,
      receiverId,
      status,
    );
  }

  @Get('/pending_connections')
  @ApiOperation({ summary: 'Get all pending connection requests' })
  @ApiQuery({
    name: 'userId',
    required: true,
    type: Number,
    description: 'User ID',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of records per page',
  })
  async getPendingConnections(
    @Query('userId') userId: number,
    @Query('page') page = 1,
    @Query('limit') limit = 10,
  ): Promise<{ connections: Connection[]; total: number }> {
    return this.connectionService.getPendingConnections(userId, page, limit);
  }

  @Get('accepted/:userId')
  @ApiOperation({ summary: 'Get all accepted connection requests by a user' })
  @ApiParam({ name: 'userId', required: true, description: 'User ID' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of records per page',
  })
  async getAcceptedConnections(
    @Param('userId') userId: string,
    @Query('page') page = 1,
    @Query('limit') limit = 10,
  ): Promise<{ connections: Connection[]; total: number }> {
    return this.connectionService.getAcceptedConnections(userId, page, limit);
  }

  @Get('/user_details')
  @ApiOperation({ summary: 'Get detail of the user' })
  // @UseGuards(JWTGuard,AdminGuard)
  async getUserDetails(@Param('id') connectionId: number) {
    return this.connectionService.getUserDetails(connectionId);
  }

  @Get('sent/:userId')
  @ApiOperation({ summary: 'Get all sent connection requests by a user' })
  @ApiParam({ name: 'userId', required: true, description: 'User ID' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of records per page',
  })
  async getSentConnectionRequests(
    @Param('userId') userId: number,
    @Query('page') page = 1,
    @Query('limit') limit = 10,
  ): Promise<{ connections: Connection[]; total: number }> {
    return this.connectionService.getSentConnectionRequests(
      userId,
      page,
      limit,
    );
  }

  @Get(':connectionId')
  @ApiOperation({ summary: 'Get a connection request by ID' })
  // @UseGuards(JWTGuard,AdminGuard)
  async getConnectionById(
    @Param('connectionId', ParseIntPipe) connectionId: number,
  ): Promise<Connection> {
    return this.connectionService.getConnectionById(connectionId);
  }

  @Delete(':connectionId')
  @ApiOperation({ summary: 'Delete a connection by ID' })
  @ApiParam({
    name: 'connectionId',
    required: true,
    description: 'Connection ID',
  })
  async deleteConnection(
    @Param('connectionId', ParseIntPipe) connectionId: number,
  ): Promise<{ message: string }> {
    await this.connectionService.deleteConnection(connectionId);
    return { message: 'Connection successfully deleted' };
  }
  @Patch(':connectionId/verify')
  @ApiBearerAuth()
  @UseGuards(JWTGuard)
  async verifyConnection(
    @Param('connectionId') connectionId: number,
    @Request() req,
  ) {
    return this.connectionService.verifyConnection(connectionId, req.user.id);
  }
}
