import { Exclude } from 'class-transformer';
import {
  BelongsTo,
  BelongsToMany,
  Column,
  DataType,
  ForeignKey,
  HasMany,
  HasOne,
  Model,
  Table,
} from 'sequelize-typescript';
// import { Subscription } from 'src/modules/subscription/schemas/subscription.schema';
import { AdminRole } from '../../schemas/admin-role.schemas';
import { UserRole } from 'src/modules/roles/schemas/usersRole.schema';
import { Role } from 'src/modules/roles/schemas/role.schema';
import { Skill } from 'src/modules/skill/schemas/skill.schema';
import { UserSkills } from 'src/modules/user-management/schemas/user-skills.schema';
import { UserJobs } from 'src/modules/user-jobs/schemas/user-jobs.schema';
import { RequestQuotation } from 'src/modules/service/schemas/quotation-request.schema';
import { Category } from 'src/modules/ category/schemas/category.schema';
import { UserSubCategories } from 'src/modules/user-management/schemas/user-categories.schema';
import { UserCategories } from 'src/modules/user-management/schemas/user-subcategories.schema';
import { AccountSettings } from 'src/modules/user-management/schemas/account-settings.schema';
import { NotificationPreference } from 'src/modules/user-management/schemas/notification-preferences.schema';
import { UserBlock } from 'src/modules/user-management/schemas/user-blocks.schema';
import { Connection } from 'src/modules/connection/schemas/connection.schema';
import { ReviewRating } from 'src/modules/review-rating/schemas/review-rating.schema';
import { UserSubscription } from 'src/modules/user-subscription/schemas/user-subscription.schema';
import { Tags } from 'src/modules/service/schemas/tags.schema';
import { UserTags } from 'src/modules/user-management/schemas/user-tags.schema';

@Table
export class User extends Model {
  @Column({ allowNull: true })
  firstName: string;

  @Column({ allowNull: true })
  lastName: string;

  @Column({ allowNull: true })
  gender: string;

  @Column({ allowNull: true })
  role: string;

  @Column({ allowNull: true, type: DataType.TEXT })
  profilePicture: string;

  @Column({ allowNull: true })
  bio: string;

  @Column({ allowNull: true })
  email: string;

  @Exclude()
  @Column({ allowNull: true })
  password: string;

  @Column({ allowNull: true })
  phoneNumber: string;

  @Column({ allowNull: true, type: DataType.DATEONLY })
  dateOfBirth: string;

  @Column({ allowNull: true })
  address1: string;

  @Column({ allowNull: true })
  address2: string;

  @Column({ allowNull: true })
  city: string;

  @Column({ allowNull: true })
  state: string;

  @Column({ allowNull: true })
  pincode: string;

  @Column({
    type: DataType.GEOGRAPHY('POINT'),
    allowNull: true,
  })
  geolocation: { type: string; coordinates: [number, number] };

  @Exclude()
  @Column({ allowNull: true })
  otp: string;

  @Column({ allowNull: true })
  firmName: string;

  @Column({ allowNull: true })
  officeNumber: string;

  @Column({ allowNull: true })
  website: string;

  @Column({ allowNull: true })
  gstIn: string;

  @Column({ allowNull: true })
  type: string;

  @Exclude()
  @Column({
    allowNull: true,
  })
  verifyOTP: string;

  @Column({ allowNull: true })
  createdBy: string;

  @Column({ defaultValue: true })
  isActive: boolean;

  @Column({ defaultValue: false })
  isVerified: boolean;

  @Column({ defaultValue: false })
  isDeleted: boolean;

  @Column({ defaultValue: false })
  profileCompletionNotificationSent: boolean;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  lastLogin: Date;

  @ForeignKey(() => AdminRole)
  @Column({ allowNull: true })
  adminRoleId: number;

  @BelongsTo(() => AdminRole)
  adminRole: AdminRole;

  @HasMany(() => UserRole)
  userRoles: UserRole[];

  @BelongsToMany(() => Role, () => UserRole)
  roles: Role[];

  @BelongsToMany(() => Skill, () => UserSkills)
  skills: Skill[];

  @BelongsToMany(() => Category, () => UserCategories)
  categories: Category[];

  @BelongsToMany(() => Category, () => UserSubCategories)
  subCategories: Category[];

  @HasMany(() => UserJobs)
  userJobs: UserRole[];

  @Column({ allowNull: true })
  ratePerDay: number;

  @Column({ allowNull: true })
  secondaryPhoneNumber: string;

  @Column({
    type: DataType.ARRAY(DataType.STRING),
    allowNull: true,
  })
  workingDays: string[];

  @Column({ allowNull: true, type: DataType.TIME })
  openingTime: string;

  @Column({ allowNull: true, type: DataType.TIME })
  closingTime: string;

  @HasMany(() => RequestQuotation, 'userId')
  requestQuotations: RequestQuotation[];

  @HasMany(() => RequestQuotation, 'requestedTo')
  receivedQuotations: RequestQuotation[];

  @HasOne(() => AccountSettings)
  accountSettings: AccountSettings;

  @HasOne(() => NotificationPreference)
  notificationPreference: NotificationPreference;

  @HasMany(() => UserBlock, 'blockerId')
  blockedUsers: UserBlock[];

  @HasMany(() => UserBlock, 'blockedId')
  blockedByUsers: UserBlock[];

  @HasMany(() => Connection)
  connections: Connection[];
  @HasMany(() => ReviewRating)
  reviewRatings: ReviewRating[];

  @HasOne(() => UserSubscription)
  subscription: UserSubscription;

  @BelongsToMany(() => Tags, () => UserTags)
  tags: Tags[];
}
