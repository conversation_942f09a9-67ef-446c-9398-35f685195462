import { IsString, IsOptional, IsEnum } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { ValidateNested } from 'class-validator';

enum ProfileVisibility {
  PUBLIC = 'public',
  PRIVATE = 'private',
}

class CreateAccountSettingsDto {
  @ApiProperty()
  @IsOptional()
  @IsString()
  appLanguage?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  communicationLanguage?: string;

  @ApiProperty()
  @IsOptional()
  @IsEnum(ProfileVisibility)
  profileVisibility?: ProfileVisibility;
}

import { IsBoolean } from 'class-validator';
class CreateNotificationPreferencesDto {
  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  jobAlerts?: boolean;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  newQuotation?: boolean;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  newBidRequest?: boolean;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  inAppNotifications?: boolean;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  sms?: boolean;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  email?: boolean;
}

export class CreateUserSettingsDto {
  @ApiProperty()
  @ValidateNested()
  @Type(() => CreateAccountSettingsDto)
  accountSettings: CreateAccountSettingsDto;

  @ApiProperty()
  @ValidateNested()
  @Type(() => CreateNotificationPreferencesDto)
  notificationPreferences: CreateNotificationPreferencesDto;
}
