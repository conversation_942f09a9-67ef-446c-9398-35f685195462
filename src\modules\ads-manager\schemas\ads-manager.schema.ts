import { Table, Column, Model, DataType, HasMany } from 'sequelize-typescript';
import { AdsDisplay } from './ads-display.schema';
import { AdsClick } from './ads-click.schema';
export enum AdsStatus {
  PLAY = 'play',
  PAUSED = 'paused',
}

@Table
export class AdsManager extends Model {
  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  title: string;

  @Column({
    type: DataType.STRING,
  })
  description: string;

  @Column({
    type: DataType.STRING,
  })
  slot: string;
  @Column({
    type: DataType.ENUM,
    values: Object.values(AdsStatus),
    defaultValue: AdsStatus.PLAY,
  })
  status: AdsStatus;

  @Column({
    type: DataType.DATE,
  })
  from_date: Date;

  @Column({
    type: DataType.DATE,
  })
  to_date: Date;

  @Column({
    type: DataType.STRING,
  })
  start_time: string;

  @Column({
    type: DataType.STRING,
  })
  end_time: string;

  @Column({
    type: DataType.STRING,
  })
  cta_external_link?: string;

  @Column({
    type: DataType.STRING,
  })
  criteria: string;

  @Column({
    type: DataType.STRING,
  })
  target_location: string;

  @Column({
    type: DataType.TEXT,
  })
  file_url: string;

  @HasMany(() => AdsDisplay, { foreignKey: 'adsId', as: 'adDisplays' })
  adDisplays: AdsDisplay[];

  @HasMany(() => AdsClick)
  adClicks: AdsClick[];
}
