import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Skill } from './schemas/skill.schema';
import { CreateSkillDto } from './dto/create-skill.dto';

@Injectable()
export class SkillService {
  constructor(
    @InjectModel(Skill)
    private skillModel: typeof Skill,
  ) {}

  async createSkill(createSkillDto: CreateSkillDto): Promise<Skill> {
    const { name } = createSkillDto;
    return this.skillModel.create({
      name,
    });
  }
  async getSkill(query: any): Promise<Skill> {
    return this.skillModel.findOne({ where: query });
  }

  async getSkillsWithCount(
    query: any,
    page: number,
    limit: number,
  ): Promise<{ skills: Skill[]; total: number }> {
    const offset = (page - 1) * limit;

    const { count, rows } = await this.skillModel.findAndCountAll({
      where: query,
      order: [['createdAt', 'DESC']],
      offset,
      limit,
    });

    return { skills: rows, total: count };
  }
}
