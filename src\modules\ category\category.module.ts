import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { Category } from './schemas/category.schema';
import { CategoryService } from './category.service';
import { CategoryController } from './category.controller';
import { CategorySeedService } from './category-seed.service';

@Module({
  imports: [SequelizeModule.forFeature([Category])],
  providers: [CategoryService, CategorySeedService],
  controllers: [CategoryController],
})
export class CategoryModule {}
