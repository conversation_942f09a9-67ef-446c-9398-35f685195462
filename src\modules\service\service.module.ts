import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { Service } from './schemas/service.schema';
import { ServiceService } from './service.service';
import {
  QuotationController,
  ServiceController,
  TagController,
} from './service.controller';
import { Tags } from './schemas/tags.schema';
import { RequestQuotation } from './schemas/quotation-request.schema';
import { ServiceTags } from './schemas/service-tags.schema';
import { RecievedQuotation } from './schemas/quotation-recieved.schema';
import { User } from '../admin/authentication/schemas/user.schema';
import { ProductDetails } from './schemas/quotaion-product.schema';
import { Connection } from '../connection/schemas/connection.schema';
import { NotificationModule } from '../notification/notification.module';
import { SendgridModule } from '../sendgrid/sendgrid.module';
import { UserManagementModule } from '../user-management/user-management.module';
import { UserBlock } from '../user-management/schemas/user-blocks.schema';
import { UserTags } from '../user-management/schemas/user-tags.schema';
import { NotificationPreference } from '../user-management/schemas/notification-preferences.schema';
import { Notification } from '../notification/schema/notification.schema';

@Module({
  imports: [
    SequelizeModule.forFeature([
      Service,
      Tags,
      ServiceTags,
      RequestQuotation,
      RecievedQuotation,
      User,
      ProductDetails,
      Connection,
      UserBlock,
      UserTags,
      NotificationPreference,
      Notification,
    ]),
    SendgridModule,
    NotificationModule,
    UserManagementModule,
  ],
  providers: [ServiceService],
  controllers: [ServiceController, QuotationController, TagController],
})
export class ServiceModule {}
