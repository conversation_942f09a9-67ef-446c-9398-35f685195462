import {
  Injectable,
  Inject,
  NotFoundException,
  Body,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Op } from 'sequelize';
import {
  notificationMessage,
  notificationTitle,
  notificationType,
} from 'src/common/messages';
import { Notification } from './schema/notification.schema';
import { ReviewRating } from '../review-rating/schemas/review-rating.schema';
import { UserDeviceToken } from './schema/user-device-token.schema';
import { createUserDeviceTokenDto } from './dto/notification.dto';
import { FirebaseService } from '../firebase/firebase.service';

@Injectable()
export class NotificationService {
  constructor(
    @InjectModel(Notification)
    private readonly notificationModel: typeof Notification,
    @InjectModel(ReviewRating)
    private readonly reviewRatingModel: typeof ReviewRating,
    @InjectModel(UserDeviceToken)
    private readonly userDeviceTokenModel: typeof UserDeviceToken,
    private readonly firebaseService: FirebaseService,
  ) {}

  async markNotificationAsRead(notificationId: number, receiverId: number) {
    if (notificationId) {
      await this.notificationModel.update(
        { isRead: true },
        { where: { id: notificationId, isRead: false } },
      );
      return { message: 'Notification Read' };
    }

    await this.notificationModel.update(
      { isRead: true },
      { where: { receiverId, isRead: false } },
    );
    return { message: 'All Notifications Read' };
  }

  async getUnreadCount(userId: number) {
    return await this.notificationModel.count({
      where: { receiverId: userId, isRead: false },
    });
  }

  async getNotifications(userId: number, page: number, limit: number) {
    const offset = (page - 1) * limit;
    const notifications = await this.notificationModel.findAll({
      where: {
        receiverId: userId,
      },
      offset,
      limit,
      order: [['createdAt', 'DESC']],
    });

    const count = await this.notificationModel.count({
      where: { receiverId: userId },
    });

    // Mark unread notifications as read
    const unReadNotificationIds = notifications
      .filter((n) => !n.isRead)
      .map((n) => n.id);

    if (unReadNotificationIds.length > 0) {
      await this.notificationModel.update(
        { isRead: true },
        { where: { id: { [Op.in]: unReadNotificationIds } } },
      );
    }
    const enhancedNotifications = await Promise.all(
      notifications.map(async (notification: any) => {
        let isReviewGiven: boolean | null = null;

        if (['post_review', 'review_requested'].includes(notification.type)) {
          let jobId: number | null = null;
          let quotationId: number | null = null;

          // Handle additionalFields being JSONB or string
          if (notification.additional) {
            if (typeof notification.additional === 'string') {
              try {
                const parsed = JSON.parse(notification.additional);
                jobId = parsed.jobId || null;
                quotationId = parsed.quotationId || null;
              } catch (e) {
                console.error('Invalid JSON in additionalFields', e);
              }
            } else {
              jobId = notification.additional.jobId || null;
              quotationId = notification.additional.quotationId || null;
            }
          }

          if (jobId || quotationId) {
            const whereCondition: any = {
              reviewerId: notification.receiverId,
            };

            if (jobId) whereCondition.jobId = jobId;
            if (quotationId) whereCondition.quotationId = quotationId;

            const review = await this.reviewRatingModel.findOne({
              where: whereCondition,
            });
            if (review) {
              isReviewGiven = true;
            } else {
              isReviewGiven = false;
            }
          }
        }

        return {
          ...notification.toJSON(),
          isReviewGiven,
        };
      }),
    );

    return { count, notifications: enhancedNotifications };
  }

  async getMessageNotifications(userId: number) {
    // Find the first unread notification
    const firstUnreadNotification = await this.notificationModel.findOne({
      where: {
        receiverId: userId,
        type: notificationType.NEW_MESSAGE,
        // isRead: false, // Only unread notifications
      },
      order: [['createdAt', 'DESC']], // Fetch the latest unread notification
    });

    // If we find an unread notification, mark it as read
    if (firstUnreadNotification) {
      await this.notificationModel.update(
        { isRead: true }, // Mark as read
        { where: { id: firstUnreadNotification.id } },
      );
    }

    return { notification: firstUnreadNotification };
  }

  async getNotificationById(notificationId: number, userId: number) {
    return await this.notificationModel.findOne({
      where: { id: notificationId, receiverId: userId },
    });
  }

  async deleteNotification(notificationId: number) {
    await this.notificationModel.destroy({ where: { id: notificationId } });
    return {
      message: 'Notification deleted successfully',
    };
  }

  async sendNotification(value: any) {
    console.log('Notification payload:', value);

    switch (value.type) {
      case notificationType.REGISTRATION_SUCCESSFUL:
        value.title = notificationTitle.REGISTRATION_SUCCESSFUL;
        value.message = notificationMessage.REGISTRATION_SUCCESSFUL;
        await this.createNotification(value, true, false);
        break;

      case notificationType.NEW_BID:
        value.title = notificationTitle.NEW_BID;
        value.message = notificationMessage.NEW_BID;
        await this.createNotification(value, true, true);
        break;

      case notificationType.JOB_POST:
        value.title = notificationTitle.POST_NEW_JOB;
        value.message = notificationMessage.POST_NEW_JOB;
        await this.createNotification(value, true, true);
        break;

      case notificationType.JOB_ALERT:
        value.title = notificationTitle.JOB_ALERT;
        value.message = notificationMessage.JOB_ALERT;
        await this.createNotification(value, true, true);
        break;

      case notificationType.UPDATE_NOTIFICATION_PREFERENCE:
        value.title = notificationTitle.NOTIFICATION_PREFERENCE_UPDATED;
        value.message = notificationMessage.NOTIFICATION_PREFERENCE_UPDATED;
        await this.createNotification(value, true, false);
        break;

      case notificationType.UPDATE_PROFILE_SETTINGS:
        value.title = notificationTitle.UPDATE_PROFILE_SETTINGS;
        value.message = notificationMessage.UPDATE_PROFILE_SETTINGS;
        await this.createNotification(value, true, false);
        break;

      case notificationType.POST_REVIEW:
        value.title = notificationTitle.POST_REVIEW;
        value.message = notificationMessage.POST_NEW_REVIEW;
        await this.createNotification(value, true, false);
        break;

      case notificationType.REVIEW_REQUESTED:
        value.title = notificationTitle.REVIEW_REQUESTED;
        value.message = notificationMessage.REVIEW_REQUESTED;
        await this.createNotification(value, true, false);
        break;
      case notificationType.REVIEW_RECIEVED:
        value.title = notificationTitle.REVIEW_RECIEVED;
        value.message = notificationMessage.REVIEW_RECIEVED;
        await this.createNotification(value, true, true);
        break;

      case notificationType.RESPONSE_SUBMITTED:
        value.title = notificationTitle.RESPONSE_SUBMITTED;
        value.message = notificationMessage.RESPONSE_SUBMITTED;
        await this.createNotification(value, true, true);
        break;

      case notificationType.QUOTATION_ACCEPTED:
        value.title = notificationTitle.QUOTATION_ACCEPTED;
        value.message = notificationMessage.QUOTATION_ACCEPTED;
        await this.createNotification(value, true, true);
        break;

      case notificationType.QUOTATION_REQUEST:
        value.title = notificationTitle.QUOTATION_REQUEST;
        value.message = notificationMessage.QUOTATION_REQUEST;
        await this.createNotification(value, true, true);
        break;
      case notificationType.QUOTATION_RECIEVED:
        value.title = notificationTitle.QUOTATION_RECIEVED;
        value.message = notificationMessage.QUOTATION_RECIEVED;
        await this.createNotification(value, true, true);
        break;

      case notificationType.BID_ACCEPTED:
        value.title = notificationTitle.BID_ACCEPTED;
        value.message = notificationMessage.BID_ACCEPTED;
        await this.createNotification(value, true, false);
        break;

      case notificationType.NEW_MESSAGE:
        value.title = notificationTitle.NEW_MESSAGE_RECIEVED;
        value.message = notificationMessage.NEW_MESSAGE_RECIEVED;
        await this.createNotification(value, true, true);
        break;

      case notificationType.UPDATE_PROFILE:
        value.title = notificationTitle.UPDATE_PROFILE;
        value.message = notificationMessage.UPDATE_PROFILE;
        await this.createNotification(value, true, true);
        break;

      case notificationType.SUBSCRIPTION_ACTIVATED:
        value.title = notificationTitle.SUBSCRIPTION_ACTIVATED;
        value.message = notificationMessage.SUBSCRIPTION_ACTIVATED(
          value.packageTitle,
        );
        await this.createNotification(value, true, false);
        break;

      case notificationType.REVIEW_REMINDER:
        value.title = notificationTitle.REVIEW_REMINDER;
        value.message = notificationMessage.REVIEW_REMINDER;
        await this.createNotification(value, true, false);
        break;
      case notificationType.CONNECTION_REQUEST:
        value.title = notificationTitle.CONNECTION_REQUEST;
        value.message = notificationMessage.CONNECTION_REQUEST;
        await this.createNotification(value, true, false);
        break;
      case notificationType.CONNECTION_ACCEPTED:
        value.title = notificationTitle.CONNECTION_ACCEPTED;
        value.message = notificationMessage.CONNECTION_ACCEPTED;
        await this.createNotification(value, true, false);
        break;

      default:
        console.warn('Unknown notification type:', value.type);
        break;
    }
  }

  private async createNotification(data: any, inApp = false, isPush = false) {
    const notification = {
      receiverId: data.receiverId,
      senderId: data.senderId,
      message: data.message,
      additional: data.additional || '',
      type: data.type,
      prefrence: data.prefrence,
      title: data.title,
    };
    if (inApp) {
      console.log('InApp notification data:::', data.receiverId);
      const data1 = await this.notificationModel.create(notification);
      // console.log('InApp notification sent:', data1);
    }
    if (isPush) {
      console.log('Push notification sent:', notification);
      await this.sendFcm(data.receiverId, data);
    }
  }

  // async sendFcm(id: number, payload: any) {
  //   const tokens = await this.userDeviceTokenModel.findOne({
  //     where: {
  //       userId: id,
  //     },
  //   });
  //   const sanitizedData = this.convertDataToString(payload);
  //   const notification = {
  //     title: payload.title,
  //     body: payload.message,
  //   };

  //   if (tokens) {
  //     await this.firebaseService.sendNotificationToDevice(
  //       tokens.token,
  //       notification,
  //       sanitizedData,
  //     );
  //   }
  // }
  async sendFcm(userId: number, payload: any): Promise<void> {
    try {
      // Fetch user device tokens from the database
      const tokens = await this.userDeviceTokenModel.findAll({
        where: {
          userId,
        },
        attributes: ['token'], // Only fetch the token field to reduce overhead
      });

      if (!tokens || tokens.length === 0) {
        console.log(`No device tokens found for user ID: ${userId}`);
        return;
      }

      // Extract token strings from the result
      const tokenList = tokens.map((tokenObj) => tokenObj.token);

      // Prepare notification payload
      const sanitizedData = this.convertDataToString(payload);
      const notification = {
        title: payload.title || 'Notification', // Default title if none is provided
        body: payload.message || 'You have a new message.', // Default message
      };

      await this.firebaseService.sendPushNotification(
        tokenList,
        notification,
        sanitizedData,
      );
    } catch (error) {
      console.error('Error sending FCM notifications:', error);
    }
  }
  private convertDataToString(data: any): Record<string, string> {
    const stringifiedData: Record<string, string> = {};
    Object.keys(data).forEach((key) => {
      stringifiedData[key] = String(data[key]); // Ensure all values are strings
    });
    return stringifiedData;
  }
  ////////////////////////    USER DEVICE TOKEN    /////////////////////////

  async getToken(userId: number): Promise<UserDeviceToken> {
    const token = await this.userDeviceTokenModel.findOne({
      where: { userId },
    });
    if (!token) {
      throw new NotFoundException('Token not found');
    }
    return token;
  }

  // Fetch all tokens based on the query
  async getAll(query: any): Promise<UserDeviceToken[]> {
    return await this.userDeviceTokenModel.findAll({ where: query });
  }

  // Create a new token
  async createToken(
    body: createUserDeviceTokenDto,
    userId: number,
  ): Promise<UserDeviceToken> {
    const existingToken = await this.userDeviceTokenModel.findOne({
      where: { token: body.Token, userId },
    });

    if (existingToken) {
      throw new HttpException('Token already exists', HttpStatus.CONFLICT);
    }
    const payload = {
      token: body.Token,
      userId,
    };
    return await this.userDeviceTokenModel.create(payload);
  }

  // Update an existing token
  async updateToken(
    existingToken: UserDeviceToken,
    body: createUserDeviceTokenDto,
  ): Promise<UserDeviceToken> {
    existingToken.token = body.Token;
    return await existingToken.save();
  }

  // Delete an existing token
  async deleteToken(existingToken: UserDeviceToken) {
    await existingToken.destroy();
  }
}
