import {
  IsNotEmpty,
  IsOptional,
  IsArray,
  IsString,
  IsInt,
  IsNumber,
  IsEnum,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { ServiceStatus } from '../schemas/service.schema';

export class CreateServiceDto {
  @ApiProperty({ description: 'Title of the service' })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({ description: 'Thumbnail of the service' })
  @IsString()
  @IsNotEmpty()
  thumbnail: string;

  @ApiProperty({ type: Number })
  @IsInt()
  @IsOptional()
  categoryId: number;

  @ApiProperty({ type: Number })
  @IsInt()
  @IsOptional()
  subCategoryId: number;

  @ApiProperty({ description: 'Description of the service', required: false })
  @IsString()
  @IsOptional()
  description: string;

  @ApiProperty({
    description: 'Tags for the service',
    required: false,
    type: [Number],
  })
  @IsNumber({}, { each: true })
  @IsArray()
  @IsOptional()
  tags: number[];

  @ApiProperty({ description: 'Suggestion for the service', required: false })
  @IsString()
  @IsOptional()
  suggestion: string;

  // @ApiProperty({ description: 'User ID associated with the service' })
  // @IsNotEmpty()
  // userId: number;

  @ApiProperty({
    description: 'Images for the service',
    required: false,
    type: [String],
  })
  @IsOptional()
  images: string;
}

export class UpdateServiceDto extends CreateServiceDto {
  @ApiProperty({ description: 'Service ID' })
  @IsOptional()
  id: number;
}
export class UpdateServiceStatusDto {
  @ApiProperty({ description: 'Service ID' })
  @IsOptional()
  @IsNotEmpty()
  id: number;

  @ApiProperty()
  @IsEnum(ServiceStatus)
  status: ServiceStatus;
}

export class keywordDto {
  @ApiProperty({ description: 'keyword ID' })
  @IsOptional()
  @IsNotEmpty()
  id: number;

  @ApiProperty({ description: 'keyword name' })
  @IsNotEmpty()
  name: string;
}

// export class UpdatekeywordDto extends keywordDto {

//   }
