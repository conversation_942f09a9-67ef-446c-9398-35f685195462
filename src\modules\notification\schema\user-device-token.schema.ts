import {
  <PERSON>umn,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  BelongsTo,
  Table,
} from 'sequelize-typescript';
import { User } from 'src/modules/admin/authentication/schemas/user.schema';

@Table
export class UserDeviceToken extends Model {
  @Column({
    type: 'TEXT',
    allowNull: false,
  })
  token: string;

  @ForeignKey(() => User)
  @Column({
    type: 'INTEGER',
    allowNull: false,
  })
  userId: number;

  @BelongsTo(() => User, { as: 'user' })
  user: User;
}
