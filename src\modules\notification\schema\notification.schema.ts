import {
  BelongsTo,
  Column,
  DataType,
  ForeignKey,
  Model,
  Table,
} from 'sequelize-typescript';
import { User } from 'src/modules/admin/authentication/schemas/user.schema';

@Table
export class Notification extends Model {
  //   @Column({
  //     allowNull: false,
  //     primaryKey: true,
  //     autoIncrement: true,
  //     type: DataType.INTEGER,
  //   })
  //   id: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  title: string;

  @Column({
    defaultValue: false,
    type: DataType.BOOLEAN,
  })
  isRead: boolean;

  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  receiverId: number;

  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  senderId: number;

  @BelongsTo(() => User, { as: 'receiver' })
  receiver: User;

  @BelongsTo(() => User, { as: 'sender' })
  sender: User;

  @Column({
    defaultValue: false,
    type: DataType.BOOLEAN,
  })
  isSystemGenerated: boolean;

  @Column({
    allowNull: true,
    type: DataType.TEXT,
  })
  message: string;

  @Column({
    allowNull: true,
    type: DataType.JSONB,
  })
  additional: any;

  @Column({
    allowNull: true,
    // defaultValue: 'unknown',
    type: DataType.STRING,
  })
  type: string;

  @Column({
    allowNull: true,
    type: DataType.STRING,
  })
  appName: string;

  @Column({
    allowNull: true,
    type: DataType.STRING,
  })
  prefrence: string;

  // @Column({
  //   allowNull: true,
  //   type: DataType.BOOLEAN,
  // })
  // isAdmin: boolean;
}
