import {
  Controller,
  Post,
  Body,
  Get,
  Param,
  Query,
  UseGuards,
  Request,
  ParseIntPipe,
  Patch,
} from '@nestjs/common';
import { JobsService } from './job.service';
import { Job } from './schemas/job.schema';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiQuery,
  ApiParam,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { InjectModel } from '@nestjs/sequelize';
import { CreateJobDto, UpdateJobStatus } from './dto/job.dto';
import { JWTGuard } from 'src/guards/common/jwt.guard';
import { Op, Sequelize } from 'sequelize';
import { User } from '../admin/authentication/schemas/user.schema';
import { Role } from '../roles/schemas/role.schema';
import { BiddingDetail } from './schemas/bidding-details.schema';
import { JobStatus } from 'src/common/utils/enum';
import { Category } from '../ category/schemas/category.schema';
import { GlobalSearchDto } from '../user-management/dto/registerUser.input.dto';
import { ReviewRating } from '../review-rating/schemas/review-rating.schema';
import { Notification } from '../notification/schema/notification.schema';

@ApiTags('Jobs')
@Controller('jobs')
export class JobsController {
  constructor(
    private jobsService: JobsService,
    @InjectModel(Notification)
    private notificationModel: typeof Notification,
  ) {}

  @Post()
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new job' })
  @ApiResponse({
    status: 201,
    description: 'The job has been successfully created.',
    type: Job,
  })
  @ApiResponse({ status: 400, description: 'Invalid input data.' })
  async createJob(
    @Body() createJobDto: CreateJobDto,
    @Request() req,
  ): Promise<Job> {
    const { geolocation, ...rest } = createJobDto;
    const body = {
      ...rest,
      userId: req.user.id,
      geolocation: geolocation
        ? {
            type: 'Point',
            coordinates: geolocation,
          }
        : null,
    };

    return this.jobsService.create(req.user.roles[0].roleName, body);
  }

  @ApiOperation({ summary: 'Get jobs created by a specific user' })
  @ApiResponse({
    status: 200,
    description: 'Return jobs created by the specified user.',
  })
  @Get('by-user')
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'limit', required: false, type: String })
  @ApiQuery({ name: 'page', required: false, type: String })
  @ApiQuery({ name: 'lookingForId', required: false, type: String })
  @ApiQuery({ name: 'inviteeId', required: false, type: Number })
  async getJobsByUserId(
    @Request() req,
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Query('search') search = null,
    @Query('lookingForId') lookingForId = null,
    @Query('inviteeId') inviteeId = null,
  ): Promise<{ jobs: Job[]; total: number }> {
    const whereClause: any = {
      userId: req.user.id,
      ...(search && {
        title: { [Op.iLike]: `%${search}%` },
      }),
      status: {
        ...(inviteeId
          ? { [Op.eq]: JobStatus.PUBLISHED }
          : { [Op.notIn]: [JobStatus.DELETED] }),
      },
      ...(inviteeId && {
        id: {
          [Op.notIn]: Sequelize.literal(`
            (SELECT "jobId" FROM "UserJobs" WHERE "userId" = ${inviteeId})
          `),
        },
      }),
    };

    const query: any = {
      where: whereClause,
      attributes: [
        'id',
        'title',
        'description',
        'radius',
        'images',
        'status',
        'geolocation',
        'state',
        'city',
        'pincode',
        'createdAt',
        'updatedAt',
        'roleId',
      ],
      order: [['createdAt', 'DESC']],
      offset: (page - 1) * limit,
      limit,
      distinct: true,
    };
    const includeArray: any = [
      {
        model: Category,
        as: 'criteria',
        attributes: ['id', 'name', 'parentId'],
      },
      {
        model: Category,
        as: 'subCategory',
        attributes: ['id', 'name', 'parentId'],
      },
      {
        model: BiddingDetail,
        as: 'biddingDetail',
        attributes: ['maxRange', 'minRange', 'startDate', 'endDate'],
      },
      {
        model: ReviewRating,
        as: 'reviewRating',
        attributes: ['review', 'rating'],
      },
    ];

    if (lookingForId) {
      includeArray.push({
        model: Role,
        as: 'lookingFor',
        where: { id: lookingForId },
        attributes: ['id', 'roleName'],
      });
    } else
      includeArray.push({
        model: Role,
        as: 'lookingFor',
        attributes: ['id', 'roleName'],
      });
    query.include = includeArray;
    return this.jobsService.getAllWithCount(query);
  }

  @Get()
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get a list of all jobs' })
  // @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'limit', required: false, type: String })
  @ApiQuery({ name: 'page', required: false, type: String })
  @ApiResponse({ status: 200, description: 'Return a list of all jobs.' })
  async getAllJobs(
    @Request() req,
    @Query() filterData: GlobalSearchDto,
    @Query('page') page = 1,
    @Query('limit') limit = 10,
  ): Promise<{ jobs: Job[]; total: number }> {
    const { geolocation, search, radius } = filterData;
    const parsedGeolocation = geolocation?.map(Number);
    const filters = {
      geolocation: parsedGeolocation,
      search,
      radius,
      page,
      limit,
    };

    return this.jobsService.getJobsWithCount(req.user, filters);
  }

  @Get(':id')
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get details of a specific job' })
  @ApiParam({
    name: 'id',
    required: true,
    type: Number,
    description: 'ID of the job to fetch',
  })
  @ApiResponse({
    status: 200,
    description: 'Return details of the specified job.',
  })
  @ApiResponse({ status: 404, description: 'Job not found.' })
  async getJobById(@Param('id') id: number, @Request() req): Promise<Job> {
    const query = {
      where: {
        id,
      },
      include: [
        {
          model: Category,
          as: 'criteria',
          attributes: ['id', 'name', 'parentId'],
        },
        {
          model: Category,
          as: 'subCategory',
          attributes: ['id', 'name', 'parentId'],
        },
        {
          model: ReviewRating,
          as: 'reviewRating',
          attributes: ['review', 'rating'],
        },
        {
          model: User,
          as: 'createdBy',
          attributes: [
            'id',
            'firstName',
            'lastName',
            'email',
            'phoneNumber',
            'profilePicture',
            'bio',
            'address1',
            'address2',
            'city',
            'state',
            'pincode',
            'firstName',
            'officeNumber',
            'website',
            'secondaryPhoneNumber',
            'workingDays',
            'openingTime',
            'closingTime',
            [
              Sequelize.literal(`(
                SELECT CAST(ROUND(AVG(rating) * 2) / 2 AS DECIMAL(2,1))
                FROM "ReviewRatings" AS rr
                WHERE rr."revieweeId" = "createdBy"."id"
                )`),
              'averageRating',
            ],
          ],
          include: [
            {
              model: Role,
              attributes: ['id', 'roleName'],
              through: {
                attributes: [],
              },
            },
            {
              model: Category,
              through: {
                attributes: [],
              },
              as: 'categories',
              attributes: ['id', 'name'],
            },
            {
              model: Category,
              through: {
                attributes: [],
              },
              as: 'subCategories',
              attributes: ['id', 'name', 'parentId'],
            },
          ],
        },
        {
          model: Role,
          as: 'lookingFor',
          attributes: ['roleName'],
        },
        {
          model: BiddingDetail,
          attributes: [
            'id',
            'minRange',
            'maxRange',
            [
              Sequelize.literal(`
                FLOOR(EXTRACT(EPOCH FROM ("endDate" - NOW())) / 86400)::INTEGER
              `),
              'daysRemaining',
            ],
            [
              Sequelize.literal(`
                FLOOR((EXTRACT(EPOCH FROM ("endDate" - NOW())) / 3600) % 24)::INTEGER
              `),
              'hoursRemaining',
            ],
          ],
          as: 'biddingDetail',
        },
      ],
      attributes: [
        'id',
        'title',
        'description',
        'radius',
        'images',
        'status',
        'city',
        'state',
        'pincode',
        'geolocation',
        'createdAt',
        'updatedAt',
        [
          Sequelize.literal(`
            (
              SELECT status
              FROM "UserJobs"
              WHERE "UserJobs"."jobId" = ${id}
              AND "UserJobs"."userId" = ${req.user.id}
            )
          `),
          'userJobStatus',
        ],
        [
          Sequelize.literal(`
            (
              SELECT id
              FROM "UserJobs"
              WHERE "UserJobs"."jobId" = ${id}
              AND "UserJobs"."userId" = ${req.user.id}
            )
          `),
          'userJobId',
        ],
        [
          Sequelize.literal(`
            (
              SELECT status
              FROM "UserJobBids"
              WHERE "UserJobBids"."jobId" = ${id}
              AND "UserJobBids"."userId" = ${req.user.id}
            )
          `),
          'userJobBidStatus',
        ],
      ],
    };

    const job = await this.jobsService.getOne(query);
    const hasNotification = await this.notificationModel.findOne({
      where: {
        receiverId: job.createdBy.id,
        type: 'review_requested',
        additional: {
          jobId: job.id,
        },
      },
    });
    let notificationExists = false;
    if (hasNotification) {
      notificationExists = true;
    }
    const result = job.toJSON() as any;
    result.hasNotification = notificationExists;
    return result;
  }

  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @Patch(':jobId/status')
  @ApiOperation({ summary: 'Update job status' })
  @ApiParam({
    name: 'jobId',
    type: 'number',
    description: 'ID of the job to update',
  })
  async updateJobStatus(
    @Param('jobId', ParseIntPipe) jobId: number,
    @Body() updateJobStatus: UpdateJobStatus,
  ) {
    return this.jobsService.updateJobStatus(jobId, updateJobStatus.status);
  }

  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @Patch(':jobId')
  @ApiOperation({ summary: 'Job - Update an unpublished job by ID' })
  @ApiParam({ name: 'jobId', required: true, type: Number })
  @ApiResponse({
    status: 200,
    description: 'The subscription package has been successfully updated.',
  })
  updateJob(
    @Param('jobId', ParseIntPipe) jobId: number,
    @Body() updateJobData: CreateJobDto,
  ) {
    try {
      const { geolocation, ...rest } = updateJobData;
      const body = {
        ...rest,
        geolocation: geolocation
          ? {
              type: 'Point',
              coordinates: geolocation,
            }
          : null,
      };
      console.log(body);
      return this.jobsService.updateJobById(jobId, body);
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Internal server error',
      };
    }
  }
}
