import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsInt,
  IsString,
  ValidateIf,
  IsOptional,
} from 'class-validator';
import { DiscussionType } from '../utils/enum';

export class CreateDiscussionDto {
  @ApiProperty({
    description: 'Type of discussion',
    enum: DiscussionType,
    required: true,
  })
  @IsEnum(DiscussionType)
  type: DiscussionType;

  @ApiProperty({
    type: Number,
    description: 'enter user job bid id',
  })
  @IsOptional()
  @IsInt()
  @ValidateIf((dto: CreateDiscussionDto) => dto.type === DiscussionType.JOB_BID)
  userJobBidId: number;

  @ApiProperty({
    type: Number,
    description: 'enter Quotation id',
  })
  @IsInt()
  @ValidateIf(
    (dto: CreateDiscussionDto) => dto.type === DiscussionType.QUOTATION,
  )
  @IsOptional()
  requestQuotationId: number;

  @ApiProperty({ description: 'Enter message here' })
  @IsString()
  message: string;

  @IsString()
  @IsOptional()
  title: string;

  @IsInt()
  @IsOptional()
  jobId: number;
}
