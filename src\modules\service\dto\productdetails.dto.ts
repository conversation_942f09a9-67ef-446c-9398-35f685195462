import { IsNotEmpty, <PERSON>N<PERSON>ber, IsString } from 'class-validator';

export class ProductDetailsDto {
  @IsString()
  @IsNotEmpty()
  title: string;

  @IsString()
  @IsNotEmpty()
  description: string;

  @IsNumber()
  @IsNotEmpty()
  productId: number;

  @IsNumber()
  @IsNotEmpty()
  receivedQuotationId: number;

  @IsNumber()
  @IsNotEmpty()
  unitCost: number;

  @IsNumber()
  @IsNotEmpty()
  quantity: number;

  @IsNumber()
  @IsNotEmpty()
  amount: number;
}
