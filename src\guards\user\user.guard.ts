import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { CommonService } from 'src/common/common.service';
import { extractTokenFromHeader } from 'src/common/utils/helper';
import { User } from 'src/modules/admin/authentication/schemas/user.schema';
import { Role } from 'src/modules/auth/dto/user.dto';

@Injectable()
export class UserGuard implements CanActivate {
  constructor(
    private readonly commonService: CommonService,
    @InjectModel(User)
    private userRepository: typeof User,
  ) {}

  async canActivate(context: ExecutionContext) {
    try {
      const request = context.switchToHttp().getRequest();
      const token = extractTokenFromHeader(request);
      const verify = await this.commonService.verifyToken(token);
      if (verify) {
        const result: User = await this.userRepository.findOne({
          where: {
            id: verify._id,
            // role: Role.BUYER,
            isActive: true,
          },
        });
        if (!result) {
          throw new UnauthorizedException();
        }
        request.user = result;
        return true;
      } else {
        throw new UnauthorizedException();
      }
    } catch (error) {
      console.error('Error in user Guard', error);
      throw new UnauthorizedException(error);
    }
  }
}
