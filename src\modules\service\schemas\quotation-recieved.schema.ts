import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
  HasMany,
} from 'sequelize-typescript';
import { RequestQuotation } from './quotation-request.schema';
import { ProductDetails } from './quotaion-product.schema';

export enum ServiceTagStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

@Table
export class RecievedQuotation extends Model<RecievedQuotation> {
  @Column({
    type: DataType.JSONB,
    allowNull: false,
  })
  companyDetails: object;

  @Column({
    type: DataType.JSONB,
    allowNull: false,
  })
  billingDetails: object;

  @HasMany(() => ProductDetails)
  productDetails: ProductDetails[];

  @Column({ defaultValue: false })
  isPaid: boolean;

  @Column({
    defaultValue: false,
    type: DataType.BOOLEAN,
  })
  isRead: boolean;

  @ForeignKey(() => RequestQuotation)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  requestQuotationId: number;

  @BelongsTo(() => RequestQuotation, 'requestQuotationId')
  requestQuotation: RequestQuotation;
}
