'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    try {
      // Check if column exists before adding it
      const tableInfo = await queryInterface.describeTable('UserJobBids');
      if (!tableInfo.jobOwnerId) {
        await queryInterface.addColumn('UserJobBids', 'jobOwnerId', {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: 'Users',
            key: 'id',
          },
          onUpdate: 'CASCADE',
          onDelete: 'SET NULL',
        });
        console.log('Column jobOwnerId added to UserJobBids table');
      } else {
        console.log('Column jobOwnerId already exists in UserJobBids table, skipping...');
      }
    } catch (error) {
      console.error('Error in migration:', error.message);
    }
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    try {
      // Check if column exists before removing it
      const tableInfo = await queryInterface.describeTable('UserJobBids');
      if (tableInfo.jobOwnerId) {
        await queryInterface.removeColumn('UserJobBids', 'jobOwnerId');
        console.log('Column jobOwnerId removed from UserJobBids table');
      } else {
        console.log('Column jobOwnerId does not exist in UserJobBids table, skipping...');
      }
    } catch (error) {
      console.error('Error in migration rollback:', error.message);
    }
  }
};