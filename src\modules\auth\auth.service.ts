import { Injectable, UnauthorizedException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { CommonService } from 'src/common/common.service';
import { User } from '../admin/authentication/schemas/user.schema';
import axios from 'axios';
import { ERROR_MESSAGES } from 'src/common/messages';

@Injectable()
export class AuthService {
  constructor(
    @InjectModel(User)
    private userRepository: typeof User,
    private readonly authService: CommonService,
  ) {}

  createUser = async (data: any) => {
    const { email, role, firstName, lastName, picture } = data;
    const payload = {
      firstName: firstName,
      lastName: lastName,
      profilePicture: picture,
      email: email,
      role: role,
    };
    return await this.userRepository.create(payload);
  };

  loginVendorUser = async (email: string) => {
    try {
      const existingUser = await this.userRepository.findOne({
        where: {
          email: email,
        },
      });
      const token = await this.authService.generateToken(existingUser.id);
      return { token: token };
    } catch (err) {
      throw err;
    }
  };

  verifyUser = async (email: string) => {
    try {
      const existingUser = await this.userRepository.findOne({
        where: {
          email: email,
        },
      });
      return existingUser ? true : false;
    } catch (err) {
      throw err;
    }
  };

  validateFacebookUser = async (
    facebookId: string,
    data: any,
  ): Promise<any> => {
    try {
      let user = await this.userRepository.findOne({
        where: { facebookId, role: data.role },
      });
      if (user && !user.isActive)
        throw new UnauthorizedException(ERROR_MESSAGES.ACCOUNT_DEACTIVATED);
      if (!user) {
        user = await this.userRepository.create({ facebookId, ...data });
      }
      return user;
    } catch (err) {
      throw err;
    }
  };

  validateFacebookToken = async (accessToken: string) => {
    try {
      const { data } = await axios.get(
        `https://graph.facebook.com/me?access_token=${accessToken}&fields=id,name`,
      );
      return data;
    } catch (error) {
      throw new UnauthorizedException('Invalid Facebook token');
    }
  };

  validateGoogleToken = async (accessToken: string) => {
    try {
      const { data } = await axios.get(
        `https://oauth2.googleapis.com/tokeninfo?id_token=${accessToken}`,
      );
      return data;
    } catch (error) {
      console.error(error);
      throw new UnauthorizedException('Invalid Google token');
    }
  };

  validateGoogleUser = async (googleId: string, data: any): Promise<any> => {
    try {
      let user = await this.userRepository.findOne({
        where: { googleid: googleId, role: data.role },
      });
      if (user && !user.isActive)
        throw new UnauthorizedException(ERROR_MESSAGES.ACCOUNT_DEACTIVATED);
      if (!user) {
        user = await this.userRepository.create({
          googleid: googleId,
          ...data,
        });
      }
      user.lastLogin = new Date();
      user.save();
      return user;
    } catch (err) {
      throw err;
    }
  };

  validateLinkedInToken = async (accessToken: string) => {
    try {
      const { data } = await axios.get('https://api.linkedin.com/v2/me', {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });
      return data;
    } catch (error) {
      throw new UnauthorizedException('Invalid LinkedIn token');
    }
  };

  validateTwitterInToken = async (accessToken: string) => {
    const url = 'https://api.twitter.com/2/users/me';
    try {
      const { data } = await axios.get(url, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });
      return data?.data;
    } catch (error) {
      throw new UnauthorizedException('Invalid Twitter token');
    }
  };

  validateTwitterUser = async (twitterId: string, data: any): Promise<any> => {
    try {
      let user = await this.userRepository.findOne({
        where: { twitterId, role: data.role },
      });
      if (user && !user.isActive)
        throw new UnauthorizedException(ERROR_MESSAGES.ACCOUNT_DEACTIVATED);
      if (!user) {
        user = await this.userRepository.create({ twitterId, ...data });
      }
      return user;
    } catch (err) {
      throw err;
    }
  };

  validateLinkedInUser = async (
    linkedInId: string,
    data: any,
  ): Promise<any> => {
    try {
      let user = await this.userRepository.findOne({
        where: { linkedInId, role: data.role },
      });
      if (user && !user.isActive)
        throw new UnauthorizedException(ERROR_MESSAGES.ACCOUNT_DEACTIVATED);
      if (!user) {
        user = await this.userRepository.create({ linkedInId, ...data });
      }
      return user;
    } catch (err) {
      throw err;
    }
  };
}
