import {
  BadRequestException,
  ConflictException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Job } from './schemas/job.schema';
import { BiddingDetail } from './schemas/bidding-details.schema';
import { Role } from '../roles/schemas/role.schema';
import { I18nContext, I18nService } from 'nestjs-i18n';
import { JobStatus, SubscriptionStatus } from 'src/common/utils/enum';
import { Sequelize } from 'sequelize-typescript';
import { Category } from '../ category/schemas/category.schema';
import { User } from '../admin/authentication/schemas/user.schema';
import { Op } from 'sequelize';
import { Cron, CronExpression } from '@nestjs/schedule';
import { SendgridService } from '../sendgrid/sendgrid.service';
import { Constants } from 'src/common/constants';
import { Email } from 'src/common/email-template';
import { CustomException } from 'src/common/custom.exception';
import { ERROR_MESSAGES, notificationType } from 'src/common/messages';
import { UserSubscription } from '../user-subscription/schemas/user-subscription.schema';
import { Package } from '../subscription/schemas/package.schema';
import { PackageDetails } from '../subscription/schemas/packagePermission.schema';
import { NotificationService } from '../notification/notification.service';
import { ReviewRating } from '../review-rating/schemas/review-rating.schema';
import { NotificationPreference } from '../user-management/schemas/notification-preferences.schema';

@Injectable()
export class JobsService {
  constructor(
    @InjectModel(Job)
    private jobModel: typeof Job,
    @InjectModel(Role)
    private roleModel: typeof Role,

    @InjectModel(BiddingDetail) private jobBiddingModel: typeof BiddingDetail,
    private readonly i18n: I18nService,
    @InjectModel(User) private userRepository: typeof User,
    private readonly sendgridService: SendgridService,
    private readonly notificationService: NotificationService,
  ) {}

  // check after creating a account did the user created any job after 30 days

  @Cron(CronExpression.EVERY_1ST_DAY_OF_MONTH_AT_MIDNIGHT) // Runs daily at midnight
  async handleEmailTocheckUserCreatedJobAfter30Days() {
    const users = await this.userRepository.findAll({
      where: {
        isActive: true,
        createdAt: {
          [Op.gte]: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
        },
      },
    });
    const userIdsWithJobs = await this.jobModel.findAll({
      attributes: ['userId'],
      group: ['userId'],
      raw: true,
    });

    const userIdsWithJobsSet = new Set(
      userIdsWithJobs.map((job) => job.userId),
    );
    const usersWithNoJobs = users.filter(
      (user) => !userIdsWithJobsSet.has(user.id),
    );
    console.log('users', usersWithNoJobs.length);

    if (usersWithNoJobs.length > 0) {
      for (const user of usersWithNoJobs) {
        const fourteenDaysAgo = new Date();
        fourteenDaysAgo.setDate(fourteenDaysAgo.getDate() - 30);

        const pref = user.notificationPreference;
        if (
          pref?.email === true &&
          user.email &&
          (!user.lastLogin || new Date(user.lastLogin) < fourteenDaysAgo)
        ) {
          await this.sendgridService.sendEmail(
            user.email,
            Constants.Titles.PostNewJob,
            Email.getTemplate({
              title: 'Time to Post!',
              userName: `${user?.firstName} ${user?.lastName}`,
              children: `
            <p>
             It’s been a while since you posted a job. Post one now and get the best quotes.
          </p>
            `,
            }),
          );
        }
      }
    }
  }
  

  async create(role: string, createJobDto: any): Promise<Job> {
    // Check for duplicate job
    await this.checkForDuplicateJob(createJobDto);

    // Validate role permissions
    const roleName = await this.validateRoleForJob(createJobDto.roleId, role);

    // Create job and bidding details
    const job = await this.createJobWithBiddingDetails(createJobDto);

    // Notify relevant users
    this.notifyNearbyUsersOnJobCreation(job, roleName);

    return job;
  }

  private async checkForDuplicateJob(createJobDto: any): Promise<void> {
    const jobExist = await this.jobModel.findOne({
      where: {
        status: { [Op.notIn]: [JobStatus.DELETED] },
        title: createJobDto.title,
        userId: createJobDto.userId,
      },
    });

    if (jobExist) {
      const message = this.i18n.t('test.errorMessage.ALREADY_EXIST', {
        lang: I18nContext.current().lang,
        args: { data: 'Job title' },
      });
      throw new ConflictException(message);
    }
  }

  private async validateRoleForJob(
    roleId: number,
    userRole: string,
  ): Promise<string> {
    const userRoleObj = await this.roleModel.findOne({
      where: { id: roleId },
    });

    if (!userRoleObj) {
      const message = this.i18n.t('test.errorMessage.INVALID_DATA', {
        lang: I18nContext.current().lang,
        args: { data: 'lookingFor' },
      });
      throw new BadRequestException(message);
    }

    const roleName = userRoleObj.roleName;
    console.log('roleName::::', roleName);

    this.validateRolePermissions(userRole, roleName);

    return roleName;
  }

  private validateRolePermissions(userRole: string, targetRole: string): void {
    switch (userRole) {
      case Constants.Roles.BUYER:
        const validBuyerTargets = [
          Constants.Roles.LABOUR,
          Constants.Roles.BUYER,
          Constants.Roles.ARCHITECT,
          Constants.Roles.CONTRACTOR,
        ];
        if (!validBuyerTargets.includes(targetRole)) {
          this.throwUnauthorizedRoleError(
            'to create jobs only for labour, buyer, architect or contractor',
          );
        }
        break;

      case Constants.Roles.CONTRACTOR:
        const validContractorTargets = [
          Constants.Roles.CONTRACTOR,
          Constants.Roles.SELLER,
          Constants.Roles.LABOUR,
        ];
        if (!validContractorTargets.includes(targetRole)) {
          this.throwUnauthorizedRoleError(
            'to create jobs only for contractors, seller or labour',
          );
        }
        break;

      case Constants.Roles.ARCHITECT:
        const validArchitectTargets = [
          Constants.Roles.ARCHITECT,
          Constants.Roles.CONTRACTOR,
          Constants.Roles.LABOUR,
        ];
        if (!validArchitectTargets.includes(targetRole)) {
          this.throwUnauthorizedRoleError(
            'to create jobs only for contractors, architect or labour',
          );
        }
        break;

      default:
        throw new BadRequestException('Invalid user role');
    }
  }

  private throwUnauthorizedRoleError(details: string): void {
    const message = this.i18n.t('test.errorMessage.UNAUTHORIZED_ACTION', {
      lang: I18nContext.current().lang,
      args: { data: details },
    });
    throw new BadRequestException(message);
  }

  private async createJobWithBiddingDetails(createJobDto: any): Promise<Job> {
    const { biddingDetail, ...jobDetails } = createJobDto;
    const job = await this.jobModel.create(jobDetails);

    // If bidding details are not provided or incomplete, calculate default values
    let biddingPayload: any = { jobId: job.id };

    if (biddingDetail) {
      // Use provided bidding details
      biddingPayload = { ...biddingDetail, jobId: job.id };
    } else {
      // Calculate default bidding dates
      const currentDate = new Date();
      const endDate = new Date(currentDate);
      endDate.setDate(currentDate.getDate() + 13); // Add 13 days to current date (total 14 days including today)

      biddingPayload = {
        jobId: job.id,
        startDate: currentDate,
        endDate: endDate,
        // Default min/max range can be set here if needed
        minRange: 0,
        maxRange: 0,
      };
    }

    await this.jobBiddingModel.create(biddingPayload);

    return job;
  }

  async getAllWithCount(query: any): Promise<{ jobs: Job[]; total: number }> {
    const { count, rows } = await this.jobModel.findAndCountAll(query);

    return { jobs: rows, total: count };
  }

  async getJobsWithCount(
    user: any,
    filters: any,
  ): Promise<{ jobs: Job[]; total: number }> {
    // Step 1: Fetch and validate user data
    const userData = await this.fetchUserWithSubscriptionData(user.id);

    // Step 2: Calculate geolocation parameters
    const geoParams = this.calculateGeolocationParams(
      user,
      filters,
      userData.userRadius,
    );

    // Step 3: Build where conditions
    const whereCondition = this.buildJobWhereConditions(
      user,
      filters,
      userData,
      geoParams,
    );

    // Step 4: Build query includes
    const includeConditions = this.buildJobQueryIncludes(
      user.roles[0].roleName,
    );

    // Step 5: Build complete query
    const query = this.buildCompleteJobQuery(
      whereCondition,
      includeConditions,
      filters,
      geoParams,
    );

    // Step 6: Execute query and process results
    const { count, rows } = await this.jobModel.findAndCountAll(query);

    // Step 7: Apply additional filtering and return results
    return this.processJobResults(rows, count, userData.userRadius);
  }

  private async fetchUserWithSubscriptionData(userId: number) {
    const userExist = await this.userRepository.findOne({
      where: { id: userId },
      include: [
        {
          model: UserSubscription,
          as: 'subscription',
          where: {
            status: { [Op.in]: [SubscriptionStatus.ACTIVE] },
          },
          required: true,
          include: [
            {
              model: Package,
              as: 'package',
              include: [
                {
                  model: PackageDetails,
                  as: 'packageDetails',
                },
              ],
            },
          ],
        },
        {
          model: Category,
          through: { attributes: [] },
          as: 'categories',
          attributes: ['id', 'name'],
        },
        {
          model: Category,
          through: { attributes: [] },
          as: 'subCategories',
          attributes: ['id', 'name', 'parentId'],
        },
      ],
    });

    if (!userExist) {
      throw new CustomException(
        'User not found or no active subscription',
        HttpStatus.BAD_REQUEST,
      );
    }

    const categoryIds = userExist.categories.map((cat) => cat.id);
    const subCategoryIds = userExist.subCategories.map((sub) => sub.id);
    const userRadius =
      userExist.subscription.package.packageDetails.dataValues.radiusLimit;

    return {
      userExist,
      categoryIds,
      subCategoryIds,
      userRadius,
    };
  }

  private calculateGeolocationParams(
    user: any,
    filters: any,
    userRadius: number,
  ) {
    const userGeolocation = user.geolocation;
    let latitude: number, longitude: number, searchRadius: number;

    // Priority order for geolocation and radius
    if (filters.geolocation && filters.radius) {
      // Use filter location and filter radius
      latitude = filters.geolocation[1];
      longitude = filters.geolocation[0];
      searchRadius = Math.min(filters.radius, userRadius); // Enforce user's subscription limit
    } else if (filters.geolocation) {
      // Use filter location with user's subscription radius
      latitude = filters.geolocation[1];
      longitude = filters.geolocation[0];
      searchRadius = userRadius;
    } else if (filters.radius && userGeolocation) {
      // Use user location with filter radius (limited by subscription)
      latitude = userGeolocation.coordinates[1];
      longitude = userGeolocation.coordinates[0];
      searchRadius = Math.min(filters.radius, userRadius);
    } else if (userGeolocation) {
      // Use user location with subscription radius
      latitude = userGeolocation.coordinates[1];
      longitude = userGeolocation.coordinates[0];
      searchRadius = userRadius;
    } else {
      throw new CustomException(
        ERROR_MESSAGES.UPDATE_LOCATION,
        HttpStatus.BAD_REQUEST,
      );
    }

    return {
      latitude,
      longitude,
      searchRadius,
      searchRadiusMeters: searchRadius * 1000,
    };
  }

  private buildJobWhereConditions(
    user: any,
    filters: any,
    userData: any,
    geoParams: any,
  ) {
    const { latitude, longitude, searchRadiusMeters } = geoParams;
    const { categoryIds, subCategoryIds } = userData;
    const roleName = user.roles[0].roleName;

    // Build spatial condition - simplified and more reliable
    const spatialCondition = Sequelize.where(
      Sequelize.literal(`
        ST_DWithin(
          "Job"."geolocation"::geography,
          ST_SetSRID(ST_MakePoint(${longitude}, ${latitude}), 4326)::geography,
          ${searchRadiusMeters}
        )
      `),
      true,
    );

    const whereCondition: any = {
      userId: { [Op.ne]: user.id },
      status: JobStatus.PUBLISHED,
      [Op.and]: [
        spatialCondition,
        Sequelize.literal(`
          NOT EXISTS (
            SELECT 1 FROM "UserJobs"
            WHERE "UserJobs"."jobId" = "Job"."id"
            AND "UserJobs"."userId" = ${user.id}
            AND "UserJobs"."status" = 'rejected'
          )
        `),
      ],
    };

    // Add search conditions
    if (filters?.search) {
      whereCondition[Op.or] = [
        { title: { [Op.iLike]: `%${filters.search}%` } },
        Sequelize.literal(`
          EXISTS (
            SELECT 1 FROM "Categories" AS c
            WHERE c."id" = "Job"."roleSubcategoryId"
            AND c."name" ILIKE '%${filters.search}%'
          )
        `),
        Sequelize.literal(`
          EXISTS (
            SELECT 1 FROM "Categories" AS sc
            WHERE sc."id" = "Job"."subCategoryId"
            AND sc."name" ILIKE '%${filters.search}%'
          )
        `),
      ];
    }

    // Add role-based category filtering
    if (['buyer', 'contractor', 'architect'].includes(roleName)) {
      whereCondition.roleSubcategoryId = { [Op.in]: categoryIds };
    } else if (['labour', 'seller'].includes(roleName)) {
      whereCondition.subCategoryId = { [Op.in]: subCategoryIds };
    }

    return whereCondition;
  }

  private buildJobQueryIncludes(roleName: string) {
    return [
      {
        model: Category,
        as: 'criteria',
        attributes: ['id', 'name'],
      },
      {
        model: Category,
        as: 'subCategory',
        attributes: ['id', 'name', 'parentId'],
      },
      {
        model: BiddingDetail,
        as: 'biddingDetail',
        required: true, // Include jobs without bidding details
        attributes: ['maxRange', 'minRange', 'startDate', 'endDate'],
        where: {
          // Fixed: Use NOW() instead of CURRENT_DATE for proper timestamp comparison
          startDate: { [Op.lte]: Sequelize.literal('NOW()') },
          endDate: { [Op.gte]: Sequelize.literal('NOW()') },
        },
      },
      {
        model: ReviewRating,
        as: 'reviewRating',
        attributes: ['review', 'rating'],
      },
      {
        model: User,
        as: 'createdBy',
        where: {
          isActive: true,
          isDeleted: false,
        },
        attributes: [
          'id',
          'firstName',
          'lastName',
          'email',
          'role',
          'phoneNumber',
          'profilePicture',
          [
            Sequelize.literal(`(
              SELECT CAST(ROUND(AVG(rating) * 2) / 2 AS DECIMAL(2,1))
              FROM "ReviewRatings" AS rr
              WHERE rr."revieweeId" = "createdBy"."id"
            )`),
            'averageRating',
          ],
        ],
        include: [
          {
            model: Role,
            as: 'roles',
            attributes: ['roleName'],
            through: { attributes: [] },
          },
        ],
      },
      {
        model: Role,
        as: 'lookingFor',
        where: {
          roleName,
        },
        attributes: ['roleName'],
      },
    ];
  }

  private buildCompleteJobQuery(
    whereCondition: any,
    includeConditions: any[],
    filters: any,
    geoParams: any,
  ) {
    const { latitude, longitude } = geoParams;
    const { page, limit } = filters;

    return {
      where: whereCondition,
      include: includeConditions,
      attributes: [
        'id',
        'title',
        'description',
        'radius',
        'images',
        'status',
        'geolocation',
        'state',
        'city',
        'createdAt',
        'updatedAt',
        'pincode',
        'roleId',
        [
          // Calculate distance in kilometers between user and job
          Sequelize.literal(`
            ROUND(
              (ST_Distance(
                "Job"."geolocation"::geography,
                ST_SetSRID(ST_MakePoint(${longitude}, ${latitude}), 4326)::geography
              ) / 1000)::numeric, 2
            )
          `),
          'distanceInKm',
        ],
      ],
      order: [['createdAt', 'DESC']] as any,
      offset: (page - 1) * limit,
      limit: parseInt(limit) || 10,
      logging: true,
    } as any;
  }

  private processJobResults(rows: any[], count: number, userRadius: number) {
    // Apply additional filtering for jobs that might have slipped through
    // This is a safety check in case the spatial query isn't working as expected
    const filteredJobs = rows.filter((job) => {
      const jobData = job.toJSON() as any;
      const isWithinUserRadius = jobData.distanceInKm <= userRadius;
      const isWithinJobRadius = jobData.distanceInKm <= job.radius;

      return isWithinUserRadius && isWithinJobRadius;
    });

    // Return the correct total count from database, not filtered count
    // The database query should handle most filtering, this is just a safety net
    return { jobs: filteredJobs, total: count };
  }

  async getById(jobId: number): Promise<Job> {
    const job = await this.jobModel.findByPk(jobId);
    if (!job) {
      throw new NotFoundException('Job not found');
    }
    return job;
  }
  async getOne(query: any): Promise<Job> {
    const job = await this.jobModel.findOne(query);
    if (!job) {
      throw new NotFoundException('Job not found');
    }
    return job;
  }

  async updateJobStatus(jobId: number, status: string) {
    try {
      const jobExist = await this.getOne({
        where: {
          id: jobId,
        },
      });
      if (!jobExist) {
        throw new NotFoundException(`job with ID ${jobId} not found`);
      }
      jobExist.status = status;
      await jobExist.save();

      return {
        success: true,
        message: `Job status updated to ${status}`,
      };
    } catch (error) {
      throw new InternalServerErrorException(
        `Failed to update Job status ${error.message}`,
      );
    }
  }

  async updateJobById(jobId: number, updateJobData: any) {
    try {
      const { biddingDetail, ...jobDetails } = updateJobData;
      const jobExist = await this.getOne({
        where: {
          id: jobId,
        },
      });
      if (!jobExist) {
        throw new NotFoundException(
          `unpublished job with ID ${jobId} not found`,
        );
      }
      const jobTitle = await this.jobModel.findOne({
        where: {
          title: jobDetails.title,
          id: {
            [Op.ne]: jobId,
          },
        },
      });
      if (jobTitle) {
        throw new NotFoundException(
          `Job with same title ${jobDetails.title} already exist`,
        );
      }
      await jobExist.update(jobDetails);

      // Commented out email notification code
      // const userDetails = await this.userRepository.findByPk(jobExist.userId);
      // if (userDetails?.email) {
      //   await this.sendgridService.sendEmail(
      //     userDetails.email,
      //     'New Bid on Your Job',
      //     Email.getTemplate({
      //       title: 'You Have a New Bid!',
      //       userName: `${userDetails.firstName} ${userDetails.lastName}`,
      //       children: `
      //   <p>
      //    A new bid has been placed on your job. Check it out now.
      // </p>
      //   `,
      //     }),
      //   );
      // }

      // Handle bidding details
      const biddingExist = await this.jobBiddingModel.findOne({
        where: {
          jobId: jobExist.id,
        },
      });

      if (biddingDetail) {
        // If bidding details are provided, update them
        if (biddingExist) {
          await biddingExist.update(biddingDetail);
        } else {
          const biddingPayload = { ...biddingDetail, jobId: jobExist.id };
          await this.jobBiddingModel.create(biddingPayload);
        }
      } else if (!biddingExist) {
        // If no bidding details exist and none provided, create default ones
        const currentDate = new Date();
        const endDate = new Date(currentDate);
        endDate.setDate(currentDate.getDate() + 13); // Add 13 days to current date (total 14 days including today)

        const defaultBiddingPayload = {
          jobId: jobExist.id,
          startDate: currentDate,
          endDate: endDate,
          minRange: 0,
          maxRange: 0,
        };

        await this.jobBiddingModel.create(defaultBiddingPayload);
      }

      return jobExist;
    } catch (error) {
      throw new InternalServerErrorException(`${error.message}`);
    }
  }

  async notifyNearbyUsersOnJobCreation(job: Job, userRole: string) {
    // Get job location coordinates
    const jobLongitude = job.geolocation.coordinates[0];
    const jobLatitude = job.geolocation.coordinates[1];
    const jobRadious = job.radius * 1000;

    // Find users with matching role
    const users = await this.userRepository.findAll({
      where: {
        id: { [Op.ne]: job.userId }, // Exclude job creator
        isActive: true,
        isDeleted: false,
      },
      attributes: [
        'id',
        'firstName',
        'lastName',
        'email',
        'phoneNumber',
        'lastLogin',
        'geolocation',
      ],
      include: [
        // Role matching
        {
          model: Role,
          as: 'roles',
          attributes: ['id', 'roleName'],
          where: { roleName: userRole },
          through: { attributes: [] },
        },
        // Category matching (if specified)
        {
          model: Category,
          through: { attributes: [] },
          as: 'categories',
          where: job.roleSubcategoryId ? { id: job.roleSubcategoryId } : {},
          required: false,
        },
        // Subcategory matching (if specified)
        {
          model: Category,
          through: { attributes: [] },
          as: 'subCategories',
          where: job.subCategoryId ? { id: job.subCategoryId } : {},
          required: false,
        },
        // Notification preferences
        {
          model: NotificationPreference,
          as: 'notificationPreference',
          required: false,
        },
        // Active subscription with package details
        {
          model: UserSubscription,
          as: 'subscription',
          where: { status: SubscriptionStatus.ACTIVE },
          required: true,
          include: [
            {
              model: Package,
              as: 'package',
              include: [
                {
                  model: PackageDetails,
                  as: 'packageDetails',
                },
              ],
            },
          ],
        },
      ],
    });

    console.log(`Found ${users.length} users with matching role criteria`);

    // Process each user to check if they're within radius limits
    for (const user of users) {
      try {
        // Skip users without geolocation
        if (!user.geolocation || !user.geolocation.coordinates) {
          continue;
        }

        // Note: We're using the job's radius for distance calculation, not the user's subscription radius
        // const userRadius = parseFloat(user.subscription.package.packageDetails.dataValues.radiusLimit) || 10;

        // Get user coordinates
        const userLongitude = user.geolocation.coordinates[0];
        const userLatitude = user.geolocation.coordinates[1];

        // Check if job is within user's subscription radius
        const isWithinRadius = Sequelize.literal(`
          ST_DWithin(
            ST_SetSRID(ST_MakePoint(${userLongitude}, ${userLatitude}), 4326)::geography,
            ST_SetSRID(ST_MakePoint(${jobLongitude}, ${jobLatitude}), 4326)::geography,
            ${jobRadious}
          )
        `);

        // Execute the spatial query to check if job is within user's radius
        const isEligible = await this.userRepository.findOne({
          where: {
            id: user.id,
            [Op.and]: [isWithinRadius],
          },
          attributes: ['id'],
        });

        // Skip if job is not within user's subscription radius
        if (!isEligible) {
          continue;
        }

        // User is eligible, send notifications
        const pref = user.notificationPreference;

        // Send in-app notification if enabled
        if (pref?.jobAlerts && pref?.inAppNotifications) {
          const data = {
            type: notificationType.JOB_ALERT,
            receiverId: user.id,
            prefrence: 'jobAlerts',
            additional: {
              jobId: job.id,
              categoryId: job.roleSubcategoryId,
              subCategoryId: job.subCategoryId,
            },
          };

          await this.notificationService.sendNotification(data);
        }

        // Send email notification if enabled and user hasn't logged in recently
        const fourteenDaysAgo = new Date();
        fourteenDaysAgo.setDate(fourteenDaysAgo.getDate() - 14);

        const isInactiveUser =
          !user.lastLogin || new Date(user.lastLogin) < fourteenDaysAgo;
        const hasEmailNotificationsEnabled = pref?.email === true && user.email;

        if (hasEmailNotificationsEnabled && isInactiveUser) {
          await this.sendgridService.sendEmail(
            user.email,
            Constants.Titles.NewJobAlert,
            Email.getTemplate({
              title: 'New Job Alert',
              userName: `${user.firstName} ${user.lastName}`,
              children: `
                <p>
                  A new job "${job.title}" has been posted near your location that matches your skills.
                  Log in to view details and submit your bid.
                </p>
              `,
            }),
          );
        }
      } catch (error) {
        console.error(
          `Error processing notification for user ${user.id}:`,
          error,
        );
        // Continue with next user
        throw error;
      }
    }
  }
}
