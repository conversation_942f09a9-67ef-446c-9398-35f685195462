import {
  Column,
  DataType,
  ForeignKey,
  Model,
  Table,
  BelongsTo,
} from 'sequelize-typescript';
import { AdsManager } from './ads-manager.schema';
import { User } from 'src/modules/admin/authentication/schemas/user.schema';

@Table
export class AdsDisplay extends Model {
  @ForeignKey(() => AdsManager)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  adsId: number;

  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  userId: number;

  @BelongsTo(() => AdsManager, { foreignKey: 'adsId', as: 'ads' })
  ads: AdsManager;
}
