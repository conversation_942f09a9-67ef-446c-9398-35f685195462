import {
  ConflictException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import {
  CreateServiceDto,
  keywordDto,
  UpdateServiceDto,
  UpdateServiceStatusDto,
} from './dto/service.dto';
import { Service, ServiceStatus } from './schemas/service.schema';
import { Tags } from './schemas/tags.schema';
import { RequestQuotation } from './schemas/quotation-request.schema';
import {
  ProductDetailsDto,
  RequestQuotationDto,
  SendQuotationDto,
  UpdateIsReadDto,
  UpdateQuotationStatusDto,
} from './dto/quotation.dto';
import { User } from '../admin/authentication/schemas/user.schema';
import { Op, Sequelize } from 'sequelize';
import { RecievedQuotation } from './schemas/quotation-recieved.schema';
// import { ProductDetailsDto } from './dto/productdetails.dto';
import { ProductDetails } from './schemas/quotaion-product.schema';
import { Category } from '../ category/schemas/category.schema';
import { Role } from '../roles/schemas/role.schema';
import { Discussion } from '../discussion/schemas/discussion.schema';
import {
  RequestedQuotationStatus,
  SubscriptionStatus,
} from 'src/common/utils/enum';
import { Connection } from '../connection/schemas/connection.schema';
import {
  ERROR_MESSAGES,
  notificationMessage,
  notificationTitle,
  notificationType,
} from 'src/common/messages';
import { NotificationService } from '../notification/notification.service';
import sequelize from 'sequelize';
import { CustomException } from 'src/common/custom.exception';
import { UserBlock } from '../user-management/schemas/user-blocks.schema';
import { UserSubscription } from '../user-subscription/schemas/user-subscription.schema';
import { Package } from '../subscription/schemas/package.schema';
import { PackageDetails } from '../subscription/schemas/packagePermission.schema';
import { SendgridService } from '../sendgrid/sendgrid.service';
import { Email } from 'src/common/email-template';
import { Constants } from 'src/common/constants';
import { filter } from 'rxjs';
import { NotificationPreference } from '../user-management/schemas/notification-preferences.schema';
import { Notification } from '../notification/schema/notification.schema';
import { ReviewRating } from '../review-rating/schemas/review-rating.schema';

@Injectable()
export class ServiceService {
  constructor(
    @InjectModel(Service) private serviceModel: typeof Service,
    @InjectModel(Tags) private tagsModel: typeof Tags,
    @InjectModel(User) private userModel: typeof User,
    @InjectModel(Connection) private ConnectionModel: typeof Connection,
    @InjectModel(RequestQuotation)
    private requestQuotationModel: typeof RequestQuotation,
    @InjectModel(RecievedQuotation)
    private recievedQuotationModel: typeof RecievedQuotation,
    @InjectModel(ProductDetails)
    private productDetailsModel: typeof ProductDetails,
    private readonly notificationService: NotificationService,
    @InjectModel(UserBlock)
    private userBlockModel: typeof UserBlock,
    @InjectModel(Notification)
    private notificationModel: typeof Notification,
    private readonly sendgridService: SendgridService,
  ) {}

  async createService(createServiceDto: CreateServiceDto, userId: number) {
    const serviceExist = await this.serviceModel.findOne({
      where: {
        title: createServiceDto.title,
      },
    });
    if (serviceExist) {
      throw new ConflictException(`Service already exist with same title`);
    }

    const {
      title,
      thumbnail,
      categoryId,
      subCategoryId,
      description,
      tags,
      images,
    } = createServiceDto;
    const imagePaths = Array.isArray(images) ? images : [];
    const service = await this.serviceModel.create({
      title,
      thumbnail,
      categoryId,
      subCategoryId,
      description,
      userId: userId,
      images: imagePaths, /// Associate the service with the user
    });

    for (const tagId of tags) {
      // Find the tag by its name
      const tag = await this.tagsModel.findByPk(tagId);

      // If the tag exists, associate it with the service through the junction table
      if (tag) {
        await service.$add('tags', tag);
      }
    }
    const user = await User.findOne({
      where: { id: userId },
      attributes: {
        include: [
          [
            sequelize.literal(`(
              SELECT COUNT(*)
              FROM "services" AS s
              WHERE s."userId" = "User"."id" 
              AND s."status" = 'active'
              AND DATE_TRUNC('month', s."createdAt") = DATE_TRUNC('month', CURRENT_DATE)
            )`),
            'productCount',
          ],
        ],
      },
    });
    return {
      service: service,
      productCount: user.getDataValue('productCount'),
    };
  }

  async updateService(updateServiceDto: UpdateServiceDto): Promise<Service> {
    const service = await this.serviceModel.findByPk(updateServiceDto.id);
    if (!service) {
      throw new NotFoundException(`service/product not found`);
    }

    const {
      title,
      thumbnail,
      categoryId,
      subCategoryId,
      description,
      tags,
      images,
    } = updateServiceDto;

    service.title = title;
    service.thumbnail = thumbnail;
    service.categoryId = categoryId;
    service.subCategoryId = subCategoryId;
    service.description = description;

    if (images) {
      service.images = Array.isArray(images) ? images : [];
      service.images = Array.isArray(images) ? images : [];
    }
    await service.save();
    await service.$set('tags', []);
    for (const tagId of tags) {
      const tag = await this.tagsModel.findByPk(tagId);

      if (tag) {
        await service.$add('tags', tag);
      }
    }
    return service;
  }

  async deleteService(
    userId: number,
    updateServiceStatusDto: UpdateServiceStatusDto,
  ) {
    try {
      const service = await this.serviceModel.findOne({
        where: {
          id: updateServiceStatusDto.id,
          userId,
        },
      });
      if (!service) {
        throw new NotFoundException(`service/product not found`);
      }

      const { status } = updateServiceStatusDto;

      service.status = status;

      await service.save();
      const user = await User.findOne({
        where: { id: userId },
        attributes: {
          include: [
            [
              sequelize.literal(`(
                SELECT COUNT(*)
                FROM "services" AS s
                WHERE s."userId" = "User"."id" 
                AND s."status" = 'active'
                AND DATE_TRUNC('month', s."createdAt") = DATE_TRUNC('month', CURRENT_DATE)
              )`),
              'productCount',
            ],
          ],
        },
      });

      return {
        success: true,
        message: `Job deleted successfully.`,
        productCount: user.getDataValue('productCount'),
      };
    } catch (error) {
      throw new InternalServerErrorException(
        `Failed to delete service status ${error.message}`,
      );
    }
  }

  async getAllServices(
    page: number,
    limit: number,
    role: string,
    user: any,
    filters?: any,
  ): Promise<{ services: Service[]; total: number }> {
    const offset = (page - 1) * limit;
    const userExist = await this.userModel.findOne({
      where: { id: user.id },
      include: [
        {
          model: UserSubscription,
          as: 'subscription',
          where: {
            status: {
              [Op.in]: [SubscriptionStatus.ACTIVE],
            },
          },
          required: true,
          include: [
            {
              model: Package,
              as: 'package',
              include: [
                {
                  model: PackageDetails,
                  as: 'packageDetails',
                },
              ],
            },
          ],
        },
      ],
    });
    const userRadius =
      userExist.subscription.package.packageDetails.dataValues.radiusLimit;
    const whereCondition: any = {
      status: { [Op.ne]: ServiceStatus.DELETED },
    };

    if (role === 'seller') {
      whereCondition.userId = user.id;
    }

    if (filters?.search) {
      whereCondition[Op.or] = [
        { title: { [Op.iLike]: `%${filters.search}%` } },
        sequelize.literal(`
      EXISTS (
        SELECT 1 FROM "ServiceTags" AS st
        JOIN "Tags" AS t ON st."tagId" = t."id"
        WHERE st."serviceId" = "Service"."id"
        AND t."name" ILIKE '%${filters.search}%'
      )
    `),
        sequelize.literal(`
  EXISTS (
    SELECT 1 FROM "Categories" AS c
    WHERE c."id" = "Service"."categoryId"
    AND c."name" ILIKE '%${filters.search}%'
  )
`),
        sequelize.literal(`
  EXISTS (
    SELECT 1 FROM "Categories" AS sc
    WHERE sc."id" = "Service"."subCategoryId"
    AND sc."name" ILIKE '%${filters.search}%'
  )
`),
      ];
    }

    const userGeolocation = user.geolocation;

    let latitude: number, longitude: number, distance: number;
    if (filters?.geolocation && filters?.radius) {
      latitude = filters?.geolocation[1];
      longitude = filters?.geolocation[0];
      distance = filters?.radius * 1000;
    } else if (filters?.geolocation) {
      latitude = filters?.geolocation[1];
      longitude = filters?.geolocation[0];
      distance = userRadius * 1000;
    } else if (filters?.radius && userGeolocation) {
      latitude = userGeolocation.coordinates[1];
      longitude = userGeolocation.coordinates[0];
      distance = filters?.radius * 1000;
    } else if (userGeolocation) {
      latitude = userGeolocation.coordinates[1];
      longitude = userGeolocation.coordinates[0];
      distance = userRadius * 1000;
    } else {
      throw new CustomException(
        ERROR_MESSAGES.UPDATE_LOCATION,
        HttpStatus.BAD_REQUEST,
      );
    }
    const blockUsesIds = await this.userBlockModel.findAll({
      where: {
        blockerId: user?.id,
      },
    });
    const blockedUserIds = blockUsesIds.map((block) => block.blockedId);
    console.log('blockUsesIds', blockUsesIds.length);
    const count = await this.serviceModel.count({
      where: whereCondition,
      include: [
        {
          model: Tags,
          as: 'tags',
          required: true,
          through: {
            attributes: [],
          },
        },
        {
          model: Category,
          as: 'category',
          required: false,
        },
        {
          model: Category,
          as: 'subCategory',
          required: false,
        },
        {
          model: User,
          as: 'user',
          attributes: [
            'id',
            'firstName',
            'lastName',
            'email',
            'phoneNumber',
            'profilePicture',
            'type',
            'city',
            [
              sequelize.literal(`(
        SELECT CAST(ROUND(AVG(rating) * 2) / 2 AS DECIMAL(2,1))
              FROM "ReviewRatings" AS rr
              WHERE rr."revieweeId" = "user"."id"
            )`),
              'averageRating',
            ],
          ],
          where: {
            [Op.and]: [
              sequelize.where(
                sequelize.literal(`
                ST_DWithin(
                  "user"."geolocation"::geography,
                  ST_SetSRID(ST_MakePoint(${longitude}, ${latitude}), 4326)::geography,
                  ${distance}
                )
              `),
                true,
              ),
              { id: { [Op.notIn]: blockedUserIds } },
              { isActive: true },
              { isDeleted: false },
            ],
          },
        },
      ],
      distinct: true,
    });

    const rows = await this.serviceModel.findAll({
      where: whereCondition,
      include: [
        {
          model: Tags,
          as: 'tags',
          attributes: ['id', 'name'],
          through: { attributes: [] },
          required: false,
        },
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name'],
          required: false,
        },
        {
          model: Category,
          as: 'subCategory',
          attributes: ['id', 'name'],
          required: false,
        },
        {
          model: User,
          as: 'user',
          attributes: [
            'id',
            'firstName',
            'lastName',
            'email',
            'phoneNumber',
            'profilePicture',
            'type',
            'isActive',
            'city',
            'isDeleted',
            [
              sequelize.literal(`(
        SELECT CAST(ROUND(AVG(rating) * 2) / 2 AS DECIMAL(2,1))
              FROM "ReviewRatings" AS rr
              WHERE rr."revieweeId" = "user"."id"
            )`),
              'averageRating',
            ],
          ],
          where: {
            [Op.and]: [
              sequelize.where(
                sequelize.literal(`
                ST_DWithin(
                  "user"."geolocation"::geography,
                  ST_SetSRID(ST_MakePoint(${longitude}, ${latitude}), 4326)::geography,
                  ${distance}
                )
              `),
                true,
              ),
              { id: { [Op.notIn]: blockedUserIds } },
              { isActive: true },
              { isDeleted: false },
            ],
          },
        },
      ],
      offset,
      limit,
      order: [['createdAt', 'DESC']],
      logging: true,
    });
    return { total: count, services: rows };
  }

  async getServiceById(id: number, loggedInUserId: number): Promise<Service> {
    const service = await this.serviceModel.findOne({
      where: {
        id,
      },
      include: [
        {
          model: Tags,
          as: 'tags',
          through: { attributes: [] },
        },
        {
          model: Category,
          as: 'category',
          include: [
            {
              model: Category,
              as: 'subCategories',
              attributes: ['id', 'name', 'roleId', 'createdAt'],
            },
          ],
        },
        {
          model: Category,
          as: 'subCategory',
        },
        {
          model: User,
          as: 'user',
          attributes: [
            'id',
            'firstName',
            'lastName',
            'email',
            'phoneNumber',
            'profilePicture',
            'city',
            'state',
            'pincode',
            'type',
            [
              sequelize.literal(`(
        SELECT CAST(ROUND(AVG(rating) * 2) / 2 AS DECIMAL(2,1))
              FROM "ReviewRatings" AS rr
              WHERE rr."revieweeId" = "user"."id"
            )`),
              'averageRating',
            ],
          ],
        },
        {
          model: RequestQuotation,
          as: 'requestQuotations',
          attributes: ['id'], // You can include more fields if needed
          where: {
            userId: loggedInUserId, // Ensure the logged-in user is the one requesting the quotation
            serviceId: id, // Ensure the service ID matches the current service
          },
          required: false, // This allows the service to return even if no RequestedQuotation is found
        },
      ],
    });
    if (!service) {
      throw new NotFoundException(`Service with ID ${id} not found`);
    }
    const resp: any = service.toJSON();
    if (resp?.category?.subCategories?.length > 0) {
      resp.category = {
        ...resp.category,
        subCategoryExist: true,
      };
      delete resp.category.subCategories;
    } else
      resp.category = {
        ...resp.category,
        subCategoryExist: false,
      };
    if (resp?.requestQuotations?.length > 0) {
      resp.hasRequestedQuotation = true;
    } else {
      resp.hasRequestedQuotation = false;
    }
    return resp;
  }

  async getServiceByUserId(
    userId: number,
    page: number,
    limit: number,
  ): Promise<{ services: Service[]; total: number }> {
    const offset = (page - 1) * limit;
    const user = await this.userModel.findOne({
      where: {
        id: userId,
      },
      include: [
        {
          model: Role,
          as: 'roles',
        },
      ],
    });
    if (user.roles[0].roleName !== 'seller') {
      throw new ConflictException('only seller is authorised');
    }
    const whereCondition = {
      userId: userId,
      status: ServiceStatus.ACTIVE,
    };

    const services = await this.serviceModel.findAndCountAll({
      where: whereCondition,
      include: [
        {
          model: Tags,
          as: 'tags',
          through: { attributes: [] },
        },
        {
          model: Category,
          as: 'category',
        },
        {
          model: Category,
          as: 'subCategory',
        },
        {
          model: User,
          as: 'user',
          attributes: [
            'id',
            'firstName',
            'lastName',
            'email',
            'phoneNumber',
            'profilePicture',
          ],
        },
      ],
      offset,
      limit,
      order: [['createdAt', 'DESC']],
      distinct: true,
    });
    return { total: services.count, services: services.rows };
  }
  //////////////// service tags //////////////////
  async createServiceTag(inputDto: keywordDto): Promise<Tags> {
    return this.tagsModel.create({
      name: inputDto.name,
    });
  }

  async updateServiceTag(updatekeywordDto: keywordDto): Promise<Tags> {
    const keyword = await this.tagsModel.findByPk(updatekeywordDto.id);

    keyword.name = updatekeywordDto.name;

    await keyword.save();
    return keyword;
  }

  async getAllServicetags(
    page: number,
    limit: number,
  ): Promise<{ tags: Tags[]; total: number }> {
    const offset = (page - 1) * limit;
    const { count, rows } = await this.tagsModel.findAndCountAll({
      limit,
      offset,
    });
    return { total: count, tags: rows };
  }

  async getServiceTagById(id: number): Promise<Tags> {
    const keyword = await this.tagsModel.findByPk(id);
    if (!keyword) {
      throw new NotFoundException(`keyword with ID ${id} not found`);
    }
    return keyword;
  }

  //////////////////////// requestQuotation ///////////////////////

  async requestQuotation(
    dto: RequestQuotationDto,
    userId: number,
    requestedTo?: number, // requestedTo parameter, optional
    serviceId?: number, // serviceId parameter, optional
  ) {
    if (serviceId) {
      const service = await this.serviceModel.findOne({
        where: { id: serviceId },
      });

      if (!service) {
        throw new NotFoundException(`Service with ID ${serviceId} not found`);
      }

      requestedTo = service.userId;
    } else {
      if (!requestedTo) {
        throw new NotFoundException(
          'requestedTo must be provided if serviceId is not given',
        );
      }
    }

    const newQuotation = {
      ...dto,
      userId,
      requestedTo,
      serviceId: serviceId || null,
    };
    const quotation = await this.requestQuotationModel.create(newQuotation);

    const user = await User.findOne({
      where: { id: userId },
      attributes: {
        include: [
          [
            sequelize.literal(`(
          SELECT COUNT(*)
          FROM "RequestQuotations" AS rq
          WHERE rq."userId" = "User"."id"
          AND DATE_TRUNC('month', rq."createdAt") = DATE_TRUNC('month', CURRENT_DATE)
        )`),
            'requestedQuotationCount',
          ],
        ],
      },
    });
    if (quotation) {
      const data = {
        title: notificationTitle.QUOTATION_REQUEST,
        message: notificationMessage.QUOTATION_REQUEST,
        type: notificationType.QUOTATION_REQUEST,
        receiverId: requestedTo,
        additional: {
          quotationId: quotation.id,
        },
        prefrence: 'newQuotation',
      };
      await this.notificationService.sendNotification(data);
    }
    const userDetails = await this.userModel.findOne({
      where: {
        id: requestedTo,
      },
      include: [
        {
          model: NotificationPreference,
          as: 'notificationPreference',
          required: false,
        },
      ],
    });

    // Check user's notification preferences and inactive status
    const userPreferences = userDetails.notificationPreference;
    const fourteenDaysAgo = new Date();
    fourteenDaysAgo.setDate(fourteenDaysAgo.getDate() - 14);

    // Check if user is inactive (hasn't logged in for 14+ days or never logged in)
    const isInactiveUser =
      !userDetails.lastLogin ||
      new Date(userDetails.lastLogin) < fourteenDaysAgo;

    // Check if email notifications are enabled and user has an email
    const hasEmailNotificationsEnabled =
      userPreferences?.email === true && userDetails.email;

    // Send email if user has enabled notifications and is inactive
    if (hasEmailNotificationsEnabled && isInactiveUser) {
      await this.sendgridService.sendEmail(
        userDetails.email,
        'New Quotation Request',
        Email.getTemplate({
          title: Constants.Titles.NewQuotationRequest,
          userName: `${userDetails.firstName} ${userDetails.lastName}`,
          children: `
        <p>
          A user has requested a quotation for their project. Log in to view the details and submit your offer.
        </p>
      `,
        }),
      );
    }

    return {
      quotation: quotation,

      requestedQuotationCount: user.getDataValue('requestedQuotationCount'),
    };
  }

  async getAllRequestedQuotation(
    userId: number,
    role: string,
    page: number,
    limit: number,
    search?: string,
  ): Promise<{ requestQuotations: RequestQuotation[]; total: number }> {
    const offset = (page - 1) * limit;
    const whereCondition = {
      [Op.and]: [
        role === 'seller' ? { requestedTo: userId } : { userId: userId },
        search ? { title: { [Op.iLike]: `%${search}%` } } : {},
      ],
    };
    const { rows, count } = await this.requestQuotationModel.findAndCountAll({
      where: whereCondition,
      include: [
        {
          model: Service,
          as: 'service',
        },
        {
          model: RecievedQuotation,
          as: 'receivedQuotation',
          attributes: ['id', 'isRead'],
        },
        {
          model: User,
          as: 'user',
          include: [
            {
              model: Role,
              as: 'roles',
              through: { attributes: [] },
              attributes: ['roleName'],
            },
          ],
          attributes: [
            'id',
            'firstName',
            'lastName',
            'profilePicture',
            'email',
            'phoneNumber',
            'gender',
            'address1',
            'address2',
            'city',
            'state',
            [
              sequelize.literal(`(
                SELECT CAST(ROUND(AVG(rating) * 2) / 2 AS DECIMAL(2,1))
                FROM "ReviewRatings" AS rr
                WHERE rr."revieweeId" = "user"."id"
              )`),
              'averageRating',
            ],
          ],
        },
        {
          model: User,
          as: 'requested',
          attributes: [
            'id',
            'firstName',
            'lastName',
            'profilePicture',
            'email',
            'phoneNumber',
            'gender',
            'address1',
            'address2',
            'city',
            'state',
            [
              sequelize.literal(`(
                SELECT CAST(ROUND(AVG(rating) * 2) / 2 AS DECIMAL(2,1))
                FROM "ReviewRatings" AS rr
                WHERE rr."revieweeId" = "requested"."id"
              )`),
              'averageRating',
            ],
          ],
        },
      ],
      offset,
      limit,
      order: [['createdAt', 'DESC']],
    });
    return { requestQuotations: rows, total: count };
  }

  async getRequestedQuotationById(
    id: number,
    currentUserId: number,
  ): Promise<RequestQuotation> {
    const quotation = await this.requestQuotationModel.findOne({
      where: {
        id,
      },
      include: [
        {
          model: ReviewRating,
          as: 'reviewRating',
          attributes: ['rating', 'review'],
        },
        {
          model: Service,
          as: 'service',
          attributes: {
            exclude: [
              'userId',
              'createdAt',
              'updatedAt',
              'categoryId',
              'subCategoryId',
            ],
          },
        },
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name', 'parentId'],
        },
        {
          model: Category,
          as: 'subCategory',
          attributes: ['id', 'name', 'parentId'],
        },
        {
          model: User,
          as: 'user',
          include: [
            {
              model: Role,
              as: 'roles',
              through: { attributes: [] },
              attributes: ['roleName'],
            },
            {
              model: Category,
              through: {
                attributes: [],
              },
              as: 'categories',
            },
            {
              model: Category,
              through: {
                attributes: [],
              },
              as: 'subCategories',
            },
          ],
          attributes: [
            'id',
            'firstName',
            'lastName',
            'profilePicture',
            'email',
            'phoneNumber',
            'gender',
            'firmName',
            'address1',
            'address2',
            'city',
            'state',
            'pincode',
            [
              Sequelize.literal(`(
                SELECT "id" 
                FROM "connections" 
                WHERE (
                  ("requester_id" = ${currentUserId} AND "receiver_id" = "user"."id") OR 
                  ("receiver_id" = ${currentUserId} AND "requester_id" = "user"."id")
                ) 
                AND "status" = 'accepted' 
                LIMIT 1
              )`),
              'connectionId',
            ],
            [
              Sequelize.literal(`(
                SELECT "isVerified" 
                FROM "connections" 
                WHERE (
                  ("requester_id" = ${currentUserId} AND "receiver_id" = "user"."id") OR 
                  ("receiver_id" = ${currentUserId} AND "requester_id" = "user"."id")
                ) 
                AND "status" = 'accepted' 
                LIMIT 1
              )`),
              'isConnectionVerified',
            ],
            [
              sequelize.literal(`(
                SELECT CAST(ROUND(AVG(rating) * 2) / 2 AS DECIMAL(2,1))
                FROM "ReviewRatings" AS rr
                WHERE rr."revieweeId" = "user"."id"
              )`),
              'averageRating',
            ],
          ],
        },
        {
          model: User,
          as: 'requested',
          attributes: [
            'id',
            'firstName',
            'lastName',
            'profilePicture',
            'email',
            'phoneNumber',
            'gender',
            'address1',
            'address2',
            'city',
            'state',
            [
              sequelize.literal(`(
                SELECT CAST(ROUND(AVG(rating) * 2) / 2 AS DECIMAL(2,1))
                FROM "ReviewRatings" AS rr
                WHERE rr."revieweeId" = "requested"."id"
              )`),
              'averageRating',
            ],
          ],
        },
        {
          model: RecievedQuotation,
          as: 'receivedQuotation',
          include: [
            {
              model: ProductDetails,
              as: 'productDetails',
            },
          ],
        },
        {
          model: Discussion,
          as: 'discussions',
          include: [
            {
              model: User,
              as: 'sender',
              attributes: ['id', 'firstName', 'lastName', 'profilePicture'],
            },
            {
              model: User,
              as: 'receiver',
              attributes: ['id', 'firstName', 'lastName', 'profilePicture'],
            },
          ],
        },
      ],
    });

    if (!quotation) {
      throw new NotFoundException(`Quotation with ID ${id} not found`);
    }
    const hasNotification = await this.notificationModel.findOne({
      where: {
        receiverId: quotation.userId,
        type: 'review_requested',
        additional: {
          quotationId: quotation.id,
        },
      },
    });
    let notificationExists = false;
    if (hasNotification) {
      notificationExists = true;
    }

    const quoteDetails = quotation.toJSON() as any;
    quoteDetails.hasNotification = notificationExists;
    return quoteDetails;
  }

  async updateStatus(
    id: number,
    updateStatusDto: UpdateQuotationStatusDto,
  ): Promise<RequestQuotation> {
    const quotation = await this.requestQuotationModel.findOne({
      where: { id },
    });

    if (!quotation) {
      throw new NotFoundException(`Quotation not found`);
    }
    if (quotation.status === RequestedQuotationStatus.REQUESTED) {
      throw new ConflictException(`Quotation has not been received.`);
    }
    if (updateStatusDto.status === RequestedQuotationStatus.ACCEPTED) {
      const connection = await this.ConnectionModel.findOne({
        where: {
          [Op.or]: [
            {
              requester_id: quotation.userId,
              receiver_id: quotation.requestedTo,
            },
            {
              requester_id: quotation.requestedTo,
              receiver_id: quotation.userId,
            },
          ],
        },
      });
      if (connection) {
        if (connection.status === 'pending') {
          await this.ConnectionModel.update(
            { status: 'accepted' },
            {
              where: {
                id: connection.id,
              },
            },
          );
        }
      } else {
        await this.ConnectionModel.create({
          requester_id: quotation.userId,
          receiver_id: quotation.requestedTo,
          status: 'accepted',
        });
      }
    }
    quotation.status = updateStatusDto.status;
    if (updateStatusDto.status === RequestedQuotationStatus.ACCEPTED) {
      const user = await this.userModel.findOne({
        where: {
          id: quotation.userId,
        },
        include: [
          {
            model: Role,
            as: 'roles',
            through: { attributes: [] },
            attributes: ['roleName'],
            required: false,
          },
        ],
      });
      const data = {
        type: notificationType.QUOTATION_ACCEPTED,
        receiverId: quotation.requestedTo,
        additional: {
          quotationId: quotation.id,
          userId: quotation.userId,
          firstName: user.firstName,
          lastName: user.lastName,
          profilePicture: user.profilePicture,
          role: user.roles[0].roleName,
          prefrence: 'inAppNotifications',
        },
      };
      await this.notificationService.sendNotification(data);
    }
    await quotation.save();
    return quotation;
  }
  /////////////////////////////  send Quotation  /////////////////////////

  async sendQuotation(
    userId: number,
    quotationId: number,
    details: SendQuotationDto,
  ): Promise<RecievedQuotation> {
    const { companyDetails, billingDetails, productDetails } = details;
    const quotationExist = await this.requestQuotationModel.findOne({
      where: {
        id: quotationId,
        requestedTo: userId,
      },
      include: [
        {
          model: RecievedQuotation,
          as: 'receivedQuotation',
          attributes: ['id'],
        },
      ],
    });
    if (!quotationExist) {
      throw new NotFoundException(`Quotation not found`);
    }
    if (quotationExist.receivedQuotation) {
      throw new ConflictException(`Quotation has already been sent.`);
    }

    // Create the received quotation with company and billing details
    const quotation = await this.recievedQuotationModel.create({
      companyDetails,
      billingDetails,
      requestQuotationId: quotationId,
    });
    await quotationExist.update({
      status: RequestedQuotationStatus.RECEIVED,
    });

    if (productDetails) {
      // Create the product details and associate them with the quotation
      for (const product of productDetails) {
        await this.productDetailsModel.create({
          ...product,
          receivedQuotationId: quotation.id,
        });
      }
    }
    const data = {
      type: notificationType.QUOTATION_RECIEVED,
      receiverId: quotationExist.userId,
      additional: {
        quotationId: quotationExist.id,
        prefrence: 'inAppNotifications',
      },
    };
    await this.notificationService.sendNotification(data);

    return quotation;
  }

  async getRecievedQuotations(
    page: number,
    limit: number,
  ): Promise<RecievedQuotation[]> {
    const offset = (page - 1) * limit;
    return this.recievedQuotationModel.findAll({
      include: [{ model: ProductDetails }],
      offset,
      limit,
    });
  }

  async getQuotationById(id: number): Promise<RecievedQuotation> {
    const quotation = await this.recievedQuotationModel.findOne({
      where: { id },
      include: [{ model: ProductDetails }],
    });

    if (!quotation) {
      throw new NotFoundException(`Quotation with ID ${id} not found`);
    }

    return quotation;
  }

  async updateIsRead(
    id: number,
    userId: number,
    query: UpdateIsReadDto,
  ): Promise<any> {
    if (query.type === 'quotationReceived') {
      const quotation = await this.recievedQuotationModel.findOne({
        where: {
          id,
        },
        include: [
          {
            model: RequestQuotation,
            as: 'requestQuotation',
            where: {
              userId,
            },
          },
        ],
      });

      if (!quotation) {
        throw new NotFoundException('quotation not found');
      }

      quotation.isRead = true;
      await quotation.save();
      console.log('quotation', quotation);
      return { message: 'recieved quotation read successfully' };
    } else if (query.type === 'quotationRequest') {
      const quotation = await this.requestQuotationModel.findOne({
        where: {
          id,
          requestedTo: userId,
        },
      });

      if (!quotation) {
        throw new NotFoundException('quotation not found');
      }
      quotation.isRead = true;
      await quotation.save();
      console.log('quotation', quotation);
      return { message: 'quotation read successfully' };
    }
  }

  ///////////////////////////////////  productDetails  /////////////////////////

  async createProductDetails(
    data: ProductDetailsDto,
    recievedQuotationId: number,
  ): Promise<ProductDetails> {
    return await this.productDetailsModel.create({
      title: data.title,
      description: data.description,
      productId: data.productId,
      recievedQuotationId: recievedQuotationId,
      unitCost: data.unitCost,
      quantity: data.quantity,
      amount: data.amount,
    });
  }

  async verifyPayment(
    quotationId: number,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    userId?: number,
  ): Promise<RecievedQuotation> {
    const recievedQuotation = await this.recievedQuotationModel.findOne({
      where: {
        requestQuotationId: quotationId,
      },
      include: [
        {
          model: RequestQuotation,
          as: 'requestQuotation',
        },
      ],
    });
    console.log(
      'recievedQuotation',
      recievedQuotation.requestQuotation.requestedTo,
    );
    if (!recievedQuotation) {
      throw new NotFoundException('Quotation not found');
    }

    recievedQuotation.isPaid = true;
    await recievedQuotation.save();

    return recievedQuotation;
  }
}
