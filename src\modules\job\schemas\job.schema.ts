import {
  Table,
  Column,
  Model,
  ForeignKey,
  BelongsTo,
  DataType,
  HasOne,
  HasMany,
} from 'sequelize-typescript';
import { User } from 'src/modules/admin/authentication/schemas/user.schema';
import { BiddingDetail } from './bidding-details.schema';
import { UserJobs } from 'src/modules/user-jobs/schemas/user-jobs.schema';
import { JobStatus } from 'src/common/utils/enum';
import { Role } from 'src/modules/roles/schemas/role.schema';
import { Category } from 'src/modules/ category/schemas/category.schema';
import { ReviewRating } from 'src/modules/review-rating/schemas/review-rating.schema';
@Table({ tableName: 'jobs' })
export class Job extends Model<Job> {
  @Column
  title: string;

  @Column(DataType.TEXT)
  description: string;

  @Column
  radius: number;

  @Column(DataType.ARRAY(DataType.STRING))
  images: string[];

  // @Column({ defaultValue: 'pending' })
  // status: string;
  @Column({
    type: DataType.ENUM,
    values: Object.values(JobStatus),
    defaultValue: JobStatus.PUBLISHED,
  })
  status: string;

  @Column({
    type: DataType.GEOGRAPHY('POINT'),
    allowNull: true,
  })
  geolocation: { type: string; coordinates: [number, number] };

  @Column
  city: string;

  @Column
  state: string;

  @Column
  pincode: string;

  @ForeignKey(() => User)
  @Column({ allowNull: false })
  userId: number;

  @BelongsTo(() => User)
  createdBy: User;

  @ForeignKey(() => Role)
  @Column({ allowNull: false })
  roleId: number;

  @BelongsTo(() => Role)
  lookingFor: Role;

  @ForeignKey(() => Category)
  @Column({ allowNull: true })
  roleSubcategoryId: number;

  @ForeignKey(() => Category)
  @Column({ allowNull: true })
  subCategoryId: number;

  @BelongsTo(() => Category, {
    foreignKey: 'roleSubcategoryId',
    as: 'criteria',
  })
  criteria: Category;

  @BelongsTo(() => Category, {
    foreignKey: 'subCategoryId',
    as: 'subCategory',
  })
  subcategory: Category;

  @HasOne(() => BiddingDetail)
  biddingDetail: BiddingDetail;

  @HasOne(() => ReviewRating, {
    foreignKey: 'jobId',
    as: 'reviewRating',
  })
  reviewRating: ReviewRating;

  // @BelongsToMany(() => User, () => UserJobs)
  // users: User[];

  @HasMany(() => UserJobs)
  userJobs: UserJobs[];
}
