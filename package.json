{"name": "apna_builder", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "node main", "dev": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "migrate:up": "npx sequelize-cli db:migrate", "migrate:undo": "npx sequelize-cli db:migrate:undo"}, "dependencies": {"@nestjs/axios": "^3.1.3", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.2.2", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^4.1.2", "@nestjs/sequelize": "^10.0.1", "@nestjs/serve-static": "^4.0.2", "@nestjs/swagger": "^7.3.1", "@sendgrid/mail": "^8.1.4", "aws-sdk": "^2.1632.0", "axios": "^1.7.7", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "crypto": "^1.0.1", "csv-writer": "^1.6.0", "firebase-admin": "^12.6.0", "install": "^0.13.0", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "mysql2": "^3.9.8", "nestjs-i18n": "^10.4.9", "npm": "^10.9.2", "passport": "^0.7.0", "passport-apple": "^2.0.2", "passport-facebook": "^3.0.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-linkedin-oauth2": "^2.0.0", "passport-twitter": "^1.0.4", "pg": "^8.11.5", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "sequelize": "^6.37.3", "sequelize-typescript": "^2.1.6", "swagger-ui-express": "^5.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.21", "@types/jest": "^29.5.2", "@types/multer": "^1.4.11", "@types/node": "^20.0.0", "@types/sequelize": "^4.28.20", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}