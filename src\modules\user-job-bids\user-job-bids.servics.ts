import { Injectable, NotFoundException } from '@nestjs/common';

import { InjectModel } from '@nestjs/sequelize';
import { UserJobBids } from './schemas/user-job-bids.schema';
import { UserJobBidStatus } from './utills/enum';
import { Op } from 'sequelize';
import { JobsService } from '../job/job.service';
import { JobStatus } from 'src/common/utils/enum';
import { User } from '../admin/authentication/schemas/user.schema';
import { Role } from '../roles/schemas/role.schema';
import { NotificationService } from '../notification/notification.service';
import { notificationType } from 'src/common/messages';
import { Connection } from '../connection/schemas/connection.schema';
import { Job } from '../job/schemas/job.schema';
import sequelize from 'sequelize';
import { NotificationPreference } from '../user-management/schemas/notification-preferences.schema';
// import { SendgridService } from '../sendgrid/sendgrid.service';

@Injectable()
export class UserJobBidService {
  constructor(
    @InjectModel(UserJobBids)
    private userJobsBidModel: typeof UserJobBids,
    @InjectModel(User)
    private userModel: typeof User,
    @InjectModel(Connection)
    private connectionModel: typeof Connection,
    private jobService: JobsService,
    private readonly notificationService: NotificationService,
    // private readonly sendgridService: SendgridService,
  ) {}

  async bulkCreate(createUserJobsDto: any[]): Promise<UserJobBids[]> {
    return this.userJobsBidModel.bulkCreate(createUserJobsDto);
  }
  async create(createUserJobsDto: any): Promise<UserJobBids> {
    const result = await this.userJobsBidModel.create(createUserJobsDto);
    return result;
  }

  async getAllWithCount(
    query: any,
  ): Promise<{ userJobs: UserJobBids[]; total: number }> {
    const { count, rows } = await this.userJobsBidModel.findAndCountAll(query);
    return { userJobs: rows, total: count };
  }
  async getAll(query: any): Promise<UserJobBids[]> {
    return this.userJobsBidModel.findAll(query);
  }
  async getOne(query: any): Promise<UserJobBids> {
    return this.userJobsBidModel.findOne(query);
  }
  async getCount(query: any) {
    return this.userJobsBidModel.count(query);
  }
  async deleteOne(query: any) {
    return this.userJobsBidModel.destroy(query);
  }
  async patchUpdateStatus(existingData: any, status: string) {
    existingData.status = status;
    if (status === UserJobBidStatus.ACCEPTED) {
      await this.userJobsBidModel.update(
        {
          status: UserJobBidStatus.CLOSED,
        },
        {
          where: {
            jobId: existingData.jobId,
            [Op.not]: {
              id: existingData.id,
            },
          },
        },
      );
      await this.jobService.updateJobStatus(
        existingData.jobId,
        JobStatus.CLOSED,
      );
    }
    if (status === UserJobBidStatus.ACCEPTED) {
      const connection = await this.connectionModel.findOne({
        where: {
          [Op.or]: [
            {
              requester_id: existingData.userId,
              receiver_id: existingData.job.createdBy.id,
            },
            {
              requester_id: existingData.job.createdBy.id,
              receiver_id: existingData.userId,
            },
          ],
        },
      });
      console.log('connection::::::', connection);
      if (connection) {
        if (connection.status === 'pending') {
          await this.connectionModel.update(
            { status: 'accepted' },
            {
              where: {
                id: connection.id,
              },
            },
          );
        }
      } else {
        await this.connectionModel.create({
          requester_id: existingData.userId,
          receiver_id: existingData.job.createdBy.id,
          status: 'accepted',
        });
      }
    }
    if (status === UserJobBidStatus.ACCEPTED) {
      console.log('revieweeID::::::::::::', existingData.job.createdBy.id);
      const data = {
        type: notificationType.BID_ACCEPTED,
        receiverId: existingData.userId,
        additional: {
          jobId: existingData.job.id,
          userBidId: existingData.id,
          revieweeId: existingData.job.createdBy.id,
          firstName: existingData.job.createdBy.firstName,
          lastName: existingData.job.createdBy.lastName,
          profilePicture: existingData.job.createdBy.profilePicture,
          role: existingData.job.createdBy.roles[0].roleName,
          prefrence: 'newBidRequest',
        },
      };
      console.log('data::::::::::', data);
      await this.notificationService.sendNotification(data);
    }

    return existingData.save();
  }

  async getUserJobBidCount(userId: number) {
    const user = await this.userModel.findOne({
      where: {
        id: userId,
      },
      attributes: {
        include: [
          [
            sequelize.literal(`(
          SELECT COUNT(*)
          FROM "UserJobBids" AS ujb
          WHERE ujb."userId" = "User"."id" 
          AND ujb."status" = 'accepted'
          AND DATE_TRUNC('month', ujb."createdAt") = DATE_TRUNC('month', CURRENT_DATE)

        )`),
            'JobBidCount',
          ],
        ],
      },
    });
    return user;
  }
  async verifyPayment(userJobBidId: number, userId: number) {
    const userJobBid = await this.userJobsBidModel.findOne({
      where: {
        id: userJobBidId,
      },
      include: [
        {
          model: Job,
          as: 'job',
          attributes: ['id'],
          include: [
            {
              model: User,
              as: 'createdBy',
              where: {
                id: userId,
              },
              attributes: ['id'],
            },
          ],
        },
      ],
    });
    if (!userJobBid) {
      throw new NotFoundException('User Job Bid not found');
    }

    userJobBid.isPaid = true;
    await userJobBid.save();
    const user = await this.userModel.findOne({
      where: {
        id: userId,
      },
      include: [
        {
          model: Role,
          as: 'roles',
          through: { attributes: [] },
          attributes: ['roleName'],
          required: false,
        },
        {
          model: NotificationPreference,
          as: 'notificationPreference',
        },
      ],
    });
    // const data = {
    //   type: notificationType.POST_REVIEW,
    //   receiverId: userJobBid.userId,
    //   additional: {
    //     jobId: userJobBid.job.id,
    //     revieweeId: userJobBid.job.createdBy.id,
    //     firstName: user.firstName,
    //     lastName: user.lastName,
    //     profilePicture: user.profilePicture,
    //     role: user.roles[0].roleName,
    //   },
    //   prefrence: 'inAppNotifications',
    // };
    // if (user.notificationPreference?.inAppNotifications) {
    //   await this.notificationService.sendNotification(data);
    // }
    return userJobBid;
  }
}
