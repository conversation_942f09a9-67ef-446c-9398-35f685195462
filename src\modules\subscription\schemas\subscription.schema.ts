// import { Column, DataType, <PERSON><PERSON><PERSON>, Model, Table } from 'sequelize-typescript';
// import { User } from 'src/modules/admin/authentication/schemas/user.schema';
// // import { UserRoles } from 'src/app/roles/entities/user-roles.entity';
// // import { Entity, Column, PrimaryGeneratedColumn, JoinColumn, ManyToOne } from 'typeorm';

// export enum PackageType {
//   MONTHLY = 'Monthly',
//   QUARTERLY = 'Quarterly',
//   YEARLY = 'Yearly',
// }

// @Table
// export class Subscription extends Model {
//   @Column
//   title: string;

//   @Column({
//     type: DataType.ENUM,
//     values: ['Monthly', 'Quarterly', 'Yearly'],
//     defaultValue: 'Monthly',
//   })
//   package_type: PackageType;

//   @Column(DataType.FLOAT)
//   price: number;

//   @Column(DataType.FLOAT)
//   discount: number;

//   @Column
//   roles: string;

//   @Column(DataType.BOOLEAN)
//   status: boolean;

//   // @HasMany(() => User)
//   // users: User[];
// }
