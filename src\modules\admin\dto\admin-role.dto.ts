import { IsString, IsOptional, IsBoolean } from 'class-validator';

export class AdminRoleDto {
  @IsString()
  roleName: string;

  @IsString()
  description: string;

  @IsOptional()
  @IsBoolean()
  admins?: boolean = false;

  @IsOptional()
  @IsBoolean()
  user_management?: boolean = false;

  @IsOptional()
  @IsBoolean()
  dashboard?: boolean = false;

  @IsOptional()
  @IsBoolean()
  content_management?: boolean = false;

  @IsOptional()
  @IsBoolean()
  ads_manager?: boolean = false;

  @IsOptional()
  @IsBoolean()
  subscription_management?: boolean = false;
}
