'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('Packages', 'gst', {
      type: Sequelize.FLOAT,
      allowNull: true,
    });

    await queryInterface.addColumn('Packages', 'discountPercentage', {
      type: Sequelize.FLOAT,
      allowNull: true,
    });

  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('Packages', 'gst');
    await queryInterface.removeColumn('Packages', 'discountPercentage');
  },
};

