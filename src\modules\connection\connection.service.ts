import {
  Injectable,
  NotFoundException,
  ConflictException,
  ForbiddenException,
} from '@nestjs/common';
import * as moment from 'moment';
import { InjectModel } from '@nestjs/sequelize';
import { Connection } from './schemas/connection.schema';
import { User } from '../admin/authentication/schemas/user.schema';
import { Op } from 'sequelize';
import { Role } from '../roles/schemas/role.schema';
import { UserRole } from '../roles/schemas/usersRole.schema';
import { I18nContext, I18nService } from 'nestjs-i18n';
import sequelize from 'sequelize';
import { notificationTitle, notificationType } from 'src/common/messages';
import { NotificationService } from '../notification/notification.service';
@Injectable()
export class ConnectionService {
  constructor(
    @InjectModel(Connection)
    private connectionModel: typeof Connection,
    @InjectModel(User)
    private readonly userRepository: typeof User,
    @InjectModel(Role)
    private roleRepository: typeof Role,
    @InjectModel(UserRole)
    private userRoleRepository: typeof UserRole,
    private readonly notificationService: NotificationService,
    private readonly i18n: I18nService,
  ) {}
  async sendConnection(requesterId: number, receiverId: number) {
    const existingConnection = await this.connectionModel.findOne({
      where: {
        requester_id: requesterId,
        receiver_id: receiverId,
        status: 'pending',
      },
    });

    if (existingConnection) {
      const message = this.i18n.t('test.errorMessage.ALREADY_SENT', {
        lang: I18nContext.current().lang,
        args: { data: 'Connection' },
      });
      throw new ConflictException(message);
    }

    const connection = await this.connectionModel.create({
      requester_id: requesterId,
      receiver_id: receiverId,
      status: 'pending',
      created_at: new Date(),
      updated_at: new Date(),
    });
    if (connection) {
      const data = {
        type: notificationType.CONNECTION_REQUEST,
        receiverId: receiverId,
        prefrence: 'inAppNotifications',
        additional: {
          connectionId: connection.id,
          requesterId: requesterId,
          receiverId: receiverId,
        },
      };
      console.log('data.......:::::....', data);
      await this.notificationService.sendNotification(data);
    }
    const connectionRequestCount = await this.connectionModel.count({
      where: {
        requester_id: requesterId,
        created_at: {
          [Op.between]: [
            moment().startOf('month').toDate(),
            moment().endOf('month').toDate(),
          ],
        },
      },
    });

    return {
      connection,
      connectionRequestCount,
    };
  }

  async getPendingConnections(
    userId: number,
    page: number,
    limit: number,
  ): Promise<{ connections: Connection[]; total: number }> {
    const offset = (page - 1) * limit;
    const { count, rows } = await this.connectionModel.findAndCountAll({
      where: {
        status: 'pending',
        [Op.or]: [{ receiver_id: userId }],
      },
      include: [
        {
          model: User,
          as: 'requester',
          attributes: [
            'firstName',
            'lastName',
            'email',
            'phoneNumber',
            'profilePicture',
            [
              sequelize.literal(`(
                SELECT CAST(ROUND(AVG(rating) * 2) / 2 AS DECIMAL(2,1))
                FROM "ReviewRatings" AS rr
                WHERE rr."revieweeId" = "requester"."id"
              )`),
              'averageRating',
            ],
          ],
          include: [
            {
              model: Role,
            },
          ],
        },
        {
          model: User,
          as: 'receiver',
          attributes: [
            'firstName',
            'lastName',
            'email',
            'role',
            'phoneNumber',
            'profilePicture',
            [
              sequelize.literal(`(
                SELECT CAST(ROUND(AVG(rating) * 2) / 2 AS DECIMAL(2,1))
                FROM "ReviewRatings" AS rr
                WHERE rr."revieweeId" = "receiver"."id"
              )`),
              'averageRating',
            ],
          ],
          include: [
            {
              model: Role,
            },
          ],
        },
      ],
      order: [['created_at', 'DESC']],
      offset,
      limit,
    });

    return { connections: rows, total: count };
  }

  async acceptConnection(requestId: number): Promise<Connection> {
    const connection = await this.connectionModel.findOne({
      where: { id: requestId },
    });

    if (!connection) {
      const message = this.i18n.t('test.errorMessage.NOT_FOUND', {
        lang: I18nContext.current().lang,
        args: { data: 'Connection' },
      });
      throw new NotFoundException(message);
    }

    connection.status = 'accepted';
    connection.updated_at = new Date();

    await connection.save();

    return connection;
  }
  async getAcceptedConnections(
    userId: string,
    page: number,
    limit: number,
  ): Promise<{ connections: any[]; total: number }> {
    const offset = (page - 1) * limit;
    const { count, rows } = await this.connectionModel.findAndCountAll({
      where: {
        status: 'accepted',
        [Op.or]: [{ requester_id: userId }, { receiver_id: userId }],
      },
      attributes: [
        'id',
        'requester_id',
        'receiver_id',
        'status',
        'created_at',
        'updated_at',
        'isVerified',
      ],
      include: [
        {
          model: User,
          as: 'requester',
          attributes: [
            'id',
            'firstName',
            'lastName',
            'email',
            'phoneNumber',
            'profilePicture',
            [
              sequelize.literal(`(
                SELECT CAST(ROUND(AVG(rating) * 2) / 2 AS DECIMAL(2,1))
                FROM "ReviewRatings" AS rr
                WHERE rr."revieweeId" = "requester"."id"
              )`),
              'averageRating',
            ],
          ],
          include: [
            {
              model: Role,
            },
          ],
        },
        {
          model: User,
          as: 'receiver',
          attributes: [
            'id',
            'firstName',
            'lastName',
            'email',
            'phoneNumber',
            'profilePicture',
            [
              sequelize.literal(`(
                SELECT CAST(ROUND(AVG(rating) * 2) / 2 AS DECIMAL(2,1))
                FROM "ReviewRatings" AS rr
                WHERE rr."revieweeId" = "receiver"."id"
              )`),
              'averageRating',
            ],
          ],
          include: [
            {
              model: Role,
            },
          ],
        },
      ],
      order: [['updated_at', 'DESC']],
      offset,
      limit,
    });
    const connections = rows.map((connection) => {
      const isRequester = connection.requester_id === parseInt(userId);
      const connectionUser = isRequester
        ? connection.receiver.toJSON()
        : connection.requester.toJSON();
      console.log('connection....', connectionUser);

      return {
        id: connection.id,
        status: connection.status,
        requester_id: connection.requester_id,
        receiver_id: connection.receiver_id,
        created_at: connection.created_at,
        updated_at: connection.updated_at,
        isVerified: connection.isVerified,
        connection: {
          ...connectionUser,
          roles: connectionUser.roles.map((role) => ({
            id: role.id,
            roleName: role.roleName,
          })),
        },
      };
    });
    return { connections: connections, total: count };
  }

  async getUserDetails(userId: number): Promise<User> {
    // Fetch user details from the User table using the userRepository
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      const message = this.i18n.t('test.errorMessage.NOT_FOUND', {
        lang: I18nContext.current().lang,
        args: { data: 'User' },
      });
      throw new NotFoundException(message);
    }

    return user;
  }

  async getSentConnectionRequests(
    userId: number,
    page: number,
    limit: number,
  ): Promise<{ connections: Connection[]; total: number }> {
    const offset = (page - 1) * limit;
    const { count, rows } = await this.connectionModel.findAndCountAll({
      where: { requester_id: userId, status: 'pending' },
      include: [
        {
          model: User,
          as: 'receiver',
          include: [
            {
              model: Role,
            },
          ],
          attributes: {
            include: [
              [
                sequelize.literal(`(
              SELECT CAST(ROUND(AVG(rating) * 2) / 2 AS DECIMAL(2,1))
              FROM "ReviewRatings" AS rr
              WHERE rr."revieweeId" = "receiver"."id"
            )`),
                'averageRating',
              ],
            ],
          },
        },
      ],
      order: [['created_at', 'DESC']],
      offset,
      limit,
    });

    return { connections: rows, total: count };
  }
  async getConnectionById(connectionId: number): Promise<Connection> {
    const connection = await this.connectionModel.findByPk(connectionId, {
      include: [
        {
          model: User,
          as: 'requester',
          include: [
            {
              model: Role,
            },
          ],
        },
        {
          model: User,
          as: 'receiver',
          include: [
            {
              model: Role,
            },
          ],
        },
      ],
    });

    if (!connection) {
      const message = this.i18n.t('test.errorMessage.NOT_FOUND', {
        lang: I18nContext.current().lang,
        args: { data: 'Connection Request' },
      });
      throw new NotFoundException(message);
    }

    return connection;
  }
  async updateConnectionStatus(
    requesterId: number,
    receiverId: number,
    status: 'accept' | 'reject',
  ): Promise<Connection> {
    // Find the connection based on requester and receiver IDs
    const connection = await this.connectionModel.findOne({
      where: {
        requester_id: requesterId,
        receiver_id: receiverId,
      },
    });

    if (!connection) {
      const message = this.i18n.t('test.errorMessage.NOT_FOUND', {
        lang: I18nContext.current().lang,
        args: { data: 'Connection Request' },
      });
      throw new NotFoundException(message);
    }

    if (status === 'accept') {
      connection.status = 'accepted';
      await connection.save();
      const data = {
        type: notificationType.CONNECTION_ACCEPTED,
        receiverId: requesterId,
        prefrence: 'inAppNotifications',
        additional: {
          connectionId: connection.id,
          requesterId: requesterId,
          receiverId: receiverId,
        },
      };
      await this.notificationService.sendNotification(data);
    } else if (status === 'reject') {
      await connection.destroy();
    }

    return connection;
  }

  async deleteConnection(id: number): Promise<void> {
    const connection = await this.connectionModel.findOne({ where: { id } });
    if (!connection) {
      const message = this.i18n.t('test.errorMessage.NOT_FOUND', {
        lang: I18nContext.current().lang,
        args: { data: 'Connection' },
      });
      throw new NotFoundException(message);
    }
    await connection.destroy();
  }
  async verifyConnection(
    connectionId: number,
    userId: number,
  ): Promise<Connection> {
    const connection = await this.connectionModel.findOne({
      where: {
        id: connectionId,
        status: 'accepted',
      },
    });

    if (!connection) {
      const message = this.i18n.t('test.errorMessage.NOT_FOUND', {
        lang: I18nContext.current().lang,
        args: { data: 'Connection Request' },
      });
      throw new NotFoundException(message);
    }

    if (
      connection.requester_id !== userId &&
      connection.receiver_id !== userId
    ) {
      const message = this.i18n.t('test.errorMessage.UNAUTHORIZED_ACCESS', {
        lang: I18nContext.current().lang,
        args: { data: 'to verify this connection' },
      });
      throw new ForbiddenException(message);
    }
    connection.isVerified = true;
    await connection.save();

    return connection;
  }
  async getOneByQuery(query: any) {
    return await this.connectionModel.findOne(query);
  }

  async getConnectionByUserId(requesterId: number, receiverId: number) {
    return await this.connectionModel.findOne({
      where: {
        [Op.or]: [
          { requester_id: requesterId, receiver_id: receiverId },
          { requester_id: receiverId, receiver_id: requesterId },
        ],
      } as any,
    });
  }
}
