/* eslint-disable @typescript-eslint/no-unused-vars */
import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { ReviewRatingController } from './review-rating.controller';
import { ReviewRatingService } from './review-rating.service';
import { JobReviewReminderService } from './job-review-reminder.service';
import { ReviewRating } from './schemas/review-rating.schema';
import { ReviewReminder } from './schemas/review-reminder.schema';
import { User } from '../admin/authentication/schemas/user.schema';
import { ConnectionService } from '../connection/connection.service';
import { Connection } from '../connection/schemas/connection.schema';
import { Role } from '../roles/schemas/role.schema';
import { UserRole } from '../roles/schemas/usersRole.schema';
import { NotificationModule } from '../notification/notification.module';
import { NotificationService } from '../notification/notification.service';
import { SendgridModule } from '../sendgrid/sendgrid.module';
import { Job } from '../job/schemas/job.schema';
import { JobsModule } from '../job/job.module';
import { UserJobs } from '../user-jobs/schemas/user-jobs.schema';

@Module({
  imports: [
    SequelizeModule.forFeature([
      ReviewRating,
      ReviewReminder,
      User,
      UserRole,
      Role,
      Connection,
      Job,
      UserJobs,
    ]),
    NotificationModule,
    SendgridModule,
    JobsModule,
  ],
  controllers: [ReviewRatingController],
  providers: [ReviewRatingService, JobReviewReminderService, ConnectionService],
  exports: [ReviewRatingService, JobReviewReminderService],
})
export class ReviewRatingModule {}
