import {
  IsString,
  IsInt,
  IsOptional,
  IsBoolean,
  Min,
  Max,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateReviewRatingDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  revieweRatingId: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  review?: string;

  @ApiProperty()
  @IsInt()
  @Min(1)
  @Max(5)
  rating: number;

  @ApiProperty()
  @IsInt()
  revieweeId: number;

  @ApiProperty()
  @IsInt()
  @IsOptional()
  jobId: number;

  @ApiProperty()
  @IsOptional()
  @IsInt()
  quotationId: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsBoolean()
  isVerified?: boolean;
}
export class InviteForReviewRatingDto {
  @ApiProperty()
  @IsInt()
  userId: number;

  @ApiProperty()
  @IsInt()
  @IsOptional()
  jobId: number;

  @ApiProperty()
  @IsOptional()
  @IsInt()
  quotationId: number;
}

export class UpdateReviewRatingDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  review?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(5)
  rating?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  status?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsBoolean()
  isVerified?: boolean;
}

export class CreateReplyDto {
  @ApiProperty()
  @IsString()
  review?: string;
}
