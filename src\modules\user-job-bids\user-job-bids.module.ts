import { Module } from '@nestjs/common';

import { SequelizeModule } from '@nestjs/sequelize';
import { User } from 'src/modules/admin/authentication/schemas/user.schema';
import { UserJobBids } from './schemas/user-job-bids.schema';
import { UserJobBidsController } from './user-jobs-bids.controller';
import { UserJobBidService } from './user-job-bids.servics';
import { Bids } from './schemas/bids.schema';
import { BidsService } from './bids.service';
import { Job } from '../job/schemas/job.schema';
import { JobsService } from '../job/job.service';
import { BiddingDetail } from '../job/schemas/bidding-details.schema';
import { Role } from '../roles/schemas/role.schema';
import { NotificationModule } from '../notification/notification.module';
import { Connection } from '../connection/schemas/connection.schema';
import { SendgridModule } from '../sendgrid/sendgrid.module';
import { JobsModule } from '../job/job.module';
import { AuthenticationModule } from '../admin/authentication/authentication.module';
import { UserManagementModule } from '../user-management/user-management.module';
import { Notification } from '../notification/schema/notification.schema';

@Module({
  imports: [
    SequelizeModule.forFeature([
      UserJobBids,
      User,
      Bids,
      Job,
      BiddingDetail,
      Role,
      Connection,
      Notification,
    ]),
    NotificationModule,
    SendgridModule,
    JobsModule,
    UserManagementModule,
    AuthenticationModule,
  ],
  providers: [UserJobBidService, BidsService, JobsService],
  controllers: [UserJobBidsController],
})
export class UserJobBidsModule {}
