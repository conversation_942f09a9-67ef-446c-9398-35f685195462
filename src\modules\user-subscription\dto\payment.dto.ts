import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  IsNumber,
  IsOptional,
  IsBoolean,
} from 'class-validator';

export class PaymentDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  orderId: string;

  // @ApiProperty()
  // @IsNotEmpty()
  // @IsString()
  // trackingId: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  amount: number;

  // @ApiProperty()
  // @IsOptional()
  // @IsNumber()
  // discountAmount: number;

  // @ApiProperty()
  // @IsOptional()
  // @IsString()
  // status: string;

  // @ApiProperty()
  // @IsString()
  // transactionType: string;
}
