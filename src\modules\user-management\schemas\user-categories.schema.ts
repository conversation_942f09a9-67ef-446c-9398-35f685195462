import { Column, Model, Table, ForeignKey } from 'sequelize-typescript';
import { Category } from 'src/modules/ category/schemas/category.schema';
import { User } from 'src/modules/admin/authentication/schemas/user.schema';

@Table
export class UserSubCategories extends Model<UserSubCategories> {
  @ForeignKey(() => User)
  @Column
  userId: number;

  @ForeignKey(() => Category)
  @Column
  categoryId: number;
}
