// upload.service.ts

import { HttpStatus, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as AWS from 'aws-sdk';
import { CustomException } from 'src/common/custom.exception';
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from 'src/common/messages';

@Injectable()
export class UploadService {
  private S3_ACCESS_KEY_ID: string;
  private S3_SECRET_ACCESS_KEY: string;
  private S3_BUCKET_NAME: string;
  private S3_REGION: string;
  private CDN_URL: string;
  private s3: AWS.S3;

  constructor(private configService: ConfigService) {
    AWS.config.update({ logger: console });
    // Initialize configuration values
    this.S3_ACCESS_KEY_ID = this.configService.get<string>('S3_ACCESS_KEY_ID');
    this.S3_SECRET_ACCESS_KEY = this.configService.get<string>(
      'S3_SECRET_ACCESS_KEY',
    );
    this.S3_BUCKET_NAME = this.configService.get<string>('S3_BUCKET_NAME');
    this.S3_REGION = this.configService.get<string>('S3_REGION');
    this.CDN_URL = this.configService.get<string>('CDN_URL');

    // Create an instance of the AWS S3 service
    this.s3 = new AWS.S3({
      accessKeyId: this.S3_ACCESS_KEY_ID,
      secretAccessKey: this.S3_SECRET_ACCESS_KEY,
      region: this.S3_REGION,
    });
  }

  uploadFile = async (file: Express.Multer.File) => {
    const { originalname, buffer, mimetype } = file;
    try {
      const fileName = await this.generateFileName(originalname);
      const params = {
        Bucket: this.S3_BUCKET_NAME,
        Key: fileName,
        Body: buffer,
        ContentType: mimetype,
      };
      console.log('parms', params);
      const s3Response = await this.s3.upload(params).promise();
      console.log('s3 response', s3Response);
      return {
        message: SUCCESS_MESSAGES.FILE_UPLOADED,
        key: s3Response.Key,
        fileUrl: `${this.CDN_URL}/${s3Response.Key}`,
      };
    } catch (err) {
      throw err;
    }
  };

  uploadMultipleImages = async (files: Express.Multer.File[]) => {
    if (!Array.isArray(files)) {
      throw new Error('Files parameter is not an array');
    }

    try {
      const uploadPromises = files.map(async (file, index) => {
        const { originalname, buffer, mimetype } = file;
        const fileName = await this.generateFileName(originalname, index);
        const params = {
          Bucket: this.S3_BUCKET_NAME,
          Key: fileName,
          Body: buffer,
          ContentType: mimetype,
        };
        const s3Response = await this.s3.upload(params).promise();
        return {
          key: s3Response.Key,
          fileUrl: `${this.CDN_URL}/${s3Response.Key}`,
        };
      });

      const uploadedFiles = await Promise.all(uploadPromises);
      console.log('uploadedFiles', uploadedFiles);

      return {
        message: SUCCESS_MESSAGES.FILE_UPLOADED,
        files: uploadedFiles,
      };
    } catch (err) {
      throw err;
    }
  };

  generateFileName = async (fileName: string, index?: number) => {
    try {
      const currentTimeInMillis = +Date.now();
      const randomNumber = Math.floor(
        Math.random() * 984 * Number(currentTimeInMillis) + (index ?? 1),
      );
      const rawFileName = fileName.replace(/\s/g, ''); // Remove whitespace
      const [name, ext] = rawFileName.split('.'); // Split file name and extension

      // Replace all non-alphanumeric characters with underscore
      const cleanName = name.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase();

      const generatedFileName = `${cleanName}_${currentTimeInMillis}_${randomNumber}.${ext}`;
      return generatedFileName.toLowerCase();
    } catch (err) {
      throw err;
    }
  };

  getFileByKey = async (key: string) => {
    try {
      const params = {
        Bucket: this.S3_BUCKET_NAME,
        Key: key,
      };
      await this.s3.headObject(params).promise();
      return {
        message: SUCCESS_MESSAGES.FILE_UPLOADED,
        fileUrl: `${this.CDN_URL}/${key}`,
      };
    } catch (err) {
      if (err.code === 'NotFound') {
        throw new CustomException(
          ERROR_MESSAGES.KEY_NOT_FOUND,
          HttpStatus.NOT_FOUND,
        );
      }
      throw err;
    }
  };
}
