import { Module } from '@nestjs/common';
import { SubscriptionService } from './subscription.service';
import { SubscriptionController } from './subscription.controller';
// import { Subscription } from './schemas/subscription.schema';
import { JwtService } from '@nestjs/jwt';
import { SequelizeModule } from '@nestjs/sequelize';
import { Package } from './schemas/package.schema';
// import { AdminGuard } from 'src/guards/admin/admin.guard';
// import { CommonService } from 'src/common/common.service';
// import { AdminService } from '../admin/admin.service';
import { User } from '../admin/authentication/schemas/user.schema';
import { Role } from '../roles/schemas/role.schema';
import { PackageDetails } from './schemas/packagePermission.schema';
import { SendgridModule } from '../sendgrid/sendgrid.module';

@Module({
  imports: [
    SequelizeModule.forFeature([Package, User, Role, PackageDetails]),
    SendgridModule,
  ],
  controllers: [SubscriptionController],
  providers: [SubscriptionService, JwtService],
  exports: [SubscriptionService, JwtService],
})
export class SubscriptionModule {}
