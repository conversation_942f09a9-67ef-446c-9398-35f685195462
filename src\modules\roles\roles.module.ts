import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { User } from '../admin/authentication/schemas/user.schema';
import { RoleController } from './roles.controller';
import { RoleService } from './roles.service';
import { Role } from './schemas/role.schema';
import { UserRole } from './schemas/usersRole.schema';
import { RoleSeedService } from './roles-seeder.service';

@Module({
  imports: [SequelizeModule.forFeature([Role, User, UserRole])],
  providers: [RoleService, RoleSeedService],
  controllers: [RoleController],
})
export class RoleModule {}
