import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { User } from '../admin/authentication/schemas/user.schema';
import { NotificationService } from './notification.service';
import { NotificationController } from './notification.controller';
import { Notification } from './schema/notification.schema';
import { UserDeviceToken } from './schema/user-device-token.schema';
import { FirebaseService } from '../firebase/firebase.service';
import { ReviewRating } from '../review-rating/schemas/review-rating.schema';

@Module({
  imports: [
    SequelizeModule.forFeature([
      User,
      Notification,
      UserDeviceToken,
      ReviewRating,
    ]),
  ],
  providers: [NotificationService, FirebaseService],
  controllers: [NotificationController],
  exports: [
    NotificationService,
    // SequelizeModule.forFeature([UserDeviceToken]), // Export UserDeviceToken
  ],
})
export class NotificationModule {}
