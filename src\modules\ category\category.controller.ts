import { Controller, Get, Query } from '@nestjs/common';

import { ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';

import { CategoryService } from './category.service';
import { Category } from './schemas/category.schema';
import { User } from '../admin/authentication/schemas/user.schema';
import { Sequelize } from 'sequelize';

@ApiTags('Categories')
@Controller('categories')
export class CategoryController {
  constructor(private readonly categoryService: CategoryService) {}

  @ApiOperation({ summary: 'Fetch category list' })
  @ApiResponse({ status: 200, description: 'Category fetched successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiQuery({ name: 'parentIds', required: false, type: [Number] })
  @ApiQuery({ name: 'userId', required: false, type: Number })
  @ApiQuery({ name: 'roleId', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: String })
  @ApiQuery({ name: 'page', required: false, type: String })
  @Get()
  async getCategories(
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Query('roleId') roleId = null,
    @Query('userId') userId = null,
    @Query('parentIds') parentIds = [],
  ): Promise<{ categories: Category[]; total: number }> {
    const query: any = { parentId: null, ...(!userId && { roleId }) };
    if (parentIds.length > 0) {
      query.parentId = parentIds;
    }
    const whereQuery = {
      where: query,
      ...(userId && {
        include: [
          {
            model: User,
            as: 'userCategories',
            where: { id: userId },
            through: { attributes: [] },
            required: true,
            attributes: [],
          },
        ],
      }),

      attributes: [
        'id',
        'name',
        'parentId',
        'roleId',
        'createdAt',
        [
          Sequelize.literal(
            `(SELECT COUNT(*) FROM "Categories" AS subcategory WHERE subcategory."parentId" = "Category"."id") > 0`,
          ),
          'subCategoryExist',
        ],
      ],
      order: [['name', 'Asc']],
      offset: (page - 1) * limit,
      limit,
    };
    return this.categoryService.getCategoriesWithCount(whereQuery);
  }
  @ApiOperation({ summary: 'Fetch sub category list' })
  @ApiResponse({
    status: 200,
    description: 'Sub category fetched successfully',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiQuery({ name: 'parentIds', required: true, type: [Number] })
  @ApiQuery({ name: 'userId', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: String })
  @ApiQuery({ name: 'page', required: false, type: String })
  @Get('sub-categories')
  async getSubCategories(
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Query('parentIds') parentIds = [],
    @Query('userId') userId = null,
  ): Promise<{ categories: Category[]; total: number }> {
    const query: any = { id: parentIds };
    return this.categoryService.getSubCategoriesWithCount(
      query,
      page,
      limit,
      userId,
    );
  }
}
