import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';

import { Discussion } from './schemas/discussion.schema';
import { RequestQuotation } from '../service/schemas/quotation-request.schema';
import { Op } from 'sequelize';
import { User } from '../admin/authentication/schemas/user.schema';
import { notificationType } from 'src/common/messages';
import { NotificationService } from '../notification/notification.service';
import { I18nContext, I18nService } from 'nestjs-i18n';
import { Role } from '../roles/schemas/role.schema';
import { SendgridService } from '../sendgrid/sendgrid.service';
import { Constants } from 'src/common/constants';
import { Email } from 'src/common/email-template';
import { NotificationPreference } from '../user-management/schemas/notification-preferences.schema';
@Injectable()
export class DiscussionService {
  constructor(
    @InjectModel(Discussion)
    private discussionModel: typeof Discussion,
    @InjectModel(User)
    private userModel: typeof User,
    @InjectModel(RequestQuotation)
    private quotationModel: typeof RequestQuotation,
    private readonly notificationService: NotificationService,
    private readonly i18n: I18nService,
    private readonly sendgridService: SendgridService,
  ) {}

  async create(createDiscussionDto: any): Promise<Discussion> {
    if (createDiscussionDto.requestQuotationId) {
      const quotation = await this.quotationModel.findOne({
        where: {
          id: createDiscussionDto.requestQuotationId,
          [Op.or]: [
            { userId: createDiscussionDto.senderId },
            { requestedTo: createDiscussionDto.senderId },
          ],
        },
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id'],
          },
          {
            model: User,
            as: 'requested',
            attributes: ['id'],
          },
        ],
      });
      if (!quotation) {
        const message = this.i18n.t('test.errorMessage.NOT_FOUND', {
          lang: I18nContext.current().lang,
          args: { data: 'Quotation' },
        });
        throw new BadRequestException(message);
      }
      createDiscussionDto.title = quotation.title;
      if (createDiscussionDto.senderId === quotation.user.id) {
        createDiscussionDto.receiverId = quotation.requested.id;
      } else if (createDiscussionDto.senderId === quotation.requested.id) {
        createDiscussionDto.receiverId = quotation.user.id;
      }

      const userDetails = await this.userModel.findByPk(quotation.user.id, {
        include: [
          {
            model: NotificationPreference,
            as: 'notificationPreference',
          },
        ],
      });
      // console.log('*********', userDetails);
      if (userDetails.email && userDetails.notificationPreference?.email) {
        await this.sendgridService.sendEmail(
          userDetails.email,
          'New Message Received',
          Email.getTemplate({
            title: 'New Message Alert',
            userName: `${userDetails?.firstName} ${userDetails?.lastName}`,
            children: `
          <p>
           You’ve received a new message. Log in to read it.
        </p>
          `,
          }),
        );
      }
    }

    const user = await this.userModel.findOne({
      where: {
        id: createDiscussionDto.senderId,
      },
      include: [
        {
          model: Role,
          as: 'roles',
          through: { attributes: [] },
          attributes: ['id', 'roleName'],
          required: false,
        },
      ],
      attributes: ['id', 'firstName', 'lastName', 'profilePicture'],
    });
    console.log('createDiscussionDto:::::::', createDiscussionDto);
    const data = {
      type: notificationType.NEW_MESSAGE,
      receiverId: createDiscussionDto.receiverId,
      senderId: createDiscussionDto.senderId,
      additional: {
        type: createDiscussionDto.type,
        jobBidId: createDiscussionDto.userJobBidId,
        quotationId: createDiscussionDto.requestQuotationId,
        firstName: user.firstName,
        lastName: user.lastName,
        profilePicture: user.profilePicture,
        role: user.roles[0].roleName,
        title: createDiscussionDto.title,
        jobId: createDiscussionDto.jobId,
      },
    };
    await this.notificationService.sendNotification(data);

    const discussion = await this.discussionModel.create(createDiscussionDto);
    return discussion;
  }
  async get(query: any): Promise<Discussion> {
    return this.discussionModel.findOne({ where: query });
  }

  async getWithCount(
    query: any,
    page: number,
    limit: number,
  ): Promise<{ discussion: Discussion[]; total: number }> {
    const offset = (page - 1) * limit;

    const { count, rows } = await this.discussionModel.findAndCountAll({
      where: query,
      include: [
        {
          model: User,
          as: 'sender',
          attributes: ['id', 'firstName', 'lastName', 'profilePicture'],
        },
        {
          model: User,
          as: 'receiver',
          attributes: ['id', 'firstName', 'lastName', 'profilePicture'],
        },
      ],
      order: [['createdAt', 'ASC']],
      offset,
      limit,
    });

    return { discussion: rows, total: count };
  }
}
