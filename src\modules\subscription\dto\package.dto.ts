import {
  IsString,
  IsEnum,
  IsNumber,
  IsArray,
  IsOptional,
  IsIn,
  Validate,
  ValidationArguments,
  ValidatorConstraintInterface,
  ValidatorConstraint,
} from 'class-validator';
import { PackageType } from '../schemas/package.schema';
import { ApiProperty } from '@nestjs/swagger';

// @ValidatorConstraint({ name: 'IsValidRoleCount', async: false })
// class IsValidRoleCount implements ValidatorConstraintInterface {
//   validate(roles: string[], args: ValidationArguments) {
//     const title = (args.object as CreatePackageDto).title;

//     if (title === 'dual') {
//       return roles.length <= 2; // Allow up to 2 roles
//     }

//     return roles.length === 1; // Require exactly 1 role for other titles
//   }

//   defaultMessage(args: ValidationArguments) {
//     const title = (args.object as CreatePackageDto).title;
//     return title === 'dual'
//       ? 'If title is "dual", up to 2 roles are allowed.'
//       : 'Exactly 1 role is required.';
//   }
// }
export class CreatePackageDto {
  @ApiProperty({ enum: ['free', 'basic', 'premium', 'default'] })
  @IsString()
  @IsIn(['free', 'basic', 'premium', 'default'])
  title: string;

  @ApiProperty()
  @IsEnum(PackageType)
  packageType: PackageType;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  price: number;

  @ApiProperty({ type: String, required: false })
  @IsString()
  role: string;
}

export class UpdatePackageStatusDto {
  @ApiProperty({
    enum: ['active', 'inactive'],
    description: 'The status of the quotation',
  })
  @IsEnum(['active', 'inactive'], {
    message: 'Status must be either "active " or "inactive".',
  })
  status: string;
}
