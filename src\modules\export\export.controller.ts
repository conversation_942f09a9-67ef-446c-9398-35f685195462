import { Controller, Get, Query, Res, UseGuards } from '@nestjs/common';
import { Response } from 'express';
import { ExportService } from './export.service'; // Adjust the path as needed
import * as fs from 'fs'; // Import fs here
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiOperation,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { AdminPermissions } from 'src/common/decorators/permissions.decorator';
import { AdminGuard } from 'src/guards/admin/admin.guard';
import { Modules } from 'src/common/utils/enum';

@ApiTags('Export')
@Controller('export')
export class ExportController {
  constructor(private readonly exportService: ExportService) {}

  // @UseGuards(AdminGuard)
  // @AdminPermissions(Modules.USER_MANAGEMENT)
  // @ApiBearerAuth()
  // @Get('users')
  // @ApiOperation({ summary: 'Export users - all the users List ' })
  // @ApiQuery({
  //   name: 'format',
  //   enum: ['csv', 'excel'],
  //   description: 'Format of the file to export',
  //   example: 'csv'
  // })
  // @ApiResponse({ status: 200, description: 'File exported successfully' })
  // @ApiBadRequestResponse({ description: 'Invalid query parameter' })
  // @ApiResponse({ status: 500, description: 'Internal server error' })
  // async exportUsers(@Query('format') format: 'csv' | 'excel', @Res() res: Response) {
  //   try {
  //     const filePath = await this.exportService.exportUsers(format);
  //     console.log("filePath::::::", filePath)
  //     res.download(filePath, (err) => {
  //       if (err) {
  //         console.error(err);
  //         res.status(500).send('Error generating file');
  //       } else {
  //         // Clean up files after sending
  //         fs.unlinkSync(filePath); // Delete the file after sending
  //       }
  //     });
  //   } catch (err) {
  //     console.error('Error exporting files:', err);
  //     res.status(500).send('Error exporting files');
  //   }
  // }

  @UseGuards(AdminGuard)
  @ApiBearerAuth()
  @Get('users')
  @ApiOperation({ summary: 'Export users - all the users List' })
  @ApiQuery({
    name: 'format',
    enum: ['csv', 'excel'],
    description: 'Format of the file to export',
    example: 'csv',
  })
  @ApiResponse({ status: 200, description: 'File exported successfully' })
  @ApiBadRequestResponse({ description: 'Invalid query parameter' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async exportUsers(
    @Query('format') format: 'csv' | 'excel',
    @Res() res: Response,
  ) {
    try {
      const filePath = await this.exportService.exportUsers(format);

      // Set the content type and attachment header
      res.setHeader('Content-Type', 'application/octet-stream');
      res.setHeader(
        'Content-Disposition',
        `attachment; filename=${filePath.split('/').pop()}`,
      );

      // Send the file for download
      res.download(filePath, (err) => {
        if (err) {
          console.error('Error during file download:', err);
          res.status(500).send('Error generating file');
        } else {
          // Clean up files after sending
          fs.unlinkSync(filePath); // Delete the file after sending
        }
      });
    } catch (err) {
      console.error('Error exporting files:', err);
      res.status(500).send('Error exporting files');
    }
  }
}
