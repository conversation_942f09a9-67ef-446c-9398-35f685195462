/* eslint-disable prettier/prettier */
import {
  Controller,
  Post,
  Get,
  Query,
  Param,
  Body,
  UseGuards,
  Request,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { PaymentService } from './payment.service';
import { JWTGuard } from 'src/guards/common/jwt.guard';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { PaymentDto } from '../user-subscription/dto/payment.dto';

@ApiTags('Payment')
@Controller('payment')
export class PaymentController {
  constructor(private paymentService: PaymentService) {}

  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @Post('initiate')
  async initiatePayment(
    @Request() req,
    // @Param('orderId') orderId: number,
    @Body() paymentDto: PaymentDto,
  ) {
    return await this.paymentService.initiatePayment(paymentDto);
  }

  // @Get('status')
  // async getPaymentStatus(@Query('transactionId') transactionId: string) {
  //   return await this.paymentService.checkPaymentStatus(transactionId);
  // }

  @Get('/status/:id')
  async checkStatus(@Param('id') id: string) {
    try {
      const status = await this.paymentService.checkStatus(id);
      return {
        success: true,
        message: 'Payment status retrieved successfully',
        data: status,
      };
    } catch (error) {
      throw new HttpException(
        `Failed to check payment status: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
