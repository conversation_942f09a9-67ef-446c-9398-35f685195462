import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayUnique,
  IsArray,
  Length,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsNumber,
  ValidateNested,
} from 'class-validator';

import { WeekDays } from '../utils/enum';

class SkillDto {
  @ApiProperty({
    description: `enter skill id`,
  })
  @IsInt()
  value: number;

  @ApiProperty({ example: 'enter skill name' })
  @IsString()
  label: string;
}
class TagDto {
  @ApiProperty({
    description: `enter tag id`,
  })
  @IsInt()
  value: number;

  @ApiProperty({ example: 'enter tag name' })
  @IsString()
  label: string;
}

export class RegisterUserDTO {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  firstName: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  lastName: string;

  @ApiProperty()
  @IsOptional()
  @IsInt()
  roleId: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  identifier?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  type?: string;

  @ApiProperty({ type: [Number] })
  @IsArray()
  @IsInt({ each: true })
  @ArrayUnique()
  @IsOptional()
  category?: number[];

  @ApiProperty({ type: [Number] })
  @IsArray()
  @IsInt({ each: true })
  @ArrayUnique()
  @IsOptional()
  subCategory?: number[];

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  appLanguage: string;
}

export class UpdateUserDTO {
  @IsOptional()
  @IsInt()
  id?: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  firstName?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  lastName?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  profilePicture?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  bio?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  email?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  phoneNumber?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  secondaryPhoneNumber?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  address1?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  address2?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  city?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  state?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  firmName: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  gstIn?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  type?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  officeNumber?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  website?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  pincode?: string;

  @ApiProperty({ type: [Number] })
  @IsArray()
  @IsInt({ each: true })
  @ArrayUnique()
  @IsOptional()
  category?: number[];

  @ApiProperty({ type: [Number] })
  @IsArray()
  @IsInt({ each: true })
  @ArrayUnique()
  @IsOptional()
  subCategory?: number[];

  @ApiProperty({ example: '2024/01/02' })
  // @IsDate()
  @IsOptional()
  dateOfBirth?: string;

  @ApiProperty({ example: 'male' })
  @IsString()
  @IsOptional()
  gender?: string;

  @ApiProperty({ example: [WeekDays.Monday] })
  // @IsEnum(WeekDays)
  @IsOptional()
  workingDays: string[];

  @ApiProperty({ example: '12:00' })
  @IsString()
  @IsOptional()
  openingTime?: string;

  @ApiProperty({ example: '10:00' })
  @IsString()
  @IsOptional()
  closingTime?: string;

  @ApiProperty({ example: 100 })
  @IsNumber()
  @IsOptional()
  ratePerDay?: number;

  @ApiProperty({ type: () => [SkillDto] })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => SkillDto)
  skills: SkillDto[];

  @ApiProperty({ type: () => [TagDto] })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => TagDto)
  tags: TagDto[];

  @ApiProperty({
    description: 'enter geometryLocation',
    example: ['24.44444', '22.4444'],
    required: false,
  })
  @IsArray()
  @IsOptional()
  geolocation: string[];
}

export class VerifyOtpDTO {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  identifier: string; // This can be either email or phone number

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  otp: string;
}

export class SendOtpDto {
  @ApiProperty({
    example: '<EMAIL> or 9874561238',
    description: 'The emailor number of the user to send OTP to',
  })
  @IsString()
  @IsNotEmpty()
  identifier: string; // This can be either email or phone number
}

export class LoginOtpDto {
  @ApiProperty({
    example: '<EMAIL> or 9874561238',
    description: 'The email or number of the user to send OTP to',
  })
  @IsString()
  @IsNotEmpty()
  identifier: string; // This can be either email or phone number

  @ApiProperty({
    example: '123456',
    description: 'The 6-digit OTP sent to the user',
  })
  @IsString()
  @Length(6, 6)
  @IsNotEmpty()
  otp: string;
}

export class GlobalSearchDto {
  @ApiProperty({
    description: 'Enter geolocation as an array of coordinates',
    required: false,
  })
  @IsArray()
  @IsOptional()
  geolocation?: [number];

  @ApiProperty({ description: 'search', required: false })
  search?: string;

  @ApiProperty({ description: 'Radius in kilometers', required: false })
  radius?: number;
}
