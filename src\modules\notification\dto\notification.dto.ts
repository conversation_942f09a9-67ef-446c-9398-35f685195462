import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsOptional,
  IsString,
  IsBoolean,
  IsN<PERSON>ber,
  IsJSON,
} from 'class-validator';

export class CreateNotificationDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  title: string;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  isRead: boolean = false;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  receiverId: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  senderId: number;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  isSystemGenerated: boolean = false;

  @ApiProperty()
  @IsOptional()
  @IsString()
  message: string;

  @ApiProperty()
  @IsOptional()
  @IsJSON()
  additional: any;

  @ApiProperty()
  @IsOptional()
  @IsString()
  type: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  appName: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  prefrenceType: string;
}

export class createUserDeviceTokenDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  Token: string;
}
