import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { UserBlock } from './schemas/user-blocks.schema';
import { User } from '../admin/authentication/schemas/user.schema';
import { Role } from '../roles/schemas/role.schema';
import { Category } from '../ category/schemas/category.schema';

@Injectable()
export class UserBlockService {
  constructor(
    @InjectModel(UserBlock)
    private userBlockModel: typeof UserBlock,
  ) {}

  async blockUnblockUser(
    blockerId: number,
    blockedId: number,
  ): Promise<{ message: string }> {
    try {
      const userAlreadyBlocked = await this.isUserBlocked(blockerId, blockedId);
      if (userAlreadyBlocked) {
        const res = await this.unblockUser(userAlreadyBlocked);
        return res;
      }
      await this.userBlockModel.create({
        blockerId,
        blockedId,
      });
      return { message: 'User blocked successfully' };
    } catch (error) {
      throw error;
    }
  }

  async unblockUser(userBlock: any): Promise<{ message: string }> {
    await userBlock.destroy();
    return { message: 'User unblocked successfully' };
  }
  async isUserBlocked(
    currentUserId: number,
    targetUserId: number,
  ): Promise<UserBlock> {
    const block = await this.userBlockModel.findOne({
      where: {
        blockerId: currentUserId,
        blockedId: targetUserId,
      },
    });
    return block;
  }

  async getBlockedUsers(
    loggedInUserId: number,
    limit: number,
    offset: number,
  ): Promise<{ userBlock: UserBlock[]; total: number }> {
    const attributes: any = ['id', 'firstName', 'lastName', 'profilePicture'];

    const { rows, count } = await this.userBlockModel.findAndCountAll({
      where: { blockerId: loggedInUserId },
      attributes: ['id', 'createdAt'],
      include: [
        {
          model: User,
          as: 'blockedUser',
          attributes: attributes,
          include: [
            {
              model: Role,
              as: 'roles',
              through: { attributes: [] },
              attributes: ['id', 'roleName'],
              required: false,
            },
            {
              model: Category,
              through: {
                attributes: [],
              },
              as: 'categories',
            },
            {
              model: Category,
              through: {
                attributes: [],
              },
              as: 'subCategories',
            },
          ],
          required: true,
        },
      ],
      limit,
      offset,
      order: [['createdAt', 'DESC']],
    });
    return { userBlock: rows, total: count };
  }
}
