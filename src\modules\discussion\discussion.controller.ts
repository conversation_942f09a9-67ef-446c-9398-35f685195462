import {
  Controller,
  Post,
  Body,
  Get,
  Query,
  Request,
  UseGuards,
  BadRequestException,
} from '@nestjs/common';

import {
  ApiBearerAuth,
  ApiOperation,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { Op } from 'sequelize';
import { DiscussionService } from './discussion.service';
import { Discussion } from './schemas/discussion.schema';
import { CreateDiscussionDto } from './dto/create-discussion.dto';
import { JWTGuard } from 'src/guards/common/jwt.guard';
import { DiscussionType } from './utils/enum';
import { UserJobBidService } from '../user-job-bids/user-job-bids.servics';
import { Job } from '../job/schemas/job.schema';
import { User } from '../admin/authentication/schemas/user.schema';
import { I18nContext, I18nService } from 'nestjs-i18n';
import { SendgridService } from '../sendgrid/sendgrid.service';
import { Email } from 'src/common/email-template';
import { InjectModel } from '@nestjs/sequelize';
import { notificationMessage } from 'src/common/messages';
import { NotificationPreference } from '../user-management/schemas/notification-preferences.schema';

@ApiTags('Discussion')
@Controller('discussion')
export class DiscussionController {
  constructor(
    private readonly discussionService: DiscussionService,
    private readonly userJobBidService: UserJobBidService,
    private readonly i18n: I18nService,
    private readonly sendgridService: SendgridService,
    @InjectModel(User)
    private userModel: typeof User,
  ) {}

  @Post()
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a discussion message' })
  @ApiResponse({ status: 201, description: 'Message sent successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async sendMessage(
    @Request() req,
    @Body() createDiscussionDto: CreateDiscussionDto,
  ): Promise<Discussion> {
    let receiverId: number;
    if (createDiscussionDto.type === DiscussionType.JOB_BID) {
      const userJobBid = await this.userJobBidService.getOne({
        where: {
          id: createDiscussionDto.userJobBidId,
        },
        attributes: ['id'],
        include: [
          {
            model: Job,
            as: 'job',
            attributes: ['id'],
            include: [
              {
                model: User,
                as: 'createdBy',
                attributes: ['id'],
              },
            ],
          },
          { model: User, as: 'user', attributes: ['id'] },
        ],
      });
      if (!userJobBid) {
        const message = this.i18n.t('test.errorMessage.NOT_FOUND', {
          lang: I18nContext.current().lang,
          args: { data: 'User job bid' },
        });
        throw new BadRequestException(message);
      }
      createDiscussionDto.title = userJobBid.job.title;
      createDiscussionDto.jobId = userJobBid.job.id;
      if (req.user.id === userJobBid.user.id) {
        receiverId = userJobBid.job.createdBy.id;
      } else if (req.user.id === userJobBid.job.createdBy.id) {
        receiverId = userJobBid.user.id;
      }
      await this.userModel.findByPk(userJobBid.user.id, {
        include: [
          {
            model: NotificationPreference,
            as: 'notificationPreference',
          },
        ],
      });
      // if (userDetails.email && userDetails.notificationPreference?.email) {
      //   await this.sendgridService.sendEmail(
      //     userDetails.email,
      //     'Message Notification',
      //     Email.getTemplate({
      //       title: 'New Message Alert',
      //       userName: `${userDetails?.firstName} ${userDetails?.lastName}`,
      //       children: `
      //     <p>
      //      You’ve received a new message. Log in to read it.
      //   </p>
      //     `,
      //     }),
      //   );
      // }
    }

    return this.discussionService.create({
      ...createDiscussionDto,
      senderId: req.user.id,
      receiverId: receiverId,
    });
  }

  @Get()
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Fetch discussions list' })
  @ApiResponse({ status: 200, description: 'discussions fetched successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiQuery({ name: 'userJobBidId', required: false, type: Number })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'limit', required: false, type: String })
  @ApiQuery({ name: 'page', required: false, type: String })
  async getMessages(
    @Query('userJobBidId') userJobBidId = null,
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Query('search') search = null,
  ): Promise<{ discussion: Discussion[]; total: number }> {
    if (!userJobBidId) {
      return { discussion: [], total: 0 };
    }
    const query = {
      userJobBidId: userJobBidId,
      type: DiscussionType.JOB_BID,
      ...(search && {
        name: { [Op.iLike]: `%${search}%` },
      }),
    };
    return this.discussionService.getWithCount(query, page, limit);
  }
}
