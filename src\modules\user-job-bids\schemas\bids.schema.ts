import {
  Column,
  Model,
  Table,
  ForeignKey,
  DataType,
  BelongsTo,
  CreatedAt,
  UpdatedAt,
} from 'sequelize-typescript';
import { UserJobBids } from './user-job-bids.schema';

@Table({ timestamps: true })
export class Bids extends Model<Bids> {
  @Column({
    type: DataType.STRING,
  })
  firstName: string;

  @Column({
    type: DataType.STRING,
  })
  lastName: string;

  @Column({
    type: DataType.STRING,
  })
  contactNumber: string;

  @Column({
    type: DataType.STRING,
  })
  email: string;

  @Column({
    type: DataType.DECIMAL(10, 2),
    get() {
      const value = this.getDataValue('amount');
      return value ? parseFloat(value) : null;
    },
  })
  amount: number;

  @Column({
    type: DataType.STRING,
  })
  workCompletionDurationInDays: string;

  @Column({
    type: DataType.STRING,
  })
  description: string;

  @ForeignKey(() => UserJobBids)
  @Column
  userJobBidId: number;

  @BelongsTo(() => UserJobBids, 'userJobBidId')
  userJobBid: UserJobBids;

  @CreatedAt
  @Column
  createdAt: Date;

  @UpdatedAt
  @Column
  updatedAt: Date;
}
