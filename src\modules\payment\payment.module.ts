import { Module } from '@nestjs/common';
import { PaymentController } from './payment.controller';
import { Order } from '../user-subscription/schemas/order.schema';
import { SequelizeModule } from '@nestjs/sequelize';
import { PaymentService } from './payment.service';
import { Transaction } from '../user-subscription/schemas/transaction.schema';
import { User } from '../admin/authentication/schemas/user.schema';
import { UserSubscription } from '../user-subscription/schemas/user-subscription.schema';
import { SendgridModule } from '../sendgrid/sendgrid.module';
import { NotificationModule } from '../notification/notification.module';
import { SmsModule } from '../sms/sms.module';

@Module({
  imports: [
    SequelizeModule.forFeature([
      Order,
      Transaction,
      Order,
      UserSubscription,
      User,
    ]),
    SendgridModule,
    SmsModule,
    NotificationModule,
  ],
  controllers: [PaymentController],
  providers: [PaymentService],
  exports: [PaymentService],
})
export class PaymentModule {}
