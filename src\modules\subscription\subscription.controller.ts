import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import { SubscriptionService } from './subscription.service';
// import { CreateSubscriptionDto } from './dto/subscription.dto';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { CreatePackageDto, UpdatePackageStatusDto } from './dto/package.dto';
import { AdminGuard } from 'src/guards/admin/admin.guard';
import { JWTGuard } from 'src/guards/common/jwt.guard';
import { PackageType } from './schemas/package.schema';
// import { SuperAdminAuthGuard } from 'lib/guards/super-admin-guard';

@ApiTags('Packages')
@Controller('packages')
export class SubscriptionController {
  constructor(private readonly subscriptionService: SubscriptionService) {}

  // @UseGuards(SuperAdminAuthGuard )
  // @ApiBearerAuth()
  // @Post()
  // @ApiOperation({ summary: 'Subscription - Create a new subscription' })
  // @ApiBody({
  //   schema: {
  //     type: 'object',
  //     properties: {
  //       title: { type: 'string', example: '2024-07-02' },
  //       packageType: { type: 'string', enum: ['Monthly', 'Quarterly', 'Yearly'], example: 'Yearly' },
  //       price: { type: 'number', example: 100 },
  //       discount: { type: 'number', example: 10 },
  //       roles: { type: 'array', items: { type: 'string' }, example: ['slot1', 'slot2'] },
  //     },
  //   },
  //   description: 'Subscription data',
  // })
  // @ApiResponse({ status: 200, description: 'The subscription has been successfully created.' })
  // createSubscription(@Body() createSubscriptionData: CreateSubscriptionDto) {
  //   try {
  //     return this.subscriptionService.createSubscription(createSubscriptionData);
  //   } catch (error) {
  //     return {
  //       success: false,
  //       error: error.message || 'Internal server error',
  //     };
  //   }

  // }

  // @UseGuards(SuperAdminAuthGuard )
  // @ApiBearerAuth()
  // @Get()
  // @ApiOperation({ summary: 'Subscription - Get all subscriptions with pagination' })
  // @ApiResponse({
  //   status: 200,
  //   content: {
  //     'application/json': {
  //       schema: {
  //         type: 'array',
  //         items: {
  //           type: 'object',
  //           properties: {
  //             id: { type: 'number' },
  //             title: { type: 'string' },
  //             package_type: { type: 'string' },
  //             price: { type: 'number' },
  //             discount: { type: 'number' },
  //             roles: { type: 'array' },
  //           },
  //         },
  //       },
  //       example: [
  //         {
  //           status: true,
  //           created_at: "2024-07-08T00:30:51.682Z",
  //           updated_at: "2024-07-08T00:30:51.682Z",
  //           id: 3,
  //           title: "Premium",
  //           package_type: "Monthly",
  //           price: 29.99,
  //           discount: 2,
  //           roles: [
  //             "role1"
  //           ]
  //         },
  //         {
  //           status: true,
  //           id: 1,
  //           title: "Premium Suscription",
  //           created_at: "2024-07-08T00:30:51.682Z",
  //           updated_at: "2024-07-08T00:30:51.682Z",
  //           package_type: "Monthly",
  //           price: 1000,
  //           discount: 2,
  //           roles: [
  //             "role1"
  //           ]
  //         },
  //       ],
  //     },
  //   },
  // })
  // @ApiQuery({ name: 'limit', required: false, type: Number })
  // @ApiQuery({ name: 'page', required: false, type: Number })
  // findAllSubscription(@Query('page') page = 1, @Query('limit') limit = 10) {
  //   try {
  //     return this.subscriptionService.findAllSubscription(page, limit);
  //   } catch (error) {
  //     return {
  //       success: false,
  //       error: error.message || 'Internal server error',
  //     };
  //   }
  // }

  // @UseGuards(SuperAdminAuthGuard )
  // @ApiBearerAuth()
  // @Get(':id')
  // @ApiOperation({ summary: 'Subscription - Get a subscription by ID' })
  // @ApiParam({ name: 'id', required: true, type: Number })
  // @ApiResponse({
  //   status: 200,
  //   content: {
  //     'application/json': {
  //       schema: {
  //         type: 'array',
  //         items: {
  //           type: 'object',
  //           properties: {
  //             id: { type: 'number' },
  //             title: { type: 'string' },
  //             package_type: { type: 'string' },
  //             price: { type: 'number' },
  //             discount: { type: 'number' },
  //             roles: { type: 'array' },
  //           },
  //         },
  //       },
  //       example:
  //         {
  //           status: true,
  //           created_at: "2024-07-08T00:30:51.682Z",
  //           updated_at: "2024-07-08T00:30:51.682Z",
  //           id: 3,
  //           title: "Premium",
  //           package_type: "Monthly",
  //           price: 29.99,
  //           discount: 2,
  //           roles: [
  //             "role1"
  //           ]
  //         }
  //     },
  //   },
  // })
  // findOneSubscription(@Param('id') id: string) {
  //   try {
  //     return this.subscriptionService.findOneSubscription(+id);
  //   } catch (error) {
  //     return {
  //       success: false,
  //       error: error.message || 'Internal server error',
  //     };
  //   }
  // }

  // @UseGuards(SuperAdminAuthGuard )
  // @ApiBearerAuth()
  // @Patch(':id')
  // @ApiOperation({ summary: 'Subscription - Update a subscription by ID' })
  // @ApiBody({
  //   schema: {
  //     type: 'object',
  //     properties: {
  //       title: { type: 'string', example: '2024-07-02' },
  //       packageType: { type: 'string', enum: ['Monthly', 'Quarterly', 'Yearly'], example: 'Yearly' },
  //       price: { type: 'number', example: 100 },
  //       discount: { type: 'number', example: 10 },
  //       roles: { type: 'array', items: { type: 'string' }, example: ['slot1', 'slot2'] },
  //     },
  //   },
  //   description: 'Subscription data',
  // })
  // @ApiParam({ name: 'id', required: true, type: Number })
  // @ApiResponse({ status: 200, description: 'The subscription has been successfully updated.' })
  // updateSubscription(@Param('id') id: string, @Body() updateSubscriptionData: CreateSubscriptionDto) {
  //   try {
  //     return this.subscriptionService.updateSubscription(+id, updateSubscriptionData);
  //   } catch (error) {
  //     return {
  //       success: false,
  //       error: error.message || 'Internal server error',
  //     };
  //   }
  // }

  // // @UseGuards(SuperAdminAuthGuard )
  // @ApiBearerAuth()
  // @Delete(':id')
  // @ApiOperation({ summary: 'Subscription - Delete a subscription by ID' })
  // @ApiParam({ name: 'id', required: true, type: Number })
  // @ApiResponse({ status: 200, description: 'The subscription has been successfully deleted.' })
  // removeSubscription(@Param('id') id: string) {
  //   try {
  //     return this.subscriptionService.removeSubscription(+id);
  //   } catch (error) {
  //     return {
  //       success: false,
  //       error: error.message || 'Internal server error',
  //     };
  //   }
  // }

  // // @UseGuards(SuperAdminAuthGuard )
  // // @ApiBearerAuth()
  // // @Patch('/status/:id')
  // // @ApiOperation({ summary: 'Subscription - Update subscription status by ID' })
  // // @ApiBody({
  // //   schema: {
  // //     type: 'object',
  // //     properties: {
  // //       status: { type: 'boolean', example: false },
  // //     },
  // //   },
  // //   description: 'Subscription data',
  // // })
  // // @ApiParam({ name: 'id', required: true, type: Number })
  // // @ApiResponse({ status: 200, description: 'The subscription status has been successfully updated.' })
  // // updateSubscriptionStatus(@Param('id') id: string, @Body() updateSubscriptionData: CreateSubscriptionDto) {
  // //   try {
  // //     return this.subscriptionService.updateSubscriptionStatus(+id, updateSubscriptionData);
  // //   } catch (error) {
  // //     return {
  // //       success: false,
  // //       error: error.message || 'Internal server error',
  // //     };
  // //   }
  // // }

  // // @UseGuards(SuperAdminAuthGuard )
  // @ApiBearerAuth()
  // @Get('/search/subscription')
  // @ApiOperation({ summary: 'Subscription - Get All Subscription  data by Title' })
  // @ApiQuery({ name: 'title', required: false, type: String,example: 'Premium'})
  // @ApiQuery({ name: 'limit', required: false, type: Number,example:10 })
  // @ApiQuery({ name: 'page', required: false, type: Number,example:1 })
  // @ApiResponse({
  //   status: 200,
  //   content: {
  //     'application/json': {
  //       schema: {
  //         type: 'array',
  //         items: {
  //           type: 'object',
  //           properties: {
  //             id: { type: 'number' },
  //             title: { type: 'string' },
  //             package_type: { type: 'string' },
  //             price: { type: 'number' },
  //             discount: { type: 'number' },
  //             roles: { type: 'array' },
  //           },
  //         },
  //       },
  //       example: [
  //         {
  //           status: true,
  //           created_at: "2024-07-08T00:30:51.682Z",
  //           updated_at: "2024-07-08T00:30:51.682Z",
  //           id: 3,
  //           title: "Premium",
  //           package_type: "Monthly",
  //           price: 29.99,
  //           discount: 2,
  //           roles: Role.CONTRACTOR
  //         },
  //         {
  //           status: true,
  //           id: 1,
  //           title: "Premium Suscription",
  //           created_at: "2024-07-08T00:30:51.682Z",
  //           updated_at: "2024-07-08T00:30:51.682Z",
  //           package_type: "Monthly",
  //           price: 1000,
  //           discount: 2,
  //           roles: Role.ARCHITECT
  //         },
  //       ],
  //     },
  //   },
  // })
  // async search(
  //   @Query('title') title: string,
  //   @Query('limit') limit: number = 10,
  //   @Query('page') page: number = 1,
  // ) {
  //   try {
  //     const result = await this.subscriptionService.subscriptionSearch(title, limit, page);
  //     return {
  //       success: true,
  //       data: result,
  //     };
  //   } catch (error) {
  //     return {
  //       success: false,
  //       error: error.message || 'Internal server error',
  //     };
  //   }
  // }

  ///////////////// PACKAGE //////////////////////

  @UseGuards(AdminGuard)
  @ApiBearerAuth()
  @Post()
  @ApiOperation({
    summary: 'Subscription Package - Create a new subscription Package',
  })
  @ApiResponse({
    status: 200,
    description: 'The subscription package has been successfully created.',
  })
  createPackage(@Body() createPackageData: CreatePackageDto) {
    try {
      return this.subscriptionService.createPackage(createPackageData);
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Internal server error',
      };
    }
  }

  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @Get()
  @ApiOperation({
    summary: 'Subscription Package - Get all packages with pagination',
  })
  @ApiResponse({
    status: 200,
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'number' },
              title: { type: 'string' },
              package_type: { type: 'string' },
              price: { type: 'number' },
              discount: { type: 'number' },
              roles: { type: 'array' },
            },
          },
        },
        example: [
          {
            status: true,
            created_at: '2024-07-08T00:30:51.682Z',
            updated_at: '2024-07-08T00:30:51.682Z',
            id: 3,
            title: 'Premium',
            package_type: 'Monthly',
            price: 29.99,
            discount: 2,
            roles: ['role1'],
          },
          {
            status: true,
            id: 1,
            title: 'Premium Suscription',
            created_at: '2024-07-08T00:30:51.682Z',
            updated_at: '2024-07-08T00:30:51.682Z',
            package_type: 'Monthly',
            price: 1000,
            discount: 2,
            roles: ['role1'],
          },
        ],
      },
    },
  })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'type', required: false, enum: PackageType })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'role', required: false, type: String })
  findAllPackages(
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Query('type') type?: string,
    @Query('role') role?: string,
    @Query('search') search?: string,
  ) {
    try {
      const filters = {
        type,
        role,
        search,
      };
      return this.subscriptionService.findAllPackages(page, limit, filters);
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Internal server error',
      };
    }
  }

  @UseGuards(AdminGuard)
  @ApiBearerAuth()
  @Get(':id')
  @ApiOperation({ summary: 'Subscription Packages - Get a package by ID' })
  @ApiParam({ name: 'id', required: true, type: Number })
  @ApiResponse({
    status: 200,
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'number' },
              title: { type: 'string' },
              package_type: { type: 'string' },
              price: { type: 'number' },
              discount: { type: 'number' },
              roles: { type: 'array' },
            },
          },
        },
        example: {
          status: true,
          created_at: '2024-07-08T00:30:51.682Z',
          updated_at: '2024-07-08T00:30:51.682Z',
          id: 3,
          title: 'Premium',
          package_type: 'Monthly',
          price: 29.99,
          discount: 2,
          roles: ['role1'],
        },
      },
    },
  })
  findPackageById(@Param('id', ParseIntPipe) id: number) {
    try {
      return this.subscriptionService.findOnePackage(id);
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Internal server error',
      };
    }
  }

  @UseGuards(AdminGuard)
  @ApiBearerAuth()
  @Delete(':id')
  @ApiOperation({
    summary: 'Subscription Package - Delete a subscription package by ID',
  })
  @ApiParam({ name: 'id', required: true, type: Number })
  @ApiResponse({
    status: 200,
    description: 'The subscription package has been successfully deleted.',
  })
  removePackage(@Param('id', ParseIntPipe) id: number) {
    try {
      return this.subscriptionService.removePackage(id);
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Internal server error',
      };
    }
  }

  @UseGuards(AdminGuard)
  @ApiBearerAuth()
  @Patch(':id')
  @ApiOperation({
    summary: 'Subscription Package - Update a subscription package by ID',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        title: { type: 'string', example: 'gold' },
        packageType: {
          type: 'string',
          enum: ['Monthly', 'Quarterly', 'Yearly'],
          example: 'Yearly',
        },
        price: { type: 'number', example: 100 },
        discount: { type: 'number', example: 10 },
        role: { type: 'string', example: 'buyer' },
      },
    },
    description: 'Subscription Package data',
  })
  @ApiParam({ name: 'id', required: true, type: Number })
  @ApiResponse({
    status: 200,
    description: 'The subscription package has been successfully updated.',
  })
  updatePackage(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateSubscriptionData: CreatePackageDto,
  ) {
    try {
      return this.subscriptionService.UpdatePackageById(
        +id,
        updateSubscriptionData,
      );
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Internal server error',
      };
    }
  }

  @Patch(':packageId/status')
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  async updatePackageStatus(
    @Param('packageId', ParseIntPipe) packageId: number,
    @Body() updatePackageStatusDto: UpdatePackageStatusDto,
  ) {
    return await this.subscriptionService.updateStatus(
      packageId,
      updatePackageStatusDto,
    );
  }
}
