import { InjectModel } from '@nestjs/sequelize';
import * as createCsvWriter from 'csv-writer';
import * as XLSX from 'xlsx';
import { Injectable, Logger } from '@nestjs/common';
import * as path from 'path';
import * as os from 'os';
import { User } from '../admin/authentication/schemas/user.schema';

@Injectable()
export class ExportService {
  private readonly logger = new Logger(ExportService.name);
  constructor(
    @InjectModel(User)
    private readonly userModel: typeof User,
  ) {}

  private async generateCsv(): Promise<string> {
    const users = await this.userModel.findAll();

    // Use the system's temporary directory
    const tempDir = os.tmpdir();
    const filePath = path.join(tempDir, `users-${Date.now()}.csv`); // Use a timestamp to ensure a unique file name

    const csvWriter = createCsvWriter.createObjectCsvWriter({
      path: filePath,
      header: [
        { id: 'id', title: 'ID' },
        { id: 'firstName', title: 'First Name' },
        { id: 'lastName', title: 'Last Name' },
        { id: 'email', title: 'Email' },
        // Add other user fields as needed
      ],
    });

    await csvWriter.writeRecords(users.map((user) => user.toJSON()));
    this.logger.log(`CSV generated at ${filePath}`);
    return filePath;
  }

  private async generateExcel(): Promise<string> {
    const users = await this.userModel.findAll();

    // Use the system's temporary directory
    const tempDir = os.tmpdir();
    const filePath = path.join(tempDir, `users-${Date.now()}.xlsx`); // Use a timestamp to ensure a unique file name

    const ws = XLSX.utils.json_to_sheet(users.map((user) => user.toJSON()));
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Users');

    XLSX.writeFile(wb, filePath);
    this.logger.log(`Excel generated at ${filePath}`);
    return filePath;
  }

  async exportUsers(format: 'csv' | 'excel'): Promise<string> {
    if (format === 'csv') {
      return this.generateCsv();
    } else if (format === 'excel') {
      return this.generateExcel();
    } else {
      throw new Error('Unsupported format');
    }
  }
}
