import {
  Table,
  Column,
  <PERSON>,
  <PERSON>Key,
  BelongsTo,
  DataType,
  HasOne,
} from 'sequelize-typescript';
import { Order } from './order.schema';
import { UserSubscription } from './user-subscription.schema';

export enum TransactionStatus {
  SUCCESS = 'success',
  PENDING = 'pending',
  FAILED = 'failed',
}
@Table
export class Transaction extends Model<Transaction> {
  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  trackingId: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  bankReferenceId: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  transactionType: string;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  transactionData: object;

  @Column({
    type: DataType.ENUM,
    values: Object.values(TransactionStatus),
  })
  status: TransactionStatus;

  @Column({
    type: DataType.DECIMAL(10, 2),
  })
  discountAmount: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
  })
  totalAmount: number;

  @ForeignKey(() => Order)
  @Column
  orderId: number;

  @BelongsTo(() => Order)
  order: Order;

  @HasOne(() => UserSubscription)
  subscription: UserSubscription;
}
