import {
  Table,
  Column,
  Model,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { Job } from './job.schema';

@Table
export class BiddingDetail extends Model<BiddingDetail> {
  @Column
  minRange: number;

  @Column
  maxRange: number;

  @Column
  startDate: Date;

  @Column
  endDate: Date;

  @ForeignKey(() => Job)
  @Column
  jobId: number;

  @BelongsTo(() => Job)
  job: Job;
}
