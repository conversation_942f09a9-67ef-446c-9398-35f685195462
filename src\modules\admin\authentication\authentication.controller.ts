import {
  Body,
  Controller,
  Get,
  Post,
  Request,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { SuperAdminCreateDTO } from './dto/superAdmin.dto';
import { AuthenticationService } from './authentication.service';
import { LoginDTO } from './dto/login.dto';
import { AdminGuard } from 'src/guards/admin/admin.guard';
import { Permissions } from '../../../common/decorators/permissions.decorator';
import { JWTGuard } from 'src/guards/common/jwt.guard';

@ApiTags('Authorization')
@Controller('user')
export class AuthenticationController {
  constructor(private readonly authenticationService: AuthenticationService) {}

  @Post('/create-super-admin')
  @ApiOperation({
    summary: 'Create Super Admin',
  })
  async createSuperAdmin(@Body() dto: SuperAdminCreateDTO) {
    return this.authenticationService.createSuperAdminUser(dto);
  }

  @Get()
  @ApiOperation({
    summary: 'Get All Users',
  })
  @UseGuards(AdminGuard)
  @Permissions({ module: 'SubAdmin', actions: ['Add'] })
  @ApiBearerAuth()
  async getAllUser() {
    return this.authenticationService.getAll();
  }

  @Post('/login')
  @ApiOperation({
    summary: 'Admin Login',
  })
  async login(@Body() loginDto: LoginDTO) {
    return this.authenticationService.login(loginDto);
  }
  @Get('verify')
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  async verifyToken() {
    try {
      return {
        message: 'Authorized',
        statusCode: 200,
      };
    } catch (error) {
      console.error('Token verification error:', error);
      return { isValid: false };
    }
  }
}
