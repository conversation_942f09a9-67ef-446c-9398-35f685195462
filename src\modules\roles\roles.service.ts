import { Injectable, NotFoundException, HttpStatus } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { CustomException } from 'src/common/custom.exception';
import { ERROR_MESSAGES } from 'src/common/messages';
import { CreateRoleDto } from './dto/create-role.dto';
import { Role } from './schemas/role.schema';
import { UserRole } from './schemas/usersRole.schema';

@Injectable()
export class RoleService {
  constructor(
    @InjectModel(Role)
    private readonly roleRepository: typeof Role,
    @InjectModel(UserRole)
    private readonly userRoleRepository: typeof UserRole,
  ) {}

  async create(createRoleDto: CreateRoleDto) {
    const roleExist = await this.roleRepository.findOne({
      where: {
        roleName: createRoleDto.roleName,
      },
    });
    if (roleExist) {
      throw new CustomException(
        ERROR_MESSAGES.ROLE_ALREADY_EXISTS,
        HttpStatus.UNAUTHORIZED,
      );
    }
    const role = await this.roleRepository.create({
      roleName: createRoleDto.roleName,
    });
    return role;
  }

  async findAll(): Promise<Role[]> {
    return this.roleRepository.findAll();
  }

  async findOne(id: number): Promise<Role> {
    const role = await this.roleRepository.findByPk(id);
    if (!role) {
      throw new NotFoundException('Role not found');
    }
    return role;
  }

  async getRolesWithCount(
    query: any,
    page: number,
    limit: number,
  ): Promise<{ roles: Role[]; total: number }> {
    const offset = (page - 1) * limit;

    const { count, rows } = await this.roleRepository.findAndCountAll({
      where: query,
      order: [['createdAt', 'DESC']],
      attributes: ['id', 'roleName'],
      offset,
      limit,
    });

    return { roles: rows, total: count };
  }
}
