import {
  Table,
  Column,
  Model,
  <PERSON><PERSON><PERSON>,
  Updated<PERSON>t,
  BelongsToMany,
} from 'sequelize-typescript';
import { User } from 'src/modules/admin/authentication/schemas/user.schema';
import { UserSkills } from 'src/modules/user-management/schemas/user-skills.schema';

@Table
export class Skill extends Model<Skill> {
  @Column
  name: string;

  @BelongsToMany(() => User, () => UserSkills)
  users: User[];

  @CreatedAt
  @Column
  createdAt: Date;

  @UpdatedAt
  @Column
  updatedAt: Date;
}
