// src/connection/dto/send-connection.dto.ts

import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayUnique,
  IsArray,
  IsEmail,
  Length,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Max,
  ValidateNested,
  IsNumberString,
} from 'class-validator';

export class SendConnectionDto {
  @ApiProperty({ description: 'ID of the user sending the connection request' })
  requesterId: number;

  @ApiProperty({
    description: 'ID of the user receiving the connection request',
  })
  receiverId: number;
}

export class GetPendingConnectionsDto {
  @ApiProperty({ description: 'ID of the user pendingconnection request' })
  userId: number;
}
export class AcceptConnectionDto {
  @ApiProperty({ description: 'ID of the user update status' })
  requestId: number;

  @ApiProperty({ description: 'user update status' })
  @IsNotEmpty()
  @IsEnum(['accept', 'reject'])
  status: 'accept' | 'reject';
}
export class GetAcceptedConnectionsDto {
  @ApiProperty({ description: 'ID of the user Accepted request' })
  userId: number;
}
