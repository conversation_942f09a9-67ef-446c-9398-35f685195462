import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { User } from '../admin/authentication/schemas/user.schema';
import { ExportService } from './export.service';
import { ExportController } from './export.controller';

@Module({
  imports: [SequelizeModule.forFeature([User])],
  providers: [ExportService],
  controllers: [ExportController],
})
export class ExportModule {}
