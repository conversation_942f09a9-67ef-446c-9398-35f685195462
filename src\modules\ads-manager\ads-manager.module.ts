import { Modu<PERSON> } from '@nestjs/common';
import { AdsManagerService } from './ads-manager.service';
import { AdsManagerController } from './ads-manager.controller';

import { AdsManager } from './schemas/ads-manager.schema';
import { JwtService } from '@nestjs/jwt';
import { SequelizeModule } from '@nestjs/sequelize';
import { AdsDisplay } from './schemas/ads-display.schema';
import { AdsClick } from './schemas/ads-click.schema';
import { User } from '../admin/authentication/schemas/user.schema';

@Module({
  imports: [
    SequelizeModule.forFeature([AdsManager, AdsDisplay, AdsClick, User]),
  ],
  controllers: [AdsManagerController],
  providers: [AdsManagerService, JwtService],
  exports: [AdsManagerService, JwtService],
})
export class AdsManagerModule {}
