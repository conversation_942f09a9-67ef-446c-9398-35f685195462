import {
  Table,
  Column,
  Model,
  Foreign<PERSON>ey,
  BelongsTo,
  CreatedAt,
  UpdatedAt,
  DataType,
} from 'sequelize-typescript';
import { User } from 'src/modules/admin/authentication/schemas/user.schema';

@Table({ tableName: 'UserBlocks' })
export class UserBlock extends Model<UserBlock> {
  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  blockerId: number;

  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  blockedId: number;

  @BelongsTo(() => User, 'blockerId')
  blocker: User;

  @BelongsTo(() => User, 'blockedId')
  blockedUser: User;

  @CreatedAt
  @Column
  createdAt: Date;

  @UpdatedAt
  @Column
  updatedAt: Date;
}
