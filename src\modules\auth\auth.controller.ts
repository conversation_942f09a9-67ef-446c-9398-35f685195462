import { Body, Controller, Get, Post, Req, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AuthService } from './auth.service';
import { SocialLoginDTO } from './dto/user.dto';
import { CommonService } from 'src/common/common.service';
import { JWTGuard } from 'src/guards/common/jwt.guard';

@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly commonService: CommonService,
  ) {}

  @Post('/login/google')
  @ApiOperation({ summary: 'Google Authentication' })
  @ApiResponse({
    status: 200,
    description: 'Verify Google auth token and login user',
  })
  async googleLogin(@Body() loginDto: SocialLoginDTO) {
    const { token, ...data } = loginDto;
    const googleUser = await this.authService.validateGoogleToken(token);
    const user = await this.authService.validateGoogleUser(
      googleUser['sub'],
      data,
    );
    const accessToken = await this.commonService.generateToken(user.id);
    return { accessToken };
  }

  @Post('/login/facebook')
  @ApiOperation({ summary: 'Facebook Authentication' })
  @ApiResponse({
    status: 200,
    description: 'Verify facebook auth token and login user',
  })
  async facebookLogin(@Body() loginDto: SocialLoginDTO) {
    const { token, ...data } = loginDto;
    const fbUser = await this.authService.validateFacebookToken(token);
    const user = await this.authService.validateFacebookUser(
      fbUser['id'],
      data,
    );
    const accessToken = await this.commonService.generateToken(user.id);
    return { accessToken };
  }

  @Post('/login/linkedin')
  @ApiOperation({ summary: 'LinkedIn Authentication' })
  @ApiResponse({
    status: 200,
    description: 'Verify LinkedIn auth token and login user',
  })
  async linkedInLogin(@Body() loginDto: SocialLoginDTO) {
    const { ...data } = loginDto;
    const linkedInUser = await this.authService.validateLinkedInToken(
      loginDto.token,
    );
    const user = await this.authService.validateFacebookUser(
      linkedInUser['id'],
      data,
    );
    const accessToken = await this.commonService.generateToken(user.id);
    return { accessToken };
  }

  @Post('/login/twitter')
  @ApiOperation({ summary: 'Twitter Authentication' })
  @ApiResponse({
    status: 200,
    description: 'Verify Twitter auth token and login user',
  })
  async twitterLogin(@Body() loginDto: SocialLoginDTO) {
    const { ...data } = loginDto;
    const twitterUser = await this.authService.validateTwitterInToken(
      loginDto.token,
    );
    const user = await this.authService.validateTwitterUser(
      twitterUser['id'],
      data,
    );
    const accessToken = await this.commonService.generateToken(user.id);
    return { accessToken };
  }

  @Get('/profile')
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get Profile details' })
  async getUserProfileDetails(@Req() req: any) {
    const { user } = req;
    const { firstName, lastName, profilePicture, role, permissions } = user;
    return {
      firstName,
      lastName,
      profilePicture,
      role,
      permissions,
    };
  }
}
