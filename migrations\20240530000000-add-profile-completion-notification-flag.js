'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    /**
     * Add the profileCompletionNotificationSent column to the Users table
     * This flag tracks whether a profile completion notification has been sent to the user
     */
    await queryInterface.addColumn('Users', 'profileCompletionNotificationSent', {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    });
  },

  async down(queryInterface, Sequelize) {
    /**
     * Remove the profileCompletionNotificationSent column from the Users table
     */
    await queryInterface.removeColumn('Users', 'profileCompletionNotificationSent');
  }
};
