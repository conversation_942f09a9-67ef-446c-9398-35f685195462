'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
 
     await queryInterface.addColumn('RequestQuotations', 'status', {
      type: Sequelize.ENUM(
        'requested',
        'received',
        'accepted',
        'rejected'
      ),
      allowNull: false,
      defaultValue: 'requested', 
    });
  },

  async down (queryInterface, Sequelize) {
    // await queryInterface.removeColumn('jobs', 'status');
  }
};
