// upload.dto.ts

import { ApiProperty } from '@nestjs/swagger';

export class UploadDTO {
  @ApiProperty({ type: 'string', format: 'binary' })
  file: any;
}

export class UploadResponse {
  message: string;
  base_url?: string;
  file_name?: string;
}
export class UploadsDTO {
  @ApiProperty({ type: 'array', items: { type: 'string', format: 'binary' } })
  files: any[];
}

export class UploadsResponse {
  message: string;
  files?: Array<{
    key: string;
    fileUrl: string;
  }>;
}
