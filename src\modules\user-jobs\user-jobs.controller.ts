import {
  Body,
  Controller,
  Param,
  ParseInt<PERSON>ipe,
  Post,
  UseGuards,
  Request,
  Get,
  Query,
  Patch,
  NotFoundException,
  HttpStatus,
  HttpException,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { Op } from 'sequelize';
import { UserJobsService } from './user-jobs.service';
import { UserJobStatus } from './utils/enum';
import { UserJobs } from './schemas/user-jobs.schema';
import { JWTGuard } from 'src/guards/common/jwt.guard';
import { Job } from 'src/modules/job/schemas/job.schema';
import { PatchUpdateStatusDto } from './dto/patch-update-status.dto';
import { JobRequestDto } from './dto/create-job-request.dto';
import { User } from '../admin/authentication/schemas/user.schema';
import { JobsService } from '../job/job.service';
import { JobStatus } from 'src/common/utils/enum';
import { literal } from 'sequelize';
import sequelize from 'sequelize';
import { Role } from '../roles/schemas/role.schema';
import { InjectModel } from '@nestjs/sequelize';
import { UserJobBids } from '../user-job-bids/schemas/user-job-bids.schema';
import { UserJobBidStatus } from '../user-job-bids/utills/enum';
import { ReviewRating } from '../review-rating/schemas/review-rating.schema';
import { BiddingDetail } from '../job/schemas/bidding-details.schema';
import { Category } from '../ category/schemas/category.schema';
import { Notification } from '../notification/schema/notification.schema';

@ApiTags('User-jobs')
@Controller('user-jobs')
export class UserJobController {
  constructor(
    private readonly userJobService: UserJobsService,
    private jobService: JobsService,
    @InjectModel(Job)
    private jobModel: typeof Job,
    @InjectModel(UserJobBids)
    private userJobsBidModel: typeof UserJobBids,
    @InjectModel(Notification)
    private notificationModel: typeof Notification,
  ) {}

  @Post('/:userId/request')
  @ApiOperation({ summary: 'Send a job request' })
  @ApiBearerAuth()
  @UseGuards(JWTGuard)
  async sendBidInvite(
    @Body() jobRequestDto: JobRequestDto,
    @Param('userId', ParseIntPipe) userId: number,
  ): Promise<UserJobs[]> {
    const getAllUserJobs = await this.userJobService.getAll({
      where: { userId, jobId: jobRequestDto.jobIds },
      attributes: ['jobId'],
    });
    const userJobsIds = getAllUserJobs.map((userJob) => userJob.jobId);
    if (userJobsIds.length) {
      if (userJobsIds.length === jobRequestDto.jobIds.length) {
        throw new HttpException(
          'User is already associated with selected jobs',
          HttpStatus.CONFLICT,
        );
      }
      jobRequestDto.jobIds = jobRequestDto.jobIds.filter(
        (jobId) => !userJobsIds.includes(jobId),
      );
    }
    const payload = jobRequestDto.jobIds.map((jobId) => {
      return {
        userId,
        jobId,
        status: UserJobStatus.REQUESTED,
      };
    });
    return await this.userJobService.bulkCreate(payload);
  }
  @Post('/:jobId/interested')
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'mark job status intrested' })
  @ApiResponse({
    status: 200,
    description: 'job status updated',
  })
  async markUnmarkInterested(
    @Request() req,
    @Param('jobId', ParseIntPipe) jobId: number,
  ): Promise<any> {
    const userId = req.user.id;

    const getJob = await this.jobService.getOne({
      where: { id: jobId, status: JobStatus.PUBLISHED },
    });

    if (!getJob) {
      throw new NotFoundException('Job does not exist');
    }

    const getInterestedJob = await this.userJobService.getOne({
      where: {
        status: UserJobStatus.INTERESTED,
        userId,
        jobId,
      },
    });

    if (getInterestedJob) {
      await getInterestedJob.destroy();
    } else {
      const payload = {
        jobId,
        userId,
        isInterested: true,
        status: UserJobStatus.INTERESTED,
      };
      await this.userJobService.create(payload);
    }
    const startOfMonth = new Date(
      new Date().getFullYear(),
      new Date().getMonth(),
      1,
    );
    const endOfMonth = new Date(
      new Date().getFullYear(),
      new Date().getMonth() + 1,
      0,
      23,
      59,
      59,
      999,
    );
    // Count total interested jobs for the user
    const interestedJobCount = await this.userJobService.count({
      where: {
        userId,
        // status: UserJobStatus.INTERESTED,
        isInterested: true,
        createdAt: {
          [Op.between]: [startOfMonth, endOfMonth],
        },
      },
    });

    return {
      message: getInterestedJob
        ? 'Job marked as uninterested'
        : 'Job marked as interested',
      interestedJobCount,
    };
  }

  @Patch('/:id/request')
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'accept reject job request' })
  @ApiResponse({
    status: 200,
    description: 'user job status updated',
  })
  async patchJobRequest(
    @Request() req,
    @Body() payload: PatchUpdateStatusDto,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<UserJobs> {
    const getUserJob = await this.userJobService.getOne({
      where: {
        userId: req.user.id,
        id: id,
        status: UserJobStatus.REQUESTED,
      },
      include: [
        {
          model: Job,
          as: 'job',
          attributes: ['id', 'userId'],
          required: true,
        },
      ],
    });
    if (!getUserJob) {
      throw new NotFoundException('job request does not exist');
    }
    return this.userJobService.patchUpdateStatus(
      getUserJob,
      payload.status,
      req.user.id,
    );
  }

  @Get()
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user jobs list of logged in user' })
  @ApiQuery({ name: 'limit', required: false, type: String })
  @ApiQuery({ name: 'page', required: false, type: String })
  @ApiQuery({
    name: 'by',
    required: true,
    type: String,
    enum: ['labour', 'jobCreator'],
  })
  @ApiQuery({
    name: 'status',
    required: false,
    type: String,
    enum: [
      UserJobStatus.REQUESTED,
      UserJobStatus.INTERESTED,
      UserJobStatus.ACCEPTED,
      UserJobStatus.REJECTED,
    ],
  })
  @ApiResponse({
    status: 200,
    description: 'Return a list of all jobs.',
  })
  async getAllJobs(
    @Request() req,
    @Query('page') page = 1,
    @Query('limit') limit = 100,
    @Query('status') status = null,
    @Query('by') by = null,
  ): Promise<{ userJobs: any[]; total: number }> {
    let query: any = {
      where: { userId: req.user.id, ...(status && { status: status }) },
      include: [
        {
          model: Job,
          as: 'job',
          attributes: [
            'id',
            'title',
            'description',
            'radius',
            'images',
            'geolocation',
            'city',
            'state',
            'pincode',
            'createdAt',
          ],
        },
        {
          model: User,
          as: 'acceptedBy',
          attributes: [
            'id',
            'firstName',
            'lastName',
            'email',
            'phoneNumber',
            'profilePicture',
            [
              sequelize.literal(`(
                SELECT CAST(ROUND(AVG(rating) * 2) / 2 AS DECIMAL(2,1))
                FROM "ReviewRatings" AS rr
                WHERE rr."revieweeId" = "acceptedBy"."id"
              )`),
              'averageRating',
            ],
          ],
        },
      ],
    };
    let query2: any = {
      where: { userId: req.user.id, ...(status && { status: status }) },
      include: [
        {
          model: Job,
          as: 'job',
          attributes: [
            'id',
            'title',
            'description',
            'radius',
            'images',
            'geolocation',
            'city',
            'state',
            'pincode',
            'createdAt',
          ],
        },
        {
          model: User,
          as: 'jobOwner',
          attributes: [
            'id',
            'firstName',
            'lastName',
            'email',
            'phoneNumber',
            'profilePicture',
            [
              sequelize.literal(`(
                SELECT CAST(ROUND(AVG(rating) * 2) / 2 AS DECIMAL(2,1))
                FROM "ReviewRatings" AS rr
                WHERE rr."revieweeId" = "jobOwner"."id"
              )`),
              'averageRating',
            ],
          ],
        },
      ],
    };
    if (by === 'jobCreator') {
      query = {
        where: {
          ...(status && {
            status: status,
          }),
        },
        include: [
          {
            model: Job,
            as: 'job',
            where: {
              userId: req.user.id,
            },
            include: [
              {
                model: Role,
                as: 'lookingFor',
                where: {
                  roleName: 'labour',
                },
                attributes: ['id', 'roleName'],
              },
            ],
          },
          {
            model: User,
            as: 'acceptedBy',
            attributes: [
              'id',
              'firstName',
              'lastName',
              'email',
              'phoneNumber',
              'profilePicture',
              [
                sequelize.literal(`(
                SELECT CAST(ROUND(AVG(rating) * 2) / 2 AS DECIMAL(2,1))
                FROM "ReviewRatings" AS rr
                WHERE rr."revieweeId" = "acceptedBy"."id"
              )`),
                'averageRating',
              ],
            ],
          },
          {
            model: User,
            as: 'user',
            attributes: [
              'id',
              'firstName',
              'lastName',
              'email',
              'phoneNumber',
              'profilePicture',
              [
                sequelize.literal(`(
                SELECT CAST(ROUND(AVG(rating) * 2) / 2 AS DECIMAL(2,1))
                FROM "ReviewRatings" AS rr
                WHERE rr."revieweeId" = "user"."id"
              )`),
                'averageRating',
              ],
            ],
          },
        ],
      };
      query2 = {
        where: {
          ...(status && {
            status: UserJobBidStatus.ACCEPTED.toLowerCase(),
            jobOwnerId: req.user.id,
          }),
        },
        include: [
          {
            model: Job,
            as: 'job',
            where: {
              userId: req.user.id,
            },
            attributes: ['id', 'title'],
          },
          {
            model: User,
            as: 'user',
            attributes: [
              'id',
              'firstName',
              'lastName',
              'email',
              'phoneNumber',
              'profilePicture',
              [
                sequelize.literal(`(
                SELECT CAST(ROUND(AVG(rating) * 2) / 2 AS DECIMAL(2,1))
                FROM "ReviewRatings" AS rr
                WHERE rr."revieweeId" = "user"."id"
              )`),
                'averageRating',
              ],
            ],
          },
        ],
      };
    }
    query = {
      ...query,
      attributes: ['id', 'status', 'createdAt'],
      order: [['createdAt', 'DESC']],
      offset: (page - 1) * limit,
      limit,
    };
    const userJobByLobour = await this.userJobService.getAllWithCount(query);
    let result;
    if (by === 'jobCreator') {
      // const fromJobBid = await this.userJobsBidModel.findAndCountAll(query2);
      const userJobs = [...userJobByLobour.userJobs];
      const sortedUserJobs = userJobs.sort((a, b) => {
        return (
          new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
        );
      });
      result = {
        userJobs: userJobs,
        total: userJobByLobour.total || 0,
      };
    } else {
      result = {
        userJobs: [...userJobByLobour.userJobs],
        total: userJobByLobour.total || 0,
      };
    }
    if (by === 'labour') {
      const getCount = await this.userJobService.getAll({
        attributes: [
          [
            literal(
              `COUNT(CASE WHEN status = '${UserJobStatus.INTERESTED}' THEN 1 ELSE NULL END)`,
            ),
            'interestedJobsCount',
          ],
          [
            literal(
              `COUNT(CASE WHEN status = '${UserJobStatus.REQUESTED}' THEN 1 ELSE NULL END)`,
            ),
            'requestedJobsCount',
          ],
          [
            literal(
              `COUNT(CASE WHEN status = '${UserJobStatus.ACCEPTED}' THEN 1 ELSE NULL END)`,
            ),
            'acceptedJobsCount',
          ],
          [
            literal(
              `COUNT(CASE WHEN status = '${UserJobStatus.REJECTED}' THEN 1 ELSE NULL END)`,
            ),
            'rejectedJobsCount',
          ],
        ],
        where: { userId: req.user.id },
        raw: true,
      });
      const res = {
        ...result,
        ...getCount[0],
      };
      return res;
    }
    return result;
  }

  @Get('/by-job/:jobId')
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get list of users by jobs' })
  @ApiQuery({ name: 'limit', required: false, type: String })
  @ApiQuery({ name: 'page', required: false, type: String })
  @ApiQuery({
    name: 'status',
    required: false,
    type: String,
    description: `please enter status as ${UserJobStatus.REQUESTED}, ${UserJobStatus.INTERESTED},${UserJobStatus.ACCEPTED},${UserJobStatus.REJECTED}`,
  })
  async getAllUserJobsStatus(
    @Request() req,
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Query('status') status = null,
    @Param('jobId', ParseIntPipe) jobId: number,
  ): Promise<{
    userJobs: UserJobs[];
    status: Record<string, number>;
  }> {
    const query = {
      where: {
        jobId: jobId,
        ...(status && status === UserJobStatus.REJECTED
          ? { status: [UserJobStatus.REJECTED, UserJobStatus.CLOSED] }
          : { status: status }),
      },
      include: [
        {
          model: User,
          as: 'user',
          include: [
            {
              model: Role,
              as: 'roles',
              attributes: ['id', 'roleName'],
              through: { attributes: [] },
            },
            {
              model: Category,
              through: {
                attributes: [],
              },
              as: 'categories',
              attributes: ['id', 'name'],
            },
            {
              model: Category,
              through: {
                attributes: [],
              },
              as: 'subCategories',
              attributes: ['id', 'name', 'parentId'],
            },
          ],
          attributes: [
            'id',
            'firstName',
            'lastName',
            'email',
            'role',
            'phoneNumber',
            'profilePicture',
            'ratePerDay',
            'bio',
          ],
        },
        {
          model: Job,
          as: 'job',
          include: [
            {
              model: Category,
              as: 'criteria',
              attributes: ['id', 'name', 'parentId'],
            },
            {
              model: Category,
              as: 'subCategory',
              attributes: ['id', 'name', 'parentId'],
            },
          ],
          required: true,
        },
      ],
      attributes: ['id', 'status', 'createdAt'],
      order: [['createdAt', 'DESC']],
      offset: (page - 1) * limit,
      limit,
      distinct: true,
      logging: true,
    };

    const { userJobs } = await this.userJobService.getAllWithCount(query);

    const userJobsStatus = await this.userJobService.getAllWithStatus({
      where: { jobId },
      attributes: [
        'status',
        [sequelize.fn('COUNT', sequelize.col('status')), 'count'],
      ],
      group: ['status'],
      raw: true,
    });

    const statusCounts = userJobsStatus.reduce(
      (acc, item) => {
        const status = item.status;

        if (
          status === UserJobStatus.REJECTED ||
          status === UserJobStatus.CLOSED
        ) {
          acc[UserJobStatus.REJECTED] =
            (acc[UserJobStatus.REJECTED] || 0) + parseInt(item.count, 10);
        } else {
          acc[status] = parseInt(item.count, 10);
        }

        return acc;
      },
      {} as Record<string, number>,
    );

    return { userJobs, status: statusCounts };
  }

  @Patch('/:id/interest')
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'accept reject job interest' })
  @ApiResponse({
    status: 200,
    description: 'user job status updated',
  })
  async patchJobInterest(
    @Request() req,
    @Body() payload: PatchUpdateStatusDto,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<UserJobs> {
    console.log('id:::', id);
    const getUserJob = await this.userJobService.getOne({
      where: {
        id: id,
        status: UserJobStatus.INTERESTED,
      },
      include: [
        {
          model: Job,
          as: 'job',
          where: {
            userId: req.user.id,
          },
          attributes: ['id', 'userId'],
          required: true,
        },
      ],
    });
    if (!getUserJob) {
      throw new NotFoundException('job interest does not exist');
    }
    return this.userJobService.patchUpdateStatus(
      getUserJob,
      payload.status,
      req.user.id,
    );
  }

  @Get('/:id')
  @UseGuards(JWTGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'get  userjob by id' })
  async getUserJobById(
    @Request() req,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<UserJobs> {
    const query = {
      where: {
        id,
      },
      include: [
        {
          model: User,
          as: 'user',
          include: [
            {
              model: Role,
              as: 'roles',
              attributes: ['id', 'roleName'],
              through: { attributes: [] },
            },
            {
              model: Category,
              through: {
                attributes: [],
              },
              as: 'categories',
              attributes: ['id', 'name'],
            },
            {
              model: Category,
              through: {
                attributes: [],
              },
              as: 'subCategories',
              attributes: ['id', 'name', 'parentId'],
            },
          ],
          attributes: [
            'id',
            'firstName',
            'lastName',
            'email',
            'role',
            'phoneNumber',
            'profilePicture',
            'ratePerDay',
            'bio',
            'secondaryPhoneNumber',
            'officeNumber',
            'state',
            'city',
            'pincode',
            [
              sequelize.literal(`(
                SELECT CAST(ROUND(AVG(rating) * 2) / 2 AS DECIMAL(2,1))
                FROM "ReviewRatings" AS rr
                WHERE rr."revieweeId" = "user"."id"
              )`),
              'averageRating',
            ],
          ],
        },
        {
          model: Job,
          as: 'job',
          include: [
            {
              model: Category,
              as: 'criteria',
              attributes: ['id', 'name', 'parentId'],
            },
            {
              model: Category,
              as: 'subCategory',
              attributes: ['id', 'name', 'parentId'],
            },
            {
              model: BiddingDetail,
              as: 'biddingDetail',
              attributes: [
                'id',
                'minRange',
                'maxRange',
                'startDate',
                'endDate',
              ],
            },
            {
              model: ReviewRating,
              as: 'reviewRating',
              attributes: ['review', 'rating'],
            },
            {
              model: User,
              as: 'createdBy',
              attributes: [
                'id',
                'firstName',
                'lastName',
                'email',
                'phoneNumber',
                'profilePicture',
                [
                  sequelize.literal(`(
                SELECT CAST(ROUND(AVG(rating) * 2) / 2 AS DECIMAL(2,1))
                FROM "ReviewRatings" AS rr
                WHERE rr."revieweeId" = "user"."id"
              )`),
                  'averageRating',
                ],
              ],
              include: [
                {
                  model: Role,
                  as: 'roles',
                  attributes: ['id', 'roleName'],
                  through: { attributes: [] },
                },
                {
                  model: Category,
                  through: {
                    attributes: [],
                  },
                  as: 'categories',
                  attributes: ['id', 'name'],
                },
                {
                  model: Category,
                  through: {
                    attributes: [],
                  },
                  as: 'subCategories',
                  attributes: ['id', 'name', 'parentId'],
                },
              ],
            },
          ],
          required: true,
        },
      ],
      attributes: ['id', 'status', 'createdAt'],
      order: [['createdAt', 'DESC']],
      distinct: true,
    };
    const getUserJob = await this.userJobService.getOne(query);
    const hasNotification = await this.notificationModel.findOne({
      where: {
        receiverId: getUserJob.job.createdBy.id,
        type: 'review_requested',
        additional: {
          jobId: getUserJob.job.id,
        },
      },
    });
    let notificationExists = false;
    if (hasNotification) {
      notificationExists = true;
    }
    const result = getUserJob.toJSON() as any;
    result.hasNotification = notificationExists;
    return result;
  }
}
