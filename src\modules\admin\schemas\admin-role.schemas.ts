import { Table, Model, Column, HasMany, DataType } from 'sequelize-typescript';
import { User } from '../authentication/schemas/user.schema';

@Table
export class AdminRole extends Model {
  @Column({ allowNull: true })
  role_name: string;

  @Column({ allowNull: true, type: DataType.TEXT })
  description: string;

  @Column({ defaultValue: false })
  admins: boolean;

  @Column({ defaultValue: false })
  user_management: boolean;

  @Column({ defaultValue: false })
  dashboard: boolean;

  @Column({ defaultValue: false })
  content_management: boolean;

  @Column({ defaultValue: false })
  ads_manager: boolean;

  @Column({ defaultValue: false })
  subscription_management: boolean;

  @HasMany(() => User)
  users: User[];
}
